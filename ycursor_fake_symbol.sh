#!/bin/bash

# YCursor 假符号修复脚本
# 尝试通过提供假的 CoreText 符号来绕过兼容性问题

echo "=== YCursor 假符号修复尝试 ==="

# 获取当前目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 设置环境变量以预加载我们的假库
export DYLD_INSERT_LIBRARIES="$SCRIPT_DIR/libfake_coretext.dylib"
export DYLD_FORCE_FLAT_NAMESPACE=1

# 设置其他兼容性环境变量
export QTWEBENGINE_DISABLE_SANDBOX=1
export QT_WEBENGINE_DISABLE_GPU=1
export QTWEBENGINE_REMOTE_DEBUGGING=0
export QT_QUICK_BACKEND=software

echo "已设置假符号库: $DYLD_INSERT_LIBRARIES"
echo "正在启动 YCursor..."
echo ""

# 启动应用
exec /Applications/YCursor.app/Contents/MacOS/YCursor "$@"
