#!/bin/bash

# YCursor 简单修复脚本（无需 sudo）
# 用于解决 macOS 12.x 上的兼容性问题

echo "=== YCursor 兼容性修复脚本 ==="
echo "正在设置兼容性环境..."

# 设置环境变量以解决 Qt WebEngine 兼容性问题
export QTWEBENGINE_DISABLE_SANDBOX=1
export QT_WEBENGINE_DISABLE_GPU=1
export QTWEBENGINE_REMOTE_DEBUGGING=0

# 设置 Chromium 标志以禁用可能导致崩溃的功能
export QTWEBENGINE_CHROMIUM_FLAGS="--disable-gpu --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI --disable-ipc-flooding-protection --no-sandbox --disable-dev-shm-usage --disable-web-security --disable-features=VizDisplayCompositor --disable-gpu-sandbox --disable-software-rasterizer --disable-background-timer-throttling"

# 设置库路径以尝试解决 CoreText 符号问题
export DYLD_FALLBACK_LIBRARY_PATH="/System/Library/Frameworks/CoreText.framework/Versions/A:/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/CoreText.framework/Versions/A:$DYLD_FALLBACK_LIBRARY_PATH"

# 设置 Qt 相关环境变量
export QT_MAC_WANTS_LAYER=1
export QT_AUTO_SCREEN_SCALE_FACTOR=0

# 尝试使用不同的渲染后端
export QT_QUICK_BACKEND=software

echo "环境变量设置完成"
echo ""
echo "正在启动 YCursor..."
echo "注意: 由于 macOS 12.x 与 Qt WebEngine 6.9 的兼容性问题，"
echo "强烈建议升级 macOS 到 13.0 或更高版本以获得最佳体验"
echo ""

# 启动应用
exec /Applications/YCursor.app/Contents/MacOS/YCursor "$@"
