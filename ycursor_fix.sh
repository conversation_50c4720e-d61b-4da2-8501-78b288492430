#!/bin/bash

# YCursor 兼容性修复脚本
# 用于解决 macOS 12.x 上的 Qt WebEngine 兼容性问题

echo "正在尝试修复 YCursor 兼容性问题..."

# 确保日志目录存在并有正确权限
LOG_DIR="$HOME/Library/Application Support/YCursor/logs"
mkdir -p "$LOG_DIR"
chmod 755 "$LOG_DIR"

# 方法1: 设置环境变量禁用硬件加速和沙盒
export QTWEBENGINE_DISABLE_SANDBOX=1
export QTWEBENGINE_CHROMIUM_FLAGS="--disable-gpu --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI --disable-ipc-flooding-protection --no-sandbox --disable-dev-shm-usage"

# 方法2: 设置库路径
export DYLD_FALLBACK_LIBRARY_PATH="/System/Library/Frameworks/CoreText.framework/Versions/A:/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/CoreText.framework/Versions/A:$DYLD_FALLBACK_LIBRARY_PATH"

# 方法3: 禁用 WebEngine 相关功能
export QT_WEBENGINE_DISABLE_GPU=1
export QTWEBENGINE_REMOTE_DEBUGGING=0

echo "环境变量已设置，正在启动 YCursor..."
echo "如果仍然出现错误，请考虑升级 macOS 到 13.0 或更高版本"

# 启动应用
/Applications/YCursor.app/Contents/MacOS/YCursor "$@"
