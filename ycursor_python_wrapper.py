#!/usr/bin/env python3
"""
YCursor Python 包装器
尝试通过修改 Python 导入行为来绕过 WebEngine 兼容性问题
"""

import sys
import os
import importlib.util
from unittest.mock import MagicMock

# 添加 YCursor 的路径到 Python 路径
ycursor_path = "/Applications/YCursor.app/Contents/MacOS"
if ycursor_path not in sys.path:
    sys.path.insert(0, ycursor_path)

# 创建一个模拟的 QtWebEngineWidgets 模块
class MockQtWebEngineWidgets:
    """模拟的 QtWebEngineWidgets 模块"""
    
    class QWebEngineView:
        def __init__(self, *args, **kwargs):
            print("Warning: Using mock QWebEngineView")
            pass
        
        def load(self, *args, **kwargs):
            print("Warning: Mock QWebEngineView.load() called")
            pass
        
        def setHtml(self, *args, **kwargs):
            print("Warning: Mock QWebEngineView.setHtml() called")
            pass
    
    class QWebEnginePage:
        def __init__(self, *args, **kwargs):
            print("Warning: Using mock QWebEnginePage")
            pass

# 创建模拟模块
mock_webengine = MockQtWebEngineWidgets()

# 在导入之前注入模拟模块
sys.modules['PySide6.QtWebEngineWidgets'] = mock_webengine

# 设置环境变量
os.environ['QTWEBENGINE_DISABLE_SANDBOX'] = '1'
os.environ['QT_WEBENGINE_DISABLE_GPU'] = '1'
os.environ['QTWEBENGINE_REMOTE_DEBUGGING'] = '0'
os.environ['QT_QUICK_BACKEND'] = 'software'

print("=== YCursor Python 包装器 ===")
print("已注入模拟的 QtWebEngineWidgets 模块")
print("正在启动 YCursor...")

try:
    # 尝试导入并运行 YCursor
    import YCursor
    print("YCursor 模块导入成功")
except ImportError as e:
    print(f"导入 YCursor 失败: {e}")
    # 尝试直接执行 YCursor.py
    try:
        ycursor_py = os.path.join(ycursor_path, "YCursor.py")
        if os.path.exists(ycursor_py):
            print(f"尝试直接执行 {ycursor_py}")
            exec(open(ycursor_py).read())
        else:
            print("找不到 YCursor.py 文件")
    except Exception as e:
        print(f"执行 YCursor.py 失败: {e}")
except Exception as e:
    print(f"运行 YCursor 时出错: {e}")
    import traceback
    traceback.print_exc()
