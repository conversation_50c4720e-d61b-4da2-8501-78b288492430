#include <CoreFoundation/CoreFoundation.h>
#include <stdio.h>

// 创建一个更完整的 CoreText 符号垫片
// 这个函数应该返回一个 CFArrayRef，但我们返回 NULL

CFArrayRef _CTFontCopyVariationAxesInternal(void* font) {
    printf("Warning: Using shim for _CTFontCopyVariationAxesInternal\n");
    // 返回一个空数组而不是 NULL，这样可能更安全
    return CFArrayCreate(NULL, NULL, 0, &kCFTypeArrayCallBacks);
}

// 导出符号
__attribute__((visibility("default"))) CFArrayRef _CTFontCopyVariationAxesInternal(void* font);
