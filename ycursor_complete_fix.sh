#!/bin/bash

# YCursor 完整修复脚本
# 用于解决 macOS 12.x 上的兼容性和权限问题

echo "=== YCursor 兼容性修复脚本 ==="
echo "正在诊断和修复问题..."

# 获取当前用户
CURRENT_USER=$(whoami)
echo "当前用户: $CURRENT_USER"

# 修复权限问题
echo "正在修复权限问题..."
YCURSOR_DIR="$HOME/Library/Application Support/YCursor"
LOG_DIR="$YCURSOR_DIR/logs"

# 删除可能存在的 root 拥有的目录
if [ -d "$YCURSOR_DIR" ]; then
    echo "发现现有 YCursor 目录，正在修复权限..."
    sudo rm -rf "$YCURSOR_DIR" 2>/dev/null || true
fi

# 重新创建目录并设置正确权限
mkdir -p "$LOG_DIR"
chmod 755 "$YCURSOR_DIR"
chmod 755 "$LOG_DIR"

echo "权限修复完成"

# 设置环境变量以解决 Qt WebEngine 兼容性问题
echo "正在设置兼容性环境变量..."

# 禁用沙盒和硬件加速
export QTWEBENGINE_DISABLE_SANDBOX=1
export QT_WEBENGINE_DISABLE_GPU=1
export QTWEBENGINE_REMOTE_DEBUGGING=0

# 设置 Chromium 标志
export QTWEBENGINE_CHROMIUM_FLAGS="--disable-gpu --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI --disable-ipc-flooding-protection --no-sandbox --disable-dev-shm-usage --disable-web-security --disable-features=VizDisplayCompositor"

# 设置库路径
export DYLD_FALLBACK_LIBRARY_PATH="/System/Library/Frameworks/CoreText.framework/Versions/A:/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/CoreText.framework/Versions/A:$DYLD_FALLBACK_LIBRARY_PATH"

# 设置 Qt 相关环境变量
export QT_MAC_WANTS_LAYER=1
export QT_AUTO_SCREEN_SCALE_FACTOR=0

echo "环境变量设置完成"
echo ""
echo "正在启动 YCursor..."
echo "注意: 如果仍然出现 CoreText 相关错误，强烈建议升级 macOS 到 13.0 或更高版本"
echo ""

# 启动应用
exec /Applications/YCursor.app/Contents/MacOS/YCursor "$@"
