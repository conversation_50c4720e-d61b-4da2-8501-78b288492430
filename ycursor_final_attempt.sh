#!/bin/bash

# YCursor 最终尝试脚本
# 使用完整的 CoreText 符号垫片

echo "=== YCursor 最终修复尝试 ==="
echo "使用完整的 CoreText 符号垫片..."

# 获取当前目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 设置环境变量
export DYLD_INSERT_LIBRARIES="$SCRIPT_DIR/libcomplete_coretext_shim.dylib"
export DYLD_FORCE_FLAT_NAMESPACE=1

# 禁用 SIP 相关的安全检查（如果可能）
export DYLD_LIBRARY_PATH="$SCRIPT_DIR:$DYLD_LIBRARY_PATH"

# 设置所有可能的兼容性环境变量
export QTWEBENGINE_DISABLE_SANDBOX=1
export QT_WEBENGINE_DISABLE_GPU=1
export QTWEBENGINE_REMOTE_DEBUGGING=0
export QT_QUICK_BACKEND=software
export QT_OPENGL=software
export LIBGL_ALWAYS_SOFTWARE=1

# 设置 Chromium 标志
export QTWEBENGINE_CHROMIUM_FLAGS="--disable-gpu --disable-gpu-compositing --disable-gpu-rasterization --disable-gpu-sandbox --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI,VizDisplayCompositor --disable-ipc-flooding-protection --no-sandbox --disable-dev-shm-usage --disable-web-security --single-process --no-zygote"

echo "环境设置完成"
echo "垫片库: $DYLD_INSERT_LIBRARIES"
echo ""
echo "正在启动 YCursor..."
echo "如果这次还是失败，那么只能升级系统或联系开发者了"
echo ""

# 启动应用
exec /Applications/YCursor.app/Contents/MacOS/YCursor "$@"
