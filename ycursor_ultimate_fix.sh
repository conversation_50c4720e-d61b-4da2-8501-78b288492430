#!/bin/bash

# YCursor 终极修复脚本
# 尝试多种方法绕过 Qt WebEngine 兼容性问题

echo "=== YCursor 终极兼容性修复 ==="
echo "正在尝试多种兼容性修复方法..."

# 设置最激进的兼容性环境变量
export QTWEBENGINE_DISABLE_SANDBOX=1
export QT_WEBENGINE_DISABLE_GPU=1
export QTWEBENGINE_REMOTE_DEBUGGING=0

# 强制使用软件渲染
export QT_QUICK_BACKEND=software
export QT_OPENGL=software
export LIBGL_ALWAYS_SOFTWARE=1

# 禁用所有可能导致问题的 Chromium 功能
export QTWEBENGINE_CHROMIUM_FLAGS="--disable-gpu --disable-gpu-compositing --disable-gpu-rasterization --disable-gpu-sandbox --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI,VizDisplayCompositor,VizHitTestSurfaceLayer,VizSurfaceLayer --disable-ipc-flooding-protection --no-sandbox --disable-dev-shm-usage --disable-web-security --disable-extensions --disable-plugins --disable-images --disable-javascript --single-process --no-zygote --disable-accelerated-2d-canvas --disable-accelerated-jpeg-decoding --disable-accelerated-mjpeg-decode --disable-accelerated-video-decode --disable-accelerated-video-encode"

# 设置库路径
export DYLD_FALLBACK_LIBRARY_PATH="/System/Library/Frameworks/CoreText.framework/Versions/A:/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/CoreText.framework/Versions/A:/usr/lib:$DYLD_FALLBACK_LIBRARY_PATH"

# 设置 Qt 环境变量
export QT_MAC_WANTS_LAYER=0
export QT_AUTO_SCREEN_SCALE_FACTOR=0
export QT_SCALE_FACTOR=1
export QT_FONT_DPI=96

# 尝试强制使用系统 WebKit 而不是 Chromium
export QTWEBENGINE_CHROMIUM_FLAGS="$QTWEBENGINE_CHROMIUM_FLAGS --use-system-webkit"

# 设置日志级别以获取更多调试信息
export QT_LOGGING_RULES="qt.webenginecontext.debug=true"

echo "所有兼容性设置已应用"
echo "正在启动 YCursor..."
echo ""
echo "如果仍然失败，这表明 Qt WebEngine 6.9.0 与 macOS 12.0.1 存在根本性不兼容"
echo "强烈建议："
echo "1. 升级 macOS 到 13.0+ 版本"
echo "2. 或联系 YCursor 开发者获取兼容版本"
echo ""

# 启动应用
exec /Applications/YCursor.app/Contents/MacOS/YCursor "$@"
