import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json Qt3D.Input 2.15'

Module {
    dependencies: ["Qt3D.Core 2.0"]
    Component {
        name: "Qt3DInput::QAbstractActionInput"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Input/AbstractActionInput 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DInput::QAbstractAxisInput"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Input/AbstractAxisInput 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "sourceDevice"; type: "Qt3DInput::QAbstractPhysicalDevice"; isPointer: true }
        Signal {
            name: "sourceDeviceChanged"
            Parameter { name: "sourceDevice"; type: "QAbstractPhysicalDevice"; isPointer: true }
        }
        Method {
            name: "setSourceDevice"
            Parameter { name: "sourceDevice"; type: "QAbstractPhysicalDevice"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DInput::QAbstractPhysicalDevice"
        defaultProperty: "data"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Input/QAbstractPhysicalDevice 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [200]
        Property {
            name: "axisSettings"
            revision: 200
            type: "Qt3DInput::QAxisSetting"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DInput::QAction"
        defaultProperty: "inputs"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Input/Action 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "active"; type: "bool"; isReadonly: true }
        Signal {
            name: "activeChanged"
            Parameter { name: "isActive"; type: "bool" }
        }
        Property {
            name: "inputs"
            revision: 200
            type: "Qt3DInput::QAbstractActionInput"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DInput::QActionInput"
        prototype: "Qt3DInput::QAbstractActionInput"
        exports: ["Qt3D.Input/ActionInput 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "sourceDevice"; type: "Qt3DInput::QAbstractPhysicalDevice"; isPointer: true }
        Property { name: "buttons"; type: "QVector<int>" }
        Signal {
            name: "sourceDeviceChanged"
            Parameter { name: "sourceDevice"; type: "QAbstractPhysicalDevice"; isPointer: true }
        }
        Signal {
            name: "buttonsChanged"
            Parameter { name: "buttons"; type: "QVector<int>" }
        }
        Method {
            name: "setSourceDevice"
            Parameter { name: "sourceDevice"; type: "QAbstractPhysicalDevice"; isPointer: true }
        }
        Method {
            name: "setButtons"
            Parameter { name: "buttons"; type: "QVector<int>" }
        }
    }
    Component {
        name: "Qt3DInput::QAnalogAxisInput"
        prototype: "Qt3DInput::QAbstractAxisInput"
        exports: ["Qt3D.Input/AnalogAxisInput 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "axis"; type: "int" }
        Signal {
            name: "axisChanged"
            Parameter { name: "axis"; type: "int" }
        }
        Method {
            name: "setAxis"
            Parameter { name: "axis"; type: "int" }
        }
    }
    Component {
        name: "Qt3DInput::QAxis"
        defaultProperty: "inputs"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Input/Axis 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "value"; type: "float"; isReadonly: true }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Property {
            name: "inputs"
            revision: 200
            type: "Qt3DInput::QAbstractAxisInput"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DInput::QAxisAccumulator"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Input/AxisAccumulator 2.1"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "SourceAxisType"
            values: {
                "Velocity": 0,
                "Acceleration": 1
            }
        }
        Property { name: "sourceAxis"; type: "Qt3DInput::QAxis"; isPointer: true }
        Property { name: "sourceAxisType"; type: "SourceAxisType" }
        Property { name: "scale"; type: "float" }
        Property { name: "value"; type: "float"; isReadonly: true }
        Property { name: "velocity"; type: "float"; isReadonly: true }
        Signal {
            name: "sourceAxisChanged"
            Parameter { name: "sourceAxis"; type: "Qt3DInput::QAxis"; isPointer: true }
        }
        Signal {
            name: "sourceAxisTypeChanged"
            Parameter { name: "sourceAxisType"; type: "QAxisAccumulator::SourceAxisType" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "velocityChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "scaleChanged"
            Parameter { name: "scale"; type: "float" }
        }
        Method {
            name: "setSourceAxis"
            Parameter { name: "sourceAxis"; type: "Qt3DInput::QAxis"; isPointer: true }
        }
        Method {
            name: "setSourceAxisType"
            Parameter { name: "sourceAxisType"; type: "QAxisAccumulator::SourceAxisType" }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "float" }
        }
    }
    Component {
        name: "Qt3DInput::QAxisSetting"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Input/AxisSetting 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "deadZoneRadius"; type: "float" }
        Property { name: "axes"; type: "QVector<int>" }
        Property { name: "smooth"; type: "bool" }
        Signal {
            name: "deadZoneRadiusChanged"
            Parameter { name: "deadZoneRadius"; type: "float" }
        }
        Signal {
            name: "axesChanged"
            Parameter { name: "axes"; type: "QVector<int>" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { name: "smooth"; type: "bool" }
        }
        Method {
            name: "setDeadZoneRadius"
            Parameter { name: "deadZoneRadius"; type: "float" }
        }
        Method {
            name: "setAxes"
            Parameter { name: "axes"; type: "QVector<int>" }
        }
        Method {
            name: "setSmoothEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DInput::QButtonAxisInput"
        prototype: "Qt3DInput::QAbstractAxisInput"
        exports: ["Qt3D.Input/ButtonAxisInput 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "scale"; type: "float" }
        Property { name: "buttons"; type: "QVector<int>" }
        Property { name: "acceleration"; type: "float" }
        Property { name: "deceleration"; type: "float" }
        Signal {
            name: "scaleChanged"
            Parameter { name: "scale"; type: "float" }
        }
        Signal {
            name: "buttonsChanged"
            Parameter { name: "buttons"; type: "QVector<int>" }
        }
        Signal {
            name: "accelerationChanged"
            Parameter { name: "acceleration"; type: "float" }
        }
        Signal {
            name: "decelerationChanged"
            Parameter { name: "deceleration"; type: "float" }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "float" }
        }
        Method {
            name: "setButtons"
            Parameter { name: "buttons"; type: "QVector<int>" }
        }
        Method {
            name: "setAcceleration"
            Parameter { name: "acceleration"; type: "float" }
        }
        Method {
            name: "setDeceleration"
            Parameter { name: "deceleration"; type: "float" }
        }
    }
    Component {
        name: "Qt3DInput::QGamepadInput"
        prototype: "Qt3DInput::QAbstractPhysicalDevice"
        exports: ["Qt3D.Input/GamepadInput 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "deviceId"; type: "int" }
    }
    Component {
        name: "Qt3DInput::QInputChord"
        defaultProperty: "data"
        prototype: "Qt3DInput::QAbstractActionInput"
        exports: ["Qt3D.Input/InputChord 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "timeout"; type: "int" }
        Signal {
            name: "timeoutChanged"
            Parameter { name: "timeout"; type: "int" }
        }
        Method {
            name: "setTimeout"
            Parameter { name: "timeout"; type: "int" }
        }
        Property {
            name: "chords"
            revision: 200
            type: "Qt3DInput::QAbstractActionInput"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DInput::QInputSequence"
        defaultProperty: "data"
        prototype: "Qt3DInput::QAbstractActionInput"
        exports: ["Qt3D.Input/InputSequence 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "timeout"; type: "int" }
        Property { name: "buttonInterval"; type: "int" }
        Signal {
            name: "timeoutChanged"
            Parameter { name: "timeout"; type: "int" }
        }
        Signal {
            name: "buttonIntervalChanged"
            Parameter { name: "buttonInterval"; type: "int" }
        }
        Method {
            name: "setTimeout"
            Parameter { name: "timeout"; type: "int" }
        }
        Method {
            name: "setButtonInterval"
            Parameter { name: "buttonInterval"; type: "int" }
        }
        Property {
            name: "sequences"
            revision: 200
            type: "Qt3DInput::QAbstractActionInput"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DInput::QInputSettings"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Input/InputSettings 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "eventSource"; type: "QObject"; isPointer: true }
        Signal {
            name: "eventSourceChanged"
            Parameter { type: "QObject"; isPointer: true }
        }
        Method {
            name: "setEventSource"
            Parameter { name: "eventSource"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DInput::QKeyEvent"
        prototype: "QObject"
        exports: ["Qt3D.Input/KeyEvent 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "key"; type: "int"; isReadonly: true }
        Property { name: "text"; type: "string"; isReadonly: true }
        Property { name: "modifiers"; type: "int"; isReadonly: true }
        Property { name: "isAutoRepeat"; type: "bool"; isReadonly: true }
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "nativeScanCode"; type: "uint"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
        Method {
            name: "matches"
            type: "bool"
            Parameter { name: "key_"; type: "QKeySequence::StandardKey" }
        }
    }
    Component {
        name: "Qt3DInput::QKeyboardDevice"
        prototype: "Qt3DInput::QAbstractPhysicalDevice"
        exports: ["Qt3D.Input/KeyboardDevice 2.0"]
        exportMetaObjectRevisions: [0]
        Property {
            name: "activeInput"
            type: "Qt3DInput::QKeyboardHandler"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "activeInputChanged"
            Parameter { name: "activeInput"; type: "QKeyboardHandler"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DInput::QKeyboardHandler"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Input/KeyboardHandler 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "sourceDevice"; type: "Qt3DInput::QKeyboardDevice"; isPointer: true }
        Property { name: "focus"; type: "bool" }
        Signal {
            name: "sourceDeviceChanged"
            Parameter { name: "keyboardDevice"; type: "QKeyboardDevice"; isPointer: true }
        }
        Signal {
            name: "focusChanged"
            Parameter { name: "focus"; type: "bool" }
        }
        Signal {
            name: "digit0Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit1Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit2Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit3Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit4Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit5Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit6Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit7Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit8Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit9Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "leftPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "rightPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "upPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "downPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "tabPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "backtabPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "asteriskPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "numberSignPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "escapePressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "returnPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "enterPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "deletePressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "spacePressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "backPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "cancelPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "selectPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "yesPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "noPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context1Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context2Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context3Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context4Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "callPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "hangupPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "flipPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "menuPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "volumeUpPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "volumeDownPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Method {
            name: "setSourceDevice"
            Parameter { name: "keyboardDevice"; type: "Qt3DInput::QKeyboardDevice"; isPointer: true }
        }
        Method {
            name: "setFocus"
            Parameter { name: "focus"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DInput::QLogicalDevice"
        defaultProperty: "data"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Input/LogicalDevice 2.0"]
        exportMetaObjectRevisions: [200]
        Property { name: "axes"; revision: 200; type: "Qt3DInput::QAxis"; isList: true; isReadonly: true }
        Property {
            name: "actions"
            revision: 200
            type: "Qt3DInput::QAction"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DInput::QMouseDevice"
        prototype: "Qt3DInput::QAbstractPhysicalDevice"
        exports: ["Qt3D.Input/MouseDevice 2.0", "Qt3D.Input/MouseDevice 2.15"]
        exportMetaObjectRevisions: [0, 15]
        Enum {
            name: "Axis"
            values: {
                "X": 0,
                "Y": 1,
                "WheelX": 2,
                "WheelY": 3
            }
        }
        Property { name: "sensitivity"; type: "float" }
        Property { name: "updateAxesContinuously"; revision: 15; type: "bool" }
        Signal {
            name: "sensitivityChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "updateAxesContinuouslyChanged"
            Parameter { name: "updateAxesContinuously"; type: "bool" }
        }
        Method {
            name: "setSensitivity"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setUpdateAxesContinuously"
            Parameter { name: "updateAxesContinuously"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DInput::QMouseEvent"
        prototype: "QObject"
        exports: ["Qt3D.Input/MouseEvent 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Buttons"
            values: {
                "LeftButton": 1,
                "RightButton": 2,
                "MiddleButton": 4,
                "BackButton": 8,
                "NoButton": 0
            }
        }
        Enum {
            name: "Modifiers"
            values: {
                "NoModifier": 0,
                "ShiftModifier": 33554432,
                "ControlModifier": 67108864,
                "AltModifier": 134217728,
                "MetaModifier": 268435456,
                "KeypadModifier": 536870912
            }
        }
        Property { name: "x"; type: "int"; isReadonly: true }
        Property { name: "y"; type: "int"; isReadonly: true }
        Property { name: "wasHeld"; type: "bool"; isReadonly: true }
        Property { name: "button"; type: "Qt3DInput::QMouseEvent::Buttons"; isReadonly: true }
        Property { name: "buttons"; type: "int"; isReadonly: true }
        Property { name: "modifiers"; type: "Qt3DInput::QMouseEvent::Modifiers"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
    }
    Component {
        name: "Qt3DInput::QMouseHandler"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Input/MouseHandler 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "sourceDevice"; type: "Qt3DInput::QMouseDevice"; isPointer: true }
        Property { name: "containsMouse"; type: "bool"; isReadonly: true }
        Signal {
            name: "sourceDeviceChanged"
            Parameter { name: "mouseDevice"; type: "QMouseDevice"; isPointer: true }
        }
        Signal {
            name: "containsMouseChanged"
            Parameter { name: "containsMouse"; type: "bool" }
        }
        Signal {
            name: "clicked"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal { name: "entered" }
        Signal { name: "exited" }
        Signal {
            name: "pressed"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "pressAndHold"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "wheel"
            Parameter { name: "wheel"; type: "Qt3DInput::QWheelEvent"; isPointer: true }
        }
        Method {
            name: "setSourceDevice"
            Parameter { name: "mouseDevice"; type: "QMouseDevice"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DInput::QWheelEvent"
        prototype: "QObject"
        exports: ["Qt3D.Input/WheelEvent 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Buttons"
            values: {
                "LeftButton": 1,
                "RightButton": 2,
                "MiddleButton": 4,
                "BackButton": 8,
                "NoButton": 0
            }
        }
        Enum {
            name: "Modifiers"
            values: {
                "NoModifier": 0,
                "ShiftModifier": 33554432,
                "ControlModifier": 67108864,
                "AltModifier": 134217728,
                "MetaModifier": 268435456,
                "KeypadModifier": 536870912
            }
        }
        Property { name: "x"; type: "int"; isReadonly: true }
        Property { name: "y"; type: "int"; isReadonly: true }
        Property { name: "angleDelta"; type: "QPoint"; isReadonly: true }
        Property { name: "buttons"; type: "int"; isReadonly: true }
        Property { name: "modifiers"; type: "Qt3DInput::QWheelEvent::Modifiers"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
    }
}
