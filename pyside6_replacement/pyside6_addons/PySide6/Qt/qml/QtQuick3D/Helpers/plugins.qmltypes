import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/gridgeometry_p.h"
        name: "GridGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: ["QtQuick3D.Helpers/GridGeometry 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "horizontalLines"
            type: "int"
            read: "horizontalLines"
            write: "setHorizontalLines"
            notify: "horizontalLinesChanged"
            index: 0
        }
        Property {
            name: "verticalLines"
            type: "int"
            read: "verticalLines"
            write: "setVerticalLines"
            notify: "verticalLinesChanged"
            index: 1
        }
        Property {
            name: "horizontalStep"
            type: "float"
            read: "horizontalStep"
            write: "setHorizontalStep"
            notify: "horizontalStepChanged"
            index: 2
        }
        Property {
            name: "verticalStep"
            type: "float"
            read: "verticalStep"
            write: "setVerticalStep"
            notify: "verticalStepChanged"
            index: 3
        }
        Signal { name: "horizontalLinesChanged" }
        Signal { name: "verticalLinesChanged" }
        Signal { name: "horizontalStepChanged" }
        Signal { name: "verticalStepChanged" }
        Method {
            name: "setHorizontalLines"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "setVerticalLines"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "setHorizontalStep"
            Parameter { name: "step"; type: "float" }
        }
        Method {
            name: "setVerticalStep"
            Parameter { name: "step"; type: "float" }
        }
    }
    Component {
        file: "private/heightfieldgeometry_p.h"
        name: "HeightFieldGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: [
            "QtQuick3D.Helpers/HeightFieldGeometry 6.0",
            "QtQuick3D.Helpers/HeightFieldGeometry 6.5"
        ]
        exportMetaObjectRevisions: [1536, 1541]
        Property {
            name: "source"
            revision: 1541
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "smoothShading"
            type: "bool"
            read: "smoothShading"
            write: "setSmoothShading"
            notify: "smoothShadingChanged"
            index: 1
        }
        Property {
            name: "extents"
            type: "QVector3D"
            read: "extents"
            write: "setExtents"
            notify: "extentsChanged"
            index: 2
        }
        Property {
            name: "heightMap"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 3
        }
        Signal { name: "sourceChanged" }
        Signal { name: "smoothShadingChanged" }
        Signal { name: "extentsChanged" }
    }
    Component {
        file: "private/instancerepeater_p.h"
        name: "InstanceModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        exports: ["QtQuick3D.Helpers/InstanceModel 6.4"]
        exportMetaObjectRevisions: [1540]
        Property {
            name: "instancingTable"
            type: "QQuick3DInstancing"
            isPointer: true
            read: "instancing"
            write: "setInstancing"
            notify: "instancingChanged"
            index: 0
        }
        Signal { name: "instancingChanged" }
        Method { name: "reset" }
    }
    Component {
        file: "private/instancerepeater_p.h"
        name: "InstanceRepeater"
        accessSemantics: "reference"
        defaultProperty: "delegate"
        prototype: "QQuick3DRepeater"
        exports: ["QtQuick3D.Helpers/InstanceRepeater 6.4"]
        exportMetaObjectRevisions: [1540]
        Property {
            name: "instancingTable"
            type: "QQuick3DInstancing"
            isPointer: true
            read: "instancing"
            write: "setInstancing"
            notify: "instancingChanged"
            index: 0
        }
        Signal { name: "instancingChanged" }
    }
    Component {
        file: "private/lookatnode_p.h"
        name: "LookAtNode"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Helpers/LookAtNode 6.4"]
        exportMetaObjectRevisions: [1540]
        Property {
            name: "target"
            type: "QQuick3DNode"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 0
        }
        Signal { name: "targetChanged" }
        Method {
            name: "setTarget"
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true }
        }
        Method { name: "updateLookAt" }
    }
    Component {
        file: "private/proceduralskytexturedata_p.h"
        name: "ProceduralSkyTextureData"
        accessSemantics: "reference"
        prototype: "QQuick3DTextureData"
        exports: ["QtQuick3D.Helpers/ProceduralSkyTextureData 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "SkyTextureQuality"
            values: [
                "SkyTextureQualityLow",
                "SkyTextureQualityMedium",
                "SkyTextureQualityHigh",
                "SkyTextureQualityVeryHigh"
            ]
        }
        Property {
            name: "skyTopColor"
            type: "QColor"
            read: "skyTopColor"
            write: "setSkyTopColor"
            notify: "skyTopColorChanged"
            index: 0
        }
        Property {
            name: "skyHorizonColor"
            type: "QColor"
            read: "skyHorizonColor"
            write: "setSkyHorizonColor"
            notify: "skyHorizonColorChanged"
            index: 1
        }
        Property {
            name: "skyCurve"
            type: "float"
            read: "skyCurve"
            write: "setSkyCurve"
            notify: "skyCurveChanged"
            index: 2
        }
        Property {
            name: "skyEnergy"
            type: "float"
            read: "skyEnergy"
            write: "setSkyEnergy"
            notify: "skyEnergyChanged"
            index: 3
        }
        Property {
            name: "groundBottomColor"
            type: "QColor"
            read: "groundBottomColor"
            write: "setGroundBottomColor"
            notify: "groundBottomColorChanged"
            index: 4
        }
        Property {
            name: "groundHorizonColor"
            type: "QColor"
            read: "groundHorizonColor"
            write: "setGroundHorizonColor"
            notify: "groundHorizonColorChanged"
            index: 5
        }
        Property {
            name: "groundCurve"
            type: "float"
            read: "groundCurve"
            write: "setGroundCurve"
            notify: "groundCurveChanged"
            index: 6
        }
        Property {
            name: "groundEnergy"
            type: "float"
            read: "groundEnergy"
            write: "setGroundEnergy"
            notify: "groundEnergyChanged"
            index: 7
        }
        Property {
            name: "sunColor"
            type: "QColor"
            read: "sunColor"
            write: "setSunColor"
            notify: "sunColorChanged"
            index: 8
        }
        Property {
            name: "sunLatitude"
            type: "float"
            read: "sunLatitude"
            write: "setSunLatitude"
            notify: "sunLatitudeChanged"
            index: 9
        }
        Property {
            name: "sunLongitude"
            type: "float"
            read: "sunLongitude"
            write: "setSunLongitude"
            notify: "sunLongitudeChanged"
            index: 10
        }
        Property {
            name: "sunAngleMin"
            type: "float"
            read: "sunAngleMin"
            write: "setSunAngleMin"
            notify: "sunAngleMinChanged"
            index: 11
        }
        Property {
            name: "sunAngleMax"
            type: "float"
            read: "sunAngleMax"
            write: "setSunAngleMax"
            notify: "sunAngleMaxChanged"
            index: 12
        }
        Property {
            name: "sunCurve"
            type: "float"
            read: "sunCurve"
            write: "setSunCurve"
            notify: "sunCurveChanged"
            index: 13
        }
        Property {
            name: "sunEnergy"
            type: "float"
            read: "sunEnergy"
            write: "setSunEnergy"
            notify: "sunEnergyChanged"
            index: 14
        }
        Property {
            name: "textureQuality"
            type: "SkyTextureQuality"
            read: "textureQuality"
            write: "setTextureQuality"
            notify: "textureQualityChanged"
            index: 15
        }
        Signal {
            name: "skyTopColorChanged"
            Parameter { name: "skyTopColor"; type: "QColor" }
        }
        Signal {
            name: "skyHorizonColorChanged"
            Parameter { name: "skyHorizonColor"; type: "QColor" }
        }
        Signal {
            name: "skyCurveChanged"
            Parameter { name: "skyCurve"; type: "float" }
        }
        Signal {
            name: "skyEnergyChanged"
            Parameter { name: "skyEnergy"; type: "float" }
        }
        Signal {
            name: "groundBottomColorChanged"
            Parameter { name: "groundBottomColor"; type: "QColor" }
        }
        Signal {
            name: "groundHorizonColorChanged"
            Parameter { name: "groundHorizonColor"; type: "QColor" }
        }
        Signal {
            name: "groundCurveChanged"
            Parameter { name: "groundCurve"; type: "float" }
        }
        Signal {
            name: "groundEnergyChanged"
            Parameter { name: "groundEnergy"; type: "float" }
        }
        Signal {
            name: "sunColorChanged"
            Parameter { name: "sunColor"; type: "QColor" }
        }
        Signal {
            name: "sunLatitudeChanged"
            Parameter { name: "sunLatitude"; type: "float" }
        }
        Signal {
            name: "sunLongitudeChanged"
            Parameter { name: "sunLongitude"; type: "float" }
        }
        Signal {
            name: "sunAngleMinChanged"
            Parameter { name: "sunAngleMin"; type: "float" }
        }
        Signal {
            name: "sunAngleMaxChanged"
            Parameter { name: "sunAngleMax"; type: "float" }
        }
        Signal {
            name: "sunCurveChanged"
            Parameter { name: "sunCurve"; type: "float" }
        }
        Signal {
            name: "sunEnergyChanged"
            Parameter { name: "sunEnergy"; type: "float" }
        }
        Signal {
            name: "textureQualityChanged"
            Parameter { name: "textureQuality"; type: "SkyTextureQuality" }
        }
        Method {
            name: "setSkyTopColor"
            Parameter { name: "skyTopColor"; type: "QColor" }
        }
        Method {
            name: "setSkyHorizonColor"
            Parameter { name: "skyHorizonColor"; type: "QColor" }
        }
        Method {
            name: "setSkyCurve"
            Parameter { name: "skyCurve"; type: "float" }
        }
        Method {
            name: "setSkyEnergy"
            Parameter { name: "skyEnergy"; type: "float" }
        }
        Method {
            name: "setGroundBottomColor"
            Parameter { name: "groundBottomColor"; type: "QColor" }
        }
        Method {
            name: "setGroundHorizonColor"
            Parameter { name: "groundHorizonColor"; type: "QColor" }
        }
        Method {
            name: "setGroundCurve"
            Parameter { name: "groundCurve"; type: "float" }
        }
        Method {
            name: "setGroundEnergy"
            Parameter { name: "groundEnergy"; type: "float" }
        }
        Method {
            name: "setSunColor"
            Parameter { name: "sunColor"; type: "QColor" }
        }
        Method {
            name: "setSunLatitude"
            Parameter { name: "sunLatitude"; type: "float" }
        }
        Method {
            name: "setSunLongitude"
            Parameter { name: "sunLongitude"; type: "float" }
        }
        Method {
            name: "setSunAngleMin"
            Parameter { name: "sunAngleMin"; type: "float" }
        }
        Method {
            name: "setSunAngleMax"
            Parameter { name: "sunAngleMax"; type: "float" }
        }
        Method {
            name: "setSunCurve"
            Parameter { name: "sunCurve"; type: "float" }
        }
        Method {
            name: "setSunEnergy"
            Parameter { name: "sunEnergy"; type: "float" }
        }
        Method {
            name: "setTextureQuality"
            Parameter { name: "textureQuality"; type: "SkyTextureQuality" }
        }
        Method { name: "generateRGBA16FTexture" }
    }
    Component {
        file: "private/infinitegrid_p.h"
        name: "QQuick3DInfiniteGrid"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQuick3D.Helpers/InfiniteGrid 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "visible"
            type: "bool"
            read: "visible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 0
        }
        Property {
            name: "gridInterval"
            type: "float"
            read: "gridInterval"
            write: "setGridInterval"
            notify: "gridIntervalChanged"
            index: 1
        }
        Property {
            name: "gridAxes"
            type: "bool"
            read: "gridAxes"
            write: "setGridAxes"
            notify: "gridAxesChanged"
            index: 2
        }
        Signal { name: "visibleChanged" }
        Signal { name: "gridIntervalChanged" }
        Signal { name: "gridAxesChanged" }
    }
    Component {
        file: "private/randominstancing_p.h"
        name: "QQuick3DInstanceRange"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D.Helpers/InstanceRange 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "from"
            type: "QVariant"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
        }
        Property { name: "to"; type: "QVariant"; read: "to"; write: "setTo"; notify: "toChanged"; index: 1 }
        Property {
            name: "proportional"
            type: "bool"
            read: "proportional"
            write: "setProportional"
            notify: "proportionalChanged"
            index: 2
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "proportionalChanged" }
        Signal { name: "changed" }
        Method {
            name: "setFrom"
            Parameter { name: "from"; type: "QVariant" }
        }
        Method {
            name: "setTo"
            Parameter { name: "to"; type: "QVariant" }
        }
        Method {
            name: "setProportional"
            Parameter { name: "proportional"; type: "bool" }
        }
    }
    Component {
        file: "private/randominstancing_p.h"
        name: "QQuick3DRandomInstancing"
        accessSemantics: "reference"
        prototype: "QQuick3DInstancing"
        exports: [
            "QtQuick3D.Helpers/RandomInstancing 6.2",
            "QtQuick3D.Helpers/RandomInstancing 6.3"
        ]
        exportMetaObjectRevisions: [1538, 1539]
        Enum {
            name: "ColorModel"
            values: ["RGB", "HSV", "HSL"]
        }
        Property {
            name: "instanceCount"
            type: "int"
            read: "instanceCount"
            write: "setInstanceCount"
            notify: "instanceCountChanged"
            index: 0
        }
        Property {
            name: "position"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 1
        }
        Property {
            name: "scale"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 2
        }
        Property {
            name: "rotation"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 3
        }
        Property {
            name: "color"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 4
        }
        Property {
            name: "colorModel"
            type: "ColorModel"
            read: "colorModel"
            write: "setColorModel"
            notify: "colorModelChanged"
            index: 5
        }
        Property {
            name: "customData"
            type: "QQuick3DInstanceRange"
            isPointer: true
            read: "customData"
            write: "setCustomData"
            notify: "customDataChanged"
            index: 6
        }
        Property {
            name: "randomSeed"
            type: "int"
            read: "randomSeed"
            write: "setRandomSeed"
            notify: "randomSeedChanged"
            index: 7
        }
        Signal { name: "instanceCountChanged" }
        Signal { name: "randomSeedChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "scaleChanged" }
        Signal { name: "rotationChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "customDataChanged" }
        Signal { name: "colorModelChanged" }
        Method {
            name: "setInstanceCount"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Method {
            name: "setRandomSeed"
            Parameter { name: "randomSeed"; type: "int" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setRotation"
            Parameter { name: "rotation"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setColor"
            Parameter { name: "color"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setCustomData"
            Parameter { name: "customData"; type: "QQuick3DInstanceRange"; isPointer: true }
        }
        Method {
            name: "setColorModel"
            Parameter { name: "colorModel"; type: "ColorModel" }
        }
        Method { name: "handleChange" }
    }
}
