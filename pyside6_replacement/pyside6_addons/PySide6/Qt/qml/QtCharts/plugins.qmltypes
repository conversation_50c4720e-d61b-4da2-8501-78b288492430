import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QBarCategoryAxis"
        accessSemantics: "reference"
        prototype: "QAbstractAxis"
        exports: [
            "QtCharts/BarCategoriesAxis 1.0",
            "QtCharts/BarCategoriesAxis 6.0",
            "QtCharts/BarCategoriesAxis 6.2"
        ]
        exportMetaObjectRevisions: [256, 1536, 1538]
        Property {
            name: "categories"
            type: "QStringList"
            read: "categories"
            write: "setCategories"
            notify: "categoriesChanged"
            index: 0
        }
        Property {
            name: "min"
            type: "QString"
            read: "min"
            write: "setMin"
            notify: "minChanged"
            index: 1
        }
        Property {
            name: "max"
            type: "QString"
            read: "max"
            write: "setMax"
            notify: "maxChanged"
            index: 2
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 3
            isReadonly: true
        }
        Signal { name: "categoriesChanged" }
        Signal {
            name: "minChanged"
            Parameter { name: "min"; type: "QString" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "max"; type: "QString" }
        }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "QString" }
            Parameter { name: "max"; type: "QString" }
        }
        Signal { name: "countChanged" }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QAbstractAxis"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtCharts/AbstractAxis 1.0",
            "QtCharts/AbstractAxis 6.0",
            "QtCharts/AbstractAxis 6.2"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536, 1538]
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 0
        }
        Property {
            name: "lineVisible"
            type: "bool"
            read: "isLineVisible"
            write: "setLineVisible"
            notify: "lineVisibleChanged"
            index: 1
        }
        Property {
            name: "linePen"
            type: "QPen"
            read: "linePen"
            write: "setLinePen"
            notify: "linePenChanged"
            index: 2
        }
        Property {
            name: "color"
            type: "QColor"
            read: "linePenColor"
            write: "setLinePenColor"
            notify: "colorChanged"
            index: 3
        }
        Property {
            name: "labelsVisible"
            type: "bool"
            read: "labelsVisible"
            write: "setLabelsVisible"
            notify: "labelsVisibleChanged"
            index: 4
        }
        Property {
            name: "labelsBrush"
            type: "QBrush"
            read: "labelsBrush"
            write: "setLabelsBrush"
            notify: "labelsBrushChanged"
            index: 5
        }
        Property {
            name: "labelsAngle"
            type: "int"
            read: "labelsAngle"
            write: "setLabelsAngle"
            notify: "labelsAngleChanged"
            index: 6
        }
        Property {
            name: "labelsFont"
            type: "QFont"
            read: "labelsFont"
            write: "setLabelsFont"
            notify: "labelsFontChanged"
            index: 7
        }
        Property {
            name: "labelsColor"
            type: "QColor"
            read: "labelsColor"
            write: "setLabelsColor"
            notify: "labelsColorChanged"
            index: 8
        }
        Property {
            name: "labelsTruncated"
            revision: 1538
            type: "bool"
            read: "labelsTruncated"
            notify: "labelsTruncatedChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "truncateLabels"
            revision: 1538
            type: "bool"
            read: "truncateLabels"
            write: "setTruncateLabels"
            notify: "truncateLabelsChanged"
            index: 10
        }
        Property {
            name: "gridVisible"
            type: "bool"
            read: "isGridLineVisible"
            write: "setGridLineVisible"
            notify: "gridVisibleChanged"
            index: 11
        }
        Property {
            name: "gridLinePen"
            type: "QPen"
            read: "gridLinePen"
            write: "setGridLinePen"
            notify: "gridLinePenChanged"
            index: 12
        }
        Property {
            name: "minorGridVisible"
            type: "bool"
            read: "isMinorGridLineVisible"
            write: "setMinorGridLineVisible"
            notify: "minorGridVisibleChanged"
            index: 13
        }
        Property {
            name: "minorGridLinePen"
            type: "QPen"
            read: "minorGridLinePen"
            write: "setMinorGridLinePen"
            notify: "minorGridLinePenChanged"
            index: 14
        }
        Property {
            name: "gridLineColor"
            type: "QColor"
            read: "gridLineColor"
            write: "setGridLineColor"
            notify: "gridLineColorChanged"
            index: 15
        }
        Property {
            name: "minorGridLineColor"
            type: "QColor"
            read: "minorGridLineColor"
            write: "setMinorGridLineColor"
            notify: "minorGridLineColorChanged"
            index: 16
        }
        Property {
            name: "shadesVisible"
            type: "bool"
            read: "shadesVisible"
            write: "setShadesVisible"
            notify: "shadesVisibleChanged"
            index: 17
        }
        Property {
            name: "shadesColor"
            type: "QColor"
            read: "shadesColor"
            write: "setShadesColor"
            notify: "shadesColorChanged"
            index: 18
        }
        Property {
            name: "shadesBorderColor"
            type: "QColor"
            read: "shadesBorderColor"
            write: "setShadesBorderColor"
            notify: "shadesBorderColorChanged"
            index: 19
        }
        Property {
            name: "shadesPen"
            type: "QPen"
            read: "shadesPen"
            write: "setShadesPen"
            notify: "shadesPenChanged"
            index: 20
        }
        Property {
            name: "shadesBrush"
            type: "QBrush"
            read: "shadesBrush"
            write: "setShadesBrush"
            notify: "shadesBrushChanged"
            index: 21
        }
        Property {
            name: "titleText"
            type: "QString"
            read: "titleText"
            write: "setTitleText"
            notify: "titleTextChanged"
            index: 22
        }
        Property {
            name: "titleBrush"
            type: "QBrush"
            read: "titleBrush"
            write: "setTitleBrush"
            notify: "titleBrushChanged"
            index: 23
        }
        Property {
            name: "titleVisible"
            type: "bool"
            read: "isTitleVisible"
            write: "setTitleVisible"
            notify: "titleVisibleChanged"
            index: 24
        }
        Property {
            name: "titleFont"
            type: "QFont"
            read: "titleFont"
            write: "setTitleFont"
            notify: "titleFontChanged"
            index: 25
        }
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            index: 26
            isReadonly: true
        }
        Property {
            name: "alignment"
            type: "Qt::Alignment"
            read: "alignment"
            index: 27
            isReadonly: true
        }
        Property {
            name: "reverse"
            type: "bool"
            read: "isReverse"
            write: "setReverse"
            notify: "reverseChanged"
            index: 28
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "linePenChanged"
            Parameter { name: "pen"; type: "QPen" }
        }
        Signal {
            name: "lineVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "labelsVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "labelsBrushChanged"
            Parameter { name: "brush"; type: "QBrush" }
        }
        Signal {
            name: "labelsFontChanged"
            Parameter { name: "pen"; type: "QFont" }
        }
        Signal {
            name: "labelsAngleChanged"
            Parameter { name: "angle"; type: "int" }
        }
        Signal {
            name: "gridLinePenChanged"
            Parameter { name: "pen"; type: "QPen" }
        }
        Signal {
            name: "gridVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "minorGridVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "minorGridLinePenChanged"
            Parameter { name: "pen"; type: "QPen" }
        }
        Signal {
            name: "gridLineColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "minorGridLineColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "labelsColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "titleTextChanged"
            Parameter { name: "title"; type: "QString" }
        }
        Signal {
            name: "titleBrushChanged"
            Parameter { name: "brush"; type: "QBrush" }
        }
        Signal {
            name: "titleVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "titleFontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "shadesVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "shadesColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "shadesBorderColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "shadesPenChanged"
            Parameter { name: "pen"; type: "QPen" }
        }
        Signal {
            name: "shadesBrushChanged"
            Parameter { name: "brush"; type: "QBrush" }
        }
        Signal {
            name: "reverseChanged"
            Parameter { name: "reverse"; type: "bool" }
        }
        Signal {
            name: "labelsEditableChanged"
            Parameter { name: "editable"; type: "bool" }
        }
        Signal {
            name: "labelsTruncatedChanged"
            revision: 1538
            Parameter { name: "labelsTruncated"; type: "bool" }
        }
        Signal {
            name: "truncateLabelsChanged"
            revision: 1538
            Parameter { name: "truncateLabels"; type: "bool" }
        }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QAbstractBarSeries"
        accessSemantics: "reference"
        prototype: "QAbstractSeries"
        exports: [
            "QtCharts/AbstractBarSeries 1.0",
            "QtCharts/AbstractBarSeries 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Enum {
            name: "LabelsPosition"
            values: [
                "LabelsCenter",
                "LabelsInsideEnd",
                "LabelsInsideBase",
                "LabelsOutsideEnd"
            ]
        }
        Property { name: "barWidth"; type: "double"; read: "barWidth"; write: "setBarWidth"; index: 0 }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "labelsVisible"
            type: "bool"
            read: "isLabelsVisible"
            write: "setLabelsVisible"
            notify: "labelsVisibleChanged"
            index: 2
        }
        Property {
            name: "labelsFormat"
            type: "QString"
            read: "labelsFormat"
            write: "setLabelsFormat"
            notify: "labelsFormatChanged"
            index: 3
        }
        Property {
            name: "labelsPosition"
            type: "LabelsPosition"
            read: "labelsPosition"
            write: "setLabelsPosition"
            notify: "labelsPositionChanged"
            index: 4
        }
        Property {
            name: "labelsAngle"
            type: "double"
            read: "labelsAngle"
            write: "setLabelsAngle"
            notify: "labelsAngleChanged"
            index: 5
        }
        Property {
            name: "labelsPrecision"
            type: "int"
            read: "labelsPrecision"
            write: "setLabelsPrecision"
            notify: "labelsPrecisionChanged"
            index: 6
        }
        Signal {
            name: "clicked"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal {
            name: "hovered"
            Parameter { name: "status"; type: "bool" }
            Parameter { name: "index"; type: "int" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal {
            name: "pressed"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal { name: "countChanged" }
        Signal { name: "labelsVisibleChanged" }
        Signal {
            name: "labelsFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "labelsPositionChanged"
            Parameter { name: "position"; type: "QAbstractBarSeries::LabelsPosition" }
        }
        Signal {
            name: "labelsAngleChanged"
            Parameter { name: "angle"; type: "double" }
        }
        Signal {
            name: "labelsPrecisionChanged"
            Parameter { name: "precision"; type: "int" }
        }
        Signal {
            name: "barsetsAdded"
            Parameter { name: "sets"; type: "QList<QBarSet*>" }
        }
        Signal {
            name: "barsetsRemoved"
            Parameter { name: "sets"; type: "QList<QBarSet*>" }
        }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QAbstractSeries"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtCharts/AbstractSeries 1.0",
            "QtCharts/AbstractSeries 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Enum {
            name: "SeriesType"
            values: [
                "SeriesTypeLine",
                "SeriesTypeArea",
                "SeriesTypeBar",
                "SeriesTypeStackedBar",
                "SeriesTypePercentBar",
                "SeriesTypePie",
                "SeriesTypeScatter",
                "SeriesTypeSpline",
                "SeriesTypeHorizontalBar",
                "SeriesTypeHorizontalStackedBar",
                "SeriesTypeHorizontalPercentBar",
                "SeriesTypeBoxPlot",
                "SeriesTypeCandlestick"
            ]
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 1
        }
        Property {
            name: "opacity"
            type: "double"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 2
        }
        Property { name: "type"; type: "SeriesType"; read: "type"; index: 3; isReadonly: true }
        Property {
            name: "useOpenGL"
            type: "bool"
            read: "useOpenGL"
            write: "setUseOpenGL"
            notify: "useOpenGLChanged"
            index: 4
        }
        Signal { name: "nameChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "opacityChanged" }
        Signal { name: "useOpenGLChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QBarCategoryAxis"
        accessSemantics: "reference"
        prototype: "QAbstractAxis"
        exports: [
            "QtCharts/BarCategoryAxis 1.1",
            "QtCharts/BarCategoryAxis 6.0",
            "QtCharts/BarCategoryAxis 6.2"
        ]
        exportMetaObjectRevisions: [257, 1536, 1538]
        Property {
            name: "categories"
            type: "QStringList"
            read: "categories"
            write: "setCategories"
            notify: "categoriesChanged"
            index: 0
        }
        Property {
            name: "min"
            type: "QString"
            read: "min"
            write: "setMin"
            notify: "minChanged"
            index: 1
        }
        Property {
            name: "max"
            type: "QString"
            read: "max"
            write: "setMax"
            notify: "maxChanged"
            index: 2
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 3
            isReadonly: true
        }
        Signal { name: "categoriesChanged" }
        Signal {
            name: "minChanged"
            Parameter { name: "min"; type: "QString" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "max"; type: "QString" }
        }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "QString" }
            Parameter { name: "max"; type: "QString" }
        }
        Signal { name: "countChanged" }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QBarModelMapper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtCharts/BarModelMapper 1.0",
            "QtCharts/BarModelMapper 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QBoxPlotModelMapper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtCharts/BoxPlotModelMapper 2.0",
            "QtCharts/BoxPlotModelMapper 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QCandlestickModelMapper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtCharts/CandlestickModelMapper 2.2",
            "QtCharts/CandlestickModelMapper 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [514, 1536]
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelReplaced"
            index: 0
        }
        Property {
            name: "series"
            type: "QCandlestickSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesReplaced"
            index: 1
        }
        Signal { name: "modelReplaced" }
        Signal { name: "seriesReplaced" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QDateTimeAxis"
        accessSemantics: "reference"
        prototype: "QAbstractAxis"
        exports: [
            "QtCharts/DateTimeAxis 1.1",
            "QtCharts/DateTimeAxis 6.0",
            "QtCharts/DateTimeAxis 6.2"
        ]
        exportMetaObjectRevisions: [257, 1536, 1538]
        Property {
            name: "tickCount"
            type: "int"
            read: "tickCount"
            write: "setTickCount"
            notify: "tickCountChanged"
            index: 0
        }
        Property {
            name: "min"
            type: "QDateTime"
            read: "min"
            write: "setMin"
            notify: "minChanged"
            index: 1
        }
        Property {
            name: "max"
            type: "QDateTime"
            read: "max"
            write: "setMax"
            notify: "maxChanged"
            index: 2
        }
        Property {
            name: "format"
            type: "QString"
            read: "format"
            write: "setFormat"
            notify: "formatChanged"
            index: 3
        }
        Signal {
            name: "minChanged"
            Parameter { name: "min"; type: "QDateTime" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "max"; type: "QDateTime" }
        }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "QDateTime" }
            Parameter { name: "max"; type: "QDateTime" }
        }
        Signal {
            name: "formatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "tickCountChanged"
            Parameter { name: "tick"; type: "int" }
        }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QHBarModelMapper"
        accessSemantics: "reference"
        prototype: "QBarModelMapper"
        exports: [
            "QtCharts/HBarModelMapper 1.0",
            "QtCharts/HBarModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "series"
            type: "QAbstractBarSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesReplaced"
            index: 0
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelReplaced"
            index: 1
        }
        Property {
            name: "firstBarSetRow"
            type: "int"
            read: "firstBarSetRow"
            write: "setFirstBarSetRow"
            notify: "firstBarSetRowChanged"
            index: 2
        }
        Property {
            name: "lastBarSetRow"
            type: "int"
            read: "lastBarSetRow"
            write: "setLastBarSetRow"
            notify: "lastBarSetRowChanged"
            index: 3
        }
        Property {
            name: "firstColumn"
            type: "int"
            read: "firstColumn"
            write: "setFirstColumn"
            notify: "firstColumnChanged"
            index: 4
        }
        Property {
            name: "columnCount"
            type: "int"
            read: "columnCount"
            write: "setColumnCount"
            notify: "columnCountChanged"
            index: 5
        }
        Signal { name: "seriesReplaced" }
        Signal { name: "modelReplaced" }
        Signal { name: "firstBarSetRowChanged" }
        Signal { name: "lastBarSetRowChanged" }
        Signal { name: "firstColumnChanged" }
        Signal { name: "columnCountChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QHBoxPlotModelMapper"
        accessSemantics: "reference"
        prototype: "QBoxPlotModelMapper"
        exports: [
            "QtCharts/HBoxPlotModelMapper 2.0",
            "QtCharts/HBoxPlotModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "series"
            type: "QBoxPlotSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesReplaced"
            index: 0
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelReplaced"
            index: 1
        }
        Property {
            name: "firstBoxSetRow"
            type: "int"
            read: "firstBoxSetRow"
            write: "setFirstBoxSetRow"
            notify: "firstBoxSetRowChanged"
            index: 2
        }
        Property {
            name: "lastBoxSetRow"
            type: "int"
            read: "lastBoxSetRow"
            write: "setLastBoxSetRow"
            notify: "lastBoxSetRowChanged"
            index: 3
        }
        Property {
            name: "firstColumn"
            type: "int"
            read: "firstColumn"
            write: "setFirstColumn"
            notify: "firstColumnChanged"
            index: 4
        }
        Property {
            name: "columnCount"
            type: "int"
            read: "columnCount"
            write: "setColumnCount"
            notify: "columnCountChanged"
            index: 5
        }
        Signal { name: "seriesReplaced" }
        Signal { name: "modelReplaced" }
        Signal { name: "firstBoxSetRowChanged" }
        Signal { name: "lastBoxSetRowChanged" }
        Signal { name: "firstColumnChanged" }
        Signal { name: "columnCountChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QHCandlestickModelMapper"
        accessSemantics: "reference"
        prototype: "QCandlestickModelMapper"
        exports: [
            "QtCharts/HCandlestickModelMapper 2.2",
            "QtCharts/HCandlestickModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [514, 1536]
        Property {
            name: "timestampColumn"
            type: "int"
            read: "timestampColumn"
            write: "setTimestampColumn"
            notify: "timestampColumnChanged"
            index: 0
        }
        Property {
            name: "openColumn"
            type: "int"
            read: "openColumn"
            write: "setOpenColumn"
            notify: "openColumnChanged"
            index: 1
        }
        Property {
            name: "highColumn"
            type: "int"
            read: "highColumn"
            write: "setHighColumn"
            notify: "highColumnChanged"
            index: 2
        }
        Property {
            name: "lowColumn"
            type: "int"
            read: "lowColumn"
            write: "setLowColumn"
            notify: "lowColumnChanged"
            index: 3
        }
        Property {
            name: "closeColumn"
            type: "int"
            read: "closeColumn"
            write: "setCloseColumn"
            notify: "closeColumnChanged"
            index: 4
        }
        Property {
            name: "firstSetRow"
            type: "int"
            read: "firstSetRow"
            write: "setFirstSetRow"
            notify: "firstSetRowChanged"
            index: 5
        }
        Property {
            name: "lastSetRow"
            type: "int"
            read: "lastSetRow"
            write: "setLastSetRow"
            notify: "lastSetRowChanged"
            index: 6
        }
        Signal { name: "timestampColumnChanged" }
        Signal { name: "openColumnChanged" }
        Signal { name: "highColumnChanged" }
        Signal { name: "lowColumnChanged" }
        Signal { name: "closeColumnChanged" }
        Signal { name: "firstSetRowChanged" }
        Signal { name: "lastSetRowChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QHPieModelMapper"
        accessSemantics: "reference"
        prototype: "QPieModelMapper"
        exports: [
            "QtCharts/HPieModelMapper 1.0",
            "QtCharts/HPieModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "series"
            type: "QPieSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesReplaced"
            index: 0
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelReplaced"
            index: 1
        }
        Property {
            name: "valuesRow"
            type: "int"
            read: "valuesRow"
            write: "setValuesRow"
            notify: "valuesRowChanged"
            index: 2
        }
        Property {
            name: "labelsRow"
            type: "int"
            read: "labelsRow"
            write: "setLabelsRow"
            notify: "labelsRowChanged"
            index: 3
        }
        Property {
            name: "firstColumn"
            type: "int"
            read: "firstColumn"
            write: "setFirstColumn"
            notify: "firstColumnChanged"
            index: 4
        }
        Property {
            name: "columnCount"
            type: "int"
            read: "columnCount"
            write: "setColumnCount"
            notify: "columnCountChanged"
            index: 5
        }
        Signal { name: "seriesReplaced" }
        Signal { name: "modelReplaced" }
        Signal { name: "valuesRowChanged" }
        Signal { name: "labelsRowChanged" }
        Signal { name: "firstColumnChanged" }
        Signal { name: "columnCountChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QHXYModelMapper"
        accessSemantics: "reference"
        prototype: "QXYModelMapper"
        exports: [
            "QtCharts/HXYModelMapper 1.0",
            "QtCharts/HXYModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "series"
            type: "QXYSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesReplaced"
            index: 0
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelReplaced"
            index: 1
        }
        Property {
            name: "xRow"
            type: "int"
            read: "xRow"
            write: "setXRow"
            notify: "xRowChanged"
            index: 2
        }
        Property {
            name: "yRow"
            type: "int"
            read: "yRow"
            write: "setYRow"
            notify: "yRowChanged"
            index: 3
        }
        Property {
            name: "firstColumn"
            type: "int"
            read: "firstColumn"
            write: "setFirstColumn"
            notify: "firstColumnChanged"
            index: 4
        }
        Property {
            name: "columnCount"
            type: "int"
            read: "columnCount"
            write: "setColumnCount"
            notify: "columnCountChanged"
            index: 5
        }
        Signal { name: "seriesReplaced" }
        Signal { name: "modelReplaced" }
        Signal { name: "xRowChanged" }
        Signal { name: "yRowChanged" }
        Signal { name: "firstColumnChanged" }
        Signal { name: "columnCountChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QLegend"
        accessSemantics: "reference"
        prototype: "QGraphicsWidget"
        exports: [
            "QtCharts/Legend 1.0",
            "QtCharts/Legend 6.0",
            "QtCharts/Legend 6.2"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536, 1538]
        Enum {
            name: "MarkerShape"
            values: [
                "MarkerShapeDefault",
                "MarkerShapeRectangle",
                "MarkerShapeCircle",
                "MarkerShapeFromSeries",
                "MarkerShapeRotatedRectangle",
                "MarkerShapeTriangle",
                "MarkerShapeStar",
                "MarkerShapePentagon"
            ]
        }
        Property {
            name: "alignment"
            type: "Qt::Alignment"
            read: "alignment"
            write: "setAlignment"
            index: 0
        }
        Property {
            name: "backgroundVisible"
            type: "bool"
            read: "isBackgroundVisible"
            write: "setBackgroundVisible"
            notify: "backgroundVisibleChanged"
            index: 1
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "borderColor"
            type: "QColor"
            read: "borderColor"
            write: "setBorderColor"
            notify: "borderColorChanged"
            index: 3
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 4
        }
        Property {
            name: "labelColor"
            type: "QColor"
            read: "labelColor"
            write: "setLabelColor"
            notify: "labelColorChanged"
            index: 5
        }
        Property {
            name: "reverseMarkers"
            type: "bool"
            read: "reverseMarkers"
            write: "setReverseMarkers"
            notify: "reverseMarkersChanged"
            index: 6
        }
        Property {
            name: "showToolTips"
            type: "bool"
            read: "showToolTips"
            write: "setShowToolTips"
            notify: "showToolTipsChanged"
            index: 7
        }
        Property {
            name: "markerShape"
            type: "MarkerShape"
            read: "markerShape"
            write: "setMarkerShape"
            notify: "markerShapeChanged"
            index: 8
        }
        Signal {
            name: "backgroundVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "borderColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "labelColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "reverseMarkersChanged"
            Parameter { name: "reverseMarkers"; type: "bool" }
        }
        Signal {
            name: "showToolTipsChanged"
            Parameter { name: "showToolTips"; type: "bool" }
        }
        Signal {
            name: "markerShapeChanged"
            Parameter { name: "shape"; type: "MarkerShape" }
        }
        Signal {
            name: "attachedToChartChanged"
            revision: 1538
            Parameter { name: "attachedToChart"; type: "bool" }
        }
        Signal {
            name: "interactiveChanged"
            Parameter { name: "interactive"; type: "bool" }
        }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QLogValueAxis"
        accessSemantics: "reference"
        prototype: "QAbstractAxis"
        exports: [
            "QtCharts/LogValueAxis 1.3",
            "QtCharts/LogValueAxis 6.0",
            "QtCharts/LogValueAxis 6.2"
        ]
        exportMetaObjectRevisions: [259, 1536, 1538]
        Property { name: "min"; type: "double"; read: "min"; write: "setMin"; notify: "minChanged"; index: 0 }
        Property { name: "max"; type: "double"; read: "max"; write: "setMax"; notify: "maxChanged"; index: 1 }
        Property {
            name: "labelFormat"
            type: "QString"
            read: "labelFormat"
            write: "setLabelFormat"
            notify: "labelFormatChanged"
            index: 2
        }
        Property {
            name: "base"
            type: "double"
            read: "base"
            write: "setBase"
            notify: "baseChanged"
            index: 3
        }
        Property {
            name: "tickCount"
            type: "int"
            read: "tickCount"
            notify: "tickCountChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "minorTickCount"
            type: "int"
            read: "minorTickCount"
            write: "setMinorTickCount"
            notify: "minorTickCountChanged"
            index: 5
        }
        Signal {
            name: "minChanged"
            Parameter { name: "min"; type: "double" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "max"; type: "double" }
        }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "double" }
            Parameter { name: "max"; type: "double" }
        }
        Signal {
            name: "labelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "baseChanged"
            Parameter { name: "base"; type: "double" }
        }
        Signal {
            name: "tickCountChanged"
            Parameter { name: "tickCount"; type: "int" }
        }
        Signal {
            name: "minorTickCountChanged"
            Parameter { name: "minorTickCount"; type: "int" }
        }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QPieModelMapper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtCharts/PieModelMapper 1.0",
            "QtCharts/PieModelMapper 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QVBarModelMapper"
        accessSemantics: "reference"
        prototype: "QBarModelMapper"
        exports: [
            "QtCharts/VBarModelMapper 1.0",
            "QtCharts/VBarModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "series"
            type: "QAbstractBarSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesReplaced"
            index: 0
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelReplaced"
            index: 1
        }
        Property {
            name: "firstBarSetColumn"
            type: "int"
            read: "firstBarSetColumn"
            write: "setFirstBarSetColumn"
            notify: "firstBarSetColumnChanged"
            index: 2
        }
        Property {
            name: "lastBarSetColumn"
            type: "int"
            read: "lastBarSetColumn"
            write: "setLastBarSetColumn"
            notify: "lastBarSetColumnChanged"
            index: 3
        }
        Property {
            name: "firstRow"
            type: "int"
            read: "firstRow"
            write: "setFirstRow"
            notify: "firstRowChanged"
            index: 4
        }
        Property {
            name: "rowCount"
            type: "int"
            read: "rowCount"
            write: "setRowCount"
            notify: "rowCountChanged"
            index: 5
        }
        Signal { name: "seriesReplaced" }
        Signal { name: "modelReplaced" }
        Signal { name: "firstBarSetColumnChanged" }
        Signal { name: "lastBarSetColumnChanged" }
        Signal { name: "firstRowChanged" }
        Signal { name: "rowCountChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QVBoxPlotModelMapper"
        accessSemantics: "reference"
        prototype: "QBoxPlotModelMapper"
        exports: [
            "QtCharts/VBoxPlotModelMapper 2.0",
            "QtCharts/VBoxPlotModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "series"
            type: "QBoxPlotSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesReplaced"
            index: 0
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelReplaced"
            index: 1
        }
        Property {
            name: "firstBoxSetColumn"
            type: "int"
            read: "firstBoxSetColumn"
            write: "setFirstBoxSetColumn"
            notify: "firstBoxSetColumnChanged"
            index: 2
        }
        Property {
            name: "lastBoxSetColumn"
            type: "int"
            read: "lastBoxSetColumn"
            write: "setLastBoxSetColumn"
            notify: "lastBoxSetColumnChanged"
            index: 3
        }
        Property {
            name: "firstRow"
            type: "int"
            read: "firstRow"
            write: "setFirstRow"
            notify: "firstRowChanged"
            index: 4
        }
        Property {
            name: "rowCount"
            type: "int"
            read: "rowCount"
            write: "setRowCount"
            notify: "rowCountChanged"
            index: 5
        }
        Signal { name: "seriesReplaced" }
        Signal { name: "modelReplaced" }
        Signal { name: "firstBoxSetColumnChanged" }
        Signal { name: "lastBoxSetColumnChanged" }
        Signal { name: "firstRowChanged" }
        Signal { name: "rowCountChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QVCandlestickModelMapper"
        accessSemantics: "reference"
        prototype: "QCandlestickModelMapper"
        exports: [
            "QtCharts/VCandlestickModelMapper 2.2",
            "QtCharts/VCandlestickModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [514, 1536]
        Property {
            name: "timestampRow"
            type: "int"
            read: "timestampRow"
            write: "setTimestampRow"
            notify: "timestampRowChanged"
            index: 0
        }
        Property {
            name: "openRow"
            type: "int"
            read: "openRow"
            write: "setOpenRow"
            notify: "openRowChanged"
            index: 1
        }
        Property {
            name: "highRow"
            type: "int"
            read: "highRow"
            write: "setHighRow"
            notify: "highRowChanged"
            index: 2
        }
        Property {
            name: "lowRow"
            type: "int"
            read: "lowRow"
            write: "setLowRow"
            notify: "lowRowChanged"
            index: 3
        }
        Property {
            name: "closeRow"
            type: "int"
            read: "closeRow"
            write: "setCloseRow"
            notify: "closeRowChanged"
            index: 4
        }
        Property {
            name: "firstSetColumn"
            type: "int"
            read: "firstSetColumn"
            write: "setFirstSetColumn"
            notify: "firstSetColumnChanged"
            index: 5
        }
        Property {
            name: "lastSetColumn"
            type: "int"
            read: "lastSetColumn"
            write: "setLastSetColumn"
            notify: "lastSetColumnChanged"
            index: 6
        }
        Signal { name: "timestampRowChanged" }
        Signal { name: "openRowChanged" }
        Signal { name: "highRowChanged" }
        Signal { name: "lowRowChanged" }
        Signal { name: "closeRowChanged" }
        Signal { name: "firstSetColumnChanged" }
        Signal { name: "lastSetColumnChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QVPieModelMapper"
        accessSemantics: "reference"
        prototype: "QPieModelMapper"
        exports: [
            "QtCharts/VPieModelMapper 1.0",
            "QtCharts/VPieModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "series"
            type: "QPieSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesReplaced"
            index: 0
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelReplaced"
            index: 1
        }
        Property {
            name: "valuesColumn"
            type: "int"
            read: "valuesColumn"
            write: "setValuesColumn"
            notify: "valuesColumnChanged"
            index: 2
        }
        Property {
            name: "labelsColumn"
            type: "int"
            read: "labelsColumn"
            write: "setLabelsColumn"
            notify: "labelsColumnChanged"
            index: 3
        }
        Property {
            name: "firstRow"
            type: "int"
            read: "firstRow"
            write: "setFirstRow"
            notify: "firstRowChanged"
            index: 4
        }
        Property {
            name: "rowCount"
            type: "int"
            read: "rowCount"
            write: "setRowCount"
            notify: "rowCountChanged"
            index: 5
        }
        Signal { name: "seriesReplaced" }
        Signal { name: "modelReplaced" }
        Signal { name: "valuesColumnChanged" }
        Signal { name: "labelsColumnChanged" }
        Signal { name: "firstRowChanged" }
        Signal { name: "rowCountChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QVXYModelMapper"
        accessSemantics: "reference"
        prototype: "QXYModelMapper"
        exports: [
            "QtCharts/VXYModelMapper 1.0",
            "QtCharts/VXYModelMapper 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "series"
            type: "QXYSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesReplaced"
            index: 0
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelReplaced"
            index: 1
        }
        Property {
            name: "xColumn"
            type: "int"
            read: "xColumn"
            write: "setXColumn"
            notify: "xColumnChanged"
            index: 2
        }
        Property {
            name: "yColumn"
            type: "int"
            read: "yColumn"
            write: "setYColumn"
            notify: "yColumnChanged"
            index: 3
        }
        Property {
            name: "firstRow"
            type: "int"
            read: "firstRow"
            write: "setFirstRow"
            notify: "firstRowChanged"
            index: 4
        }
        Property {
            name: "rowCount"
            type: "int"
            read: "rowCount"
            write: "setRowCount"
            notify: "rowCountChanged"
            index: 5
        }
        Signal { name: "seriesReplaced" }
        Signal { name: "modelReplaced" }
        Signal { name: "xColumnChanged" }
        Signal { name: "yColumnChanged" }
        Signal { name: "firstRowChanged" }
        Signal { name: "rowCountChanged" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QXYModelMapper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtCharts/XYModelMapper 1.0", "QtCharts/XYModelMapper 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QXYSeries"
        accessSemantics: "reference"
        prototype: "QAbstractSeries"
        exports: [
            "QtCharts/XYSeries 1.0",
            "QtCharts/XYSeries 6.0",
            "QtCharts/XYSeries 6.2"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536, 1538]
        Enum {
            name: "PointConfiguration"
            values: [
                "Color",
                "Size",
                "Visibility",
                "LabelVisibility",
                "LabelFormat"
            ]
        }
        Property {
            name: "pointsVisible"
            type: "bool"
            read: "pointsVisible"
            write: "setPointsVisible"
            index: 0
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "selectedColor"
            revision: 1538
            type: "QColor"
            read: "color"
            write: "setSelectedColor"
            notify: "selectedColorChanged"
            index: 2
        }
        Property {
            name: "pointLabelsFormat"
            type: "QString"
            read: "pointLabelsFormat"
            write: "setPointLabelsFormat"
            notify: "pointLabelsFormatChanged"
            index: 3
        }
        Property {
            name: "pointLabelsVisible"
            type: "bool"
            read: "pointLabelsVisible"
            write: "setPointLabelsVisible"
            notify: "pointLabelsVisibilityChanged"
            index: 4
        }
        Property {
            name: "pointLabelsFont"
            type: "QFont"
            read: "pointLabelsFont"
            write: "setPointLabelsFont"
            notify: "pointLabelsFontChanged"
            index: 5
        }
        Property {
            name: "pointLabelsColor"
            type: "QColor"
            read: "pointLabelsColor"
            write: "setPointLabelsColor"
            notify: "pointLabelsColorChanged"
            index: 6
        }
        Property {
            name: "pointLabelsClipping"
            type: "bool"
            read: "pointLabelsClipping"
            write: "setPointLabelsClipping"
            notify: "pointLabelsClippingChanged"
            index: 7
        }
        Property {
            name: "bestFitLineVisible"
            revision: 1538
            type: "bool"
            read: "bestFitLineVisible"
            write: "setBestFitLineVisible"
            notify: "bestFitLineVisibilityChanged"
            index: 8
        }
        Property {
            name: "bestFitLineColor"
            revision: 1538
            type: "QColor"
            read: "bestFitLineColor"
            write: "setBestFitLineColor"
            notify: "bestFitLineColorChanged"
            index: 9
        }
        Signal {
            name: "clicked"
            Parameter { name: "point"; type: "QPointF" }
        }
        Signal {
            name: "hovered"
            Parameter { name: "point"; type: "QPointF" }
            Parameter { name: "state"; type: "bool" }
        }
        Signal {
            name: "pressed"
            Parameter { name: "point"; type: "QPointF" }
        }
        Signal {
            name: "released"
            Parameter { name: "point"; type: "QPointF" }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "point"; type: "QPointF" }
        }
        Signal {
            name: "pointReplaced"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "pointRemoved"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "pointAdded"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "selectedColorChanged"
            revision: 1538
            Parameter { name: "color"; type: "QColor" }
        }
        Signal { name: "pointsReplaced" }
        Signal {
            name: "pointLabelsFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "pointLabelsVisibilityChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "pointLabelsFontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "pointLabelsColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "pointLabelsClippingChanged"
            Parameter { name: "clipping"; type: "bool" }
        }
        Signal {
            name: "pointsRemoved"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "penChanged"
            Parameter { name: "pen"; type: "QPen" }
        }
        Signal { name: "selectedPointsChanged" }
        Signal {
            name: "lightMarkerChanged"
            revision: 1538
            Parameter { name: "lightMarker"; type: "QImage" }
        }
        Signal {
            name: "selectedLightMarkerChanged"
            revision: 1538
            Parameter { name: "selectedLightMarker"; type: "QImage" }
        }
        Signal {
            name: "bestFitLineVisibilityChanged"
            revision: 1538
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "bestFitLinePenChanged"
            revision: 1538
            Parameter { name: "pen"; type: "QPen" }
        }
        Signal {
            name: "bestFitLineColorChanged"
            revision: 1538
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "pointsConfigurationChanged"
            revision: 1538
            Parameter { name: "configuration"; type: "QHash<int,QHash<PointConfiguration,QVariant>>" }
        }
        Signal {
            name: "markerSizeChanged"
            Parameter { name: "size"; type: "double" }
        }
    }
    Component {
        file: "private/declarativeareaseries_p.h"
        name: "DeclarativeAreaSeries"
        accessSemantics: "reference"
        prototype: "QAreaSeries"
        exports: [
            "QtCharts/AreaSeries 1.0",
            "QtCharts/AreaSeries 1.1",
            "QtCharts/AreaSeries 1.2",
            "QtCharts/AreaSeries 1.3",
            "QtCharts/AreaSeries 1.4",
            "QtCharts/AreaSeries 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 259, 260, 1536]
        Property {
            name: "upperSeries"
            type: "DeclarativeLineSeries"
            isPointer: true
            read: "upperSeries"
            write: "setUpperSeries"
            index: 0
        }
        Property {
            name: "lowerSeries"
            type: "DeclarativeLineSeries"
            isPointer: true
            read: "lowerSeries"
            write: "setLowerSeries"
            index: 1
        }
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 2
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 3
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 4
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 5
        }
        Property {
            name: "axisAngular"
            revision: 259
            type: "QAbstractAxis"
            isPointer: true
            read: "axisAngular"
            write: "setAxisAngular"
            notify: "axisAngularChanged"
            index: 6
        }
        Property {
            name: "axisRadial"
            revision: 259
            type: "QAbstractAxis"
            isPointer: true
            read: "axisRadial"
            write: "setAxisRadial"
            notify: "axisRadialChanged"
            index: 7
        }
        Property {
            name: "borderWidth"
            revision: 257
            type: "double"
            read: "borderWidth"
            write: "setBorderWidth"
            notify: "borderWidthChanged"
            index: 8
        }
        Property {
            name: "brushFilename"
            revision: 260
            type: "QString"
            read: "brushFilename"
            write: "setBrushFilename"
            notify: "brushFilenameChanged"
            index: 9
        }
        Property {
            name: "brush"
            revision: 260
            type: "QBrush"
            read: "brush"
            write: "setBrush"
            notify: "brushChanged"
            index: 10
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "borderWidthChanged"
            revision: 257
            Parameter { name: "width"; type: "double" }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisAngularChanged"
            revision: 259
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisRadialChanged"
            revision: 259
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal { name: "brushChanged"; revision: 260 }
        Signal {
            name: "brushFilenameChanged"
            revision: 260
            Parameter { name: "brushFilename"; type: "QString" }
        }
        Method { name: "handleBrushChanged" }
    }
    Component {
        file: "private/declarativeaxes_p.h"
        name: "DeclarativeAxes"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtCharts/DeclarativeAxes 1.0",
            "QtCharts/DeclarativeAxes 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "axisX"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisXTop"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 2
        }
        Property {
            name: "axisYRight"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 3
        }
        Signal {
            name: "axisXChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
    }
    Component {
        file: "private/declarativebarseries_p.h"
        name: "DeclarativeBarSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QBarSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/BarSeries 1.0",
            "QtCharts/BarSeries 1.1",
            "QtCharts/BarSeries 1.2",
            "QtCharts/BarSeries 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 1536]
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 2
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 3
        }
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "appendSeriesChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "at"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "insert"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativebarseries_p.h"
        name: "DeclarativeBarSet"
        accessSemantics: "reference"
        prototype: "QBarSet"
        exports: [
            "QtCharts/BarSet 1.0",
            "QtCharts/BarSet 1.1",
            "QtCharts/BarSet 1.4",
            "QtCharts/BarSet 6.0",
            "QtCharts/BarSet 6.2"
        ]
        exportMetaObjectRevisions: [256, 257, 260, 1536, 1538]
        Property { name: "values"; type: "QVariantList"; read: "values"; write: "setValues"; index: 0 }
        Property {
            name: "borderWidth"
            revision: 257
            type: "double"
            read: "borderWidth"
            write: "setBorderWidth"
            notify: "borderWidthChanged"
            index: 1
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "brushFilename"
            revision: 260
            type: "QString"
            read: "brushFilename"
            write: "setBrushFilename"
            notify: "brushFilenameChanged"
            index: 3
        }
        Signal {
            name: "countChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "borderWidthChanged"
            revision: 257
            Parameter { name: "width"; type: "double" }
        }
        Signal {
            name: "brushFilenameChanged"
            revision: 260
            Parameter { name: "brushFilename"; type: "QString" }
        }
        Method {
            name: "handleCountChanged"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method { name: "handleBrushChanged" }
        Method {
            name: "append"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "remove"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "remove"
            isCloned: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "replace"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "at"
            type: "double"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/declarativeboxplotseries_p.h"
        name: "DeclarativeBoxPlotSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QBoxPlotSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/BoxPlotSeries 1.3",
            "QtCharts/BoxPlotSeries 1.4",
            "QtCharts/BoxPlotSeries 2.0",
            "QtCharts/BoxPlotSeries 6.0"
        ]
        exportMetaObjectRevisions: [259, 260, 512, 1536]
        Property {
            name: "axisX"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisXTop"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 2
        }
        Property {
            name: "axisYRight"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 3
        }
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 4
            isReadonly: true
        }
        Property {
            name: "brushFilename"
            revision: 260
            type: "QString"
            read: "brushFilename"
            write: "setBrushFilename"
            notify: "brushFilenameChanged"
            index: 5
        }
        Signal {
            name: "axisXChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "clicked"
            Parameter { name: "boxset"; type: "DeclarativeBoxSet"; isPointer: true }
        }
        Signal {
            name: "hovered"
            Parameter { name: "status"; type: "bool" }
            Parameter { name: "boxset"; type: "DeclarativeBoxSet"; isPointer: true }
        }
        Signal {
            name: "pressed"
            Parameter { name: "boxset"; type: "DeclarativeBoxSet"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "boxset"; type: "DeclarativeBoxSet"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "boxset"; type: "DeclarativeBoxSet"; isPointer: true }
        }
        Signal {
            name: "brushFilenameChanged"
            revision: 260
            Parameter { name: "brushFilename"; type: "QString" }
        }
        Method {
            name: "appendSeriesChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onHovered"
            Parameter { name: "status"; type: "bool" }
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Method {
            name: "onClicked"
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Method {
            name: "onPressed"
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Method {
            name: "onReleased"
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Method {
            name: "onDoubleClicked"
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Method { name: "handleBrushChanged" }
        Method {
            name: "at"
            type: "DeclarativeBoxSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            type: "DeclarativeBoxSet"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "append"
            Parameter { name: "box"; type: "DeclarativeBoxSet"; isPointer: true }
        }
        Method {
            name: "insert"
            type: "DeclarativeBoxSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "box"; type: "DeclarativeBoxSet"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativeboxplotseries_p.h"
        name: "DeclarativeBoxSet"
        accessSemantics: "reference"
        prototype: "QBoxSet"
        exports: [
            "QtCharts/BoxSet 1.3",
            "QtCharts/BoxSet 1.4",
            "QtCharts/BoxSet 6.0"
        ]
        exportMetaObjectRevisions: [259, 260, 1536]
        Enum {
            name: "ValuePositions"
            values: [
                "LowerExtreme",
                "LowerQuartile",
                "Median",
                "UpperQuartile",
                "UpperExtreme"
            ]
        }
        Property { name: "values"; type: "QVariantList"; read: "values"; write: "setValues"; index: 0 }
        Property { name: "label"; type: "QString"; read: "label"; write: "setLabel"; index: 1 }
        Property { name: "count"; type: "int"; read: "count"; index: 2; isReadonly: true }
        Property {
            name: "brushFilename"
            revision: 260
            type: "QString"
            read: "brushFilename"
            write: "setBrushFilename"
            notify: "brushFilenameChanged"
            index: 3
        }
        Signal { name: "changedValues" }
        Signal {
            name: "changedValue"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "brushFilenameChanged"
            revision: 260
            Parameter { name: "brushFilename"; type: "QString" }
        }
        Method { name: "handleBrushChanged" }
        Method {
            name: "append"
            Parameter { name: "value"; type: "double" }
        }
        Method { name: "clear" }
        Method {
            name: "at"
            type: "double"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setValue"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "value"; type: "double" }
        }
    }
    Component {
        file: "private/declarativecandlestickseries_p.h"
        name: "DeclarativeCandlestickSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QCandlestickSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/CandlestickSeries 2.2",
            "QtCharts/CandlestickSeries 6.0"
        ]
        exportMetaObjectRevisions: [514, 1536]
        Property {
            name: "axisX"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisXTop"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 2
        }
        Property {
            name: "axisYRight"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 3
        }
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 4
            isReadonly: true
        }
        Property {
            name: "brushFilename"
            type: "QString"
            read: "brushFilename"
            write: "setBrushFilename"
            notify: "brushFilenameChanged"
            index: 5
        }
        Signal {
            name: "axisXChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "clicked"
            Parameter { name: "set"; type: "DeclarativeCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "hovered"
            Parameter { name: "status"; type: "bool" }
            Parameter { name: "set"; type: "DeclarativeCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "pressed"
            Parameter { name: "set"; type: "DeclarativeCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "set"; type: "DeclarativeCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "set"; type: "DeclarativeCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "brushFilenameChanged"
            Parameter { name: "brushFilename"; type: "QString" }
        }
        Method {
            name: "appendSeriesChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onClicked"
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Method {
            name: "onHovered"
            Parameter { name: "status"; type: "bool" }
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Method {
            name: "onPressed"
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Method {
            name: "onReleased"
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Method {
            name: "onDoubleClicked"
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Method { name: "handleBrushChanged" }
        Method {
            name: "at"
            type: "DeclarativeCandlestickSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            type: "bool"
            Parameter { name: "set"; type: "DeclarativeCandlestickSet"; isPointer: true }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "set"; type: "DeclarativeCandlestickSet"; isPointer: true }
        }
        Method {
            name: "append"
            type: "bool"
            Parameter { name: "open"; type: "double" }
            Parameter { name: "high"; type: "double" }
            Parameter { name: "low"; type: "double" }
            Parameter { name: "close"; type: "double" }
            Parameter { name: "timestamp"; type: "double" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "timestamp"; type: "double" }
        }
        Method {
            name: "insert"
            type: "bool"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "set"; type: "DeclarativeCandlestickSet"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativecandlestickseries_p.h"
        name: "DeclarativeCandlestickSet"
        accessSemantics: "reference"
        prototype: "QCandlestickSet"
        exports: [
            "QtCharts/CandlestickSet 2.2",
            "QtCharts/CandlestickSet 6.0"
        ]
        exportMetaObjectRevisions: [514, 1536]
        Property {
            name: "brushFilename"
            type: "QString"
            read: "brushFilename"
            write: "setBrushFilename"
            notify: "brushFilenameChanged"
            index: 0
        }
        Signal {
            name: "brushFilenameChanged"
            Parameter { name: "brushFilename"; type: "QString" }
        }
        Method { name: "handleBrushChanged" }
    }
    Component {
        file: "private/declarativecategoryaxis_p.h"
        name: "DeclarativeCategoryAxis"
        accessSemantics: "reference"
        defaultProperty: "axisChildren"
        prototype: "QCategoryAxis"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/CategoryAxis 1.1",
            "QtCharts/CategoryAxis 2.1",
            "QtCharts/CategoryAxis 2.3",
            "QtCharts/CategoryAxis 6.0",
            "QtCharts/CategoryAxis 6.2"
        ]
        exportMetaObjectRevisions: [257, 513, 515, 1536, 1538]
        Enum {
            name: "AxisLabelsPosition"
            values: ["AxisLabelsPositionCenter", "AxisLabelsPositionOnValue"]
        }
        Property {
            name: "axisChildren"
            type: "QObject"
            isList: true
            read: "axisChildren"
            index: 0
            isReadonly: true
        }
        Property {
            name: "labelsPosition"
            revision: 513
            type: "AxisLabelsPosition"
            read: "labelsPosition"
            write: "setLabelsPosition"
            notify: "labelsPositionChanged"
            index: 1
        }
        Signal {
            name: "labelsPositionChanged"
            revision: 513
            Parameter { name: "position"; type: "AxisLabelsPosition" }
        }
        Method {
            name: "append"
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "categoryEndValue"; type: "double" }
        }
        Method {
            name: "remove"
            Parameter { name: "label"; type: "QString" }
        }
        Method {
            name: "replace"
            Parameter { name: "oldLabel"; type: "QString" }
            Parameter { name: "newLabel"; type: "QString" }
        }
        Method {
            name: "appendAxisChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/declarativecategoryaxis_p.h"
        name: "DeclarativeCategoryRange"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtCharts/CategoryRange 1.1", "QtCharts/CategoryRange 6.0"]
        exportMetaObjectRevisions: [257, 1536]
        Property { name: "endValue"; type: "double"; read: "endValue"; write: "setEndValue"; index: 0 }
        Property { name: "label"; type: "QString"; read: "label"; write: "setLabel"; index: 1 }
    }
    Component {
        file: "private/declarativechart_p.h"
        name: "DeclarativeChart"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtCharts/ChartView 1.0",
            "QtCharts/ChartView 1.1",
            "QtCharts/ChartView 1.2",
            "QtCharts/ChartView 1.3",
            "QtCharts/ChartView 2.0",
            "QtCharts/ChartView 2.1",
            "QtCharts/ChartView 2.4",
            "QtCharts/ChartView 2.7",
            "QtCharts/ChartView 2.11",
            "QtCharts/ChartView 6.0",
            "QtCharts/ChartView 6.3"
        ]
        exportMetaObjectRevisions: [
            256,
            257,
            258,
            259,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539
        ]
        Enum {
            name: "Theme"
            values: [
                "ChartThemeLight",
                "ChartThemeBlueCerulean",
                "ChartThemeDark",
                "ChartThemeBrownSand",
                "ChartThemeBlueNcs",
                "ChartThemeHighContrast",
                "ChartThemeBlueIcy",
                "ChartThemeQt"
            ]
        }
        Enum {
            name: "Animation"
            values: [
                "NoAnimation",
                "GridAxisAnimations",
                "SeriesAnimations",
                "AllAnimations"
            ]
        }
        Enum {
            name: "SeriesType"
            values: [
                "SeriesTypeLine",
                "SeriesTypeArea",
                "SeriesTypeBar",
                "SeriesTypeStackedBar",
                "SeriesTypePercentBar",
                "SeriesTypePie",
                "SeriesTypeScatter",
                "SeriesTypeSpline",
                "SeriesTypeHorizontalBar",
                "SeriesTypeHorizontalStackedBar",
                "SeriesTypeHorizontalPercentBar",
                "SeriesTypeBoxPlot",
                "SeriesTypeCandlestick"
            ]
        }
        Property { name: "theme"; type: "Theme"; read: "theme"; write: "setTheme"; index: 0 }
        Property {
            name: "animationOptions"
            type: "Animation"
            read: "animationOptions"
            write: "setAnimationOptions"
            index: 1
        }
        Property {
            name: "animationDuration"
            revision: 513
            type: "int"
            read: "animationDuration"
            write: "setAnimationDuration"
            notify: "animationDurationChanged"
            index: 2
        }
        Property {
            name: "animationEasingCurve"
            revision: 513
            type: "QEasingCurve"
            read: "animationEasingCurve"
            write: "setAnimationEasingCurve"
            notify: "animationEasingCurveChanged"
            index: 3
        }
        Property { name: "title"; type: "QString"; read: "title"; write: "setTitle"; index: 4 }
        Property { name: "titleFont"; type: "QFont"; read: "titleFont"; write: "setTitleFont"; index: 5 }
        Property {
            name: "titleColor"
            type: "QColor"
            read: "titleColor"
            write: "setTitleColor"
            notify: "titleColorChanged"
            index: 6
        }
        Property {
            name: "legend"
            type: "QLegend"
            isPointer: true
            read: "legend"
            index: 7
            isReadonly: true
            isConstant: true
        }
        Property { name: "count"; type: "int"; read: "count"; index: 8; isReadonly: true }
        Property {
            name: "backgroundColor"
            type: "QColor"
            read: "backgroundColor"
            write: "setBackgroundColor"
            notify: "backgroundColorChanged"
            index: 9
        }
        Property {
            name: "dropShadowEnabled"
            type: "bool"
            read: "dropShadowEnabled"
            write: "setDropShadowEnabled"
            notify: "dropShadowEnabledChanged"
            index: 10
        }
        Property {
            name: "backgroundRoundness"
            revision: 259
            type: "double"
            read: "backgroundRoundness"
            write: "setBackgroundRoundness"
            notify: "backgroundRoundnessChanged"
            index: 11
        }
        Property {
            name: "margins"
            revision: 258
            type: "DeclarativeMargins"
            isPointer: true
            read: "margins"
            notify: "marginsChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "plotArea"
            revision: 257
            type: "QRectF"
            read: "plotArea"
            write: "setPlotArea"
            notify: "plotAreaChanged"
            index: 13
        }
        Property {
            name: "plotAreaColor"
            revision: 259
            type: "QColor"
            read: "plotAreaColor"
            write: "setPlotAreaColor"
            notify: "plotAreaColorChanged"
            index: 14
        }
        Property {
            name: "axes"
            revision: 258
            type: "QAbstractAxis"
            isList: true
            read: "axes"
            index: 15
            isReadonly: true
        }
        Property {
            name: "localizeNumbers"
            revision: 512
            type: "bool"
            read: "localizeNumbers"
            write: "setLocalizeNumbers"
            notify: "localizeNumbersChanged"
            index: 16
        }
        Property {
            name: "locale"
            revision: 512
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            notify: "localeChanged"
            index: 17
        }
        Signal { name: "axisLabelsChanged" }
        Signal {
            name: "titleColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal { name: "backgroundColorChanged" }
        Signal {
            name: "dropShadowEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal { name: "marginsChanged"; revision: 258 }
        Signal {
            name: "plotAreaChanged"
            Parameter { name: "plotArea"; type: "QRectF" }
        }
        Signal {
            name: "seriesAdded"
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Signal {
            name: "seriesRemoved"
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Signal { name: "plotAreaColorChanged"; revision: 259 }
        Signal {
            name: "backgroundRoundnessChanged"
            revision: 259
            Parameter { name: "diameter"; type: "double" }
        }
        Signal { name: "localizeNumbersChanged"; revision: 512 }
        Signal { name: "localeChanged"; revision: 512 }
        Signal {
            name: "animationDurationChanged"
            revision: 513
            Parameter { name: "msecs"; type: "int" }
        }
        Signal {
            name: "animationEasingCurveChanged"
            revision: 513
            Parameter { name: "curve"; type: "QEasingCurve" }
        }
        Signal { name: "needRender" }
        Signal { name: "pendingRenderNodeMouseEventResponses" }
        Method {
            name: "handleAntialiasingChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Method {
            name: "sceneChanged"
            Parameter { name: "region"; type: "QRectF"; isList: true }
        }
        Method { name: "renderScene" }
        Method {
            name: "changeMargins"
            Parameter { name: "top"; type: "int" }
            Parameter { name: "bottom"; type: "int" }
            Parameter { name: "left"; type: "int" }
            Parameter { name: "right"; type: "int" }
        }
        Method {
            name: "handleAxisXSet"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYSet"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisXTopSet"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYRightSet"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "handleSeriesAdded"
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Method { name: "handlePendingRenderNodeMouseEventResponses" }
        Method {
            name: "series"
            type: "QAbstractSeries"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "series"
            type: "QAbstractSeries"
            isPointer: true
            Parameter { name: "seriesName"; type: "QString" }
        }
        Method {
            name: "createSeries"
            type: "QAbstractSeries"
            isPointer: true
            Parameter { name: "type"; type: "int" }
            Parameter { name: "name"; type: "QString" }
            Parameter { name: "axisX"; type: "QAbstractAxis"; isPointer: true }
            Parameter { name: "axisY"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "createSeries"
            type: "QAbstractSeries"
            isPointer: true
            isCloned: true
            Parameter { name: "type"; type: "int" }
            Parameter { name: "name"; type: "QString" }
            Parameter { name: "axisX"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "createSeries"
            type: "QAbstractSeries"
            isPointer: true
            isCloned: true
            Parameter { name: "type"; type: "int" }
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "createSeries"
            type: "QAbstractSeries"
            isPointer: true
            isCloned: true
            Parameter { name: "type"; type: "int" }
        }
        Method {
            name: "removeSeries"
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Method { name: "removeAllSeries" }
        Method {
            name: "setAxisX"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Method {
            name: "setAxisX"
            isCloned: true
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "setAxisY"
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Method {
            name: "setAxisY"
            isCloned: true
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "axisX"
            type: "QAbstractAxis"
            isPointer: true
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Method { name: "axisX"; type: "QAbstractAxis"; isPointer: true; isCloned: true }
        Method {
            name: "axisY"
            type: "QAbstractAxis"
            isPointer: true
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Method { name: "axisY"; type: "QAbstractAxis"; isPointer: true; isCloned: true }
        Method {
            name: "zoom"
            Parameter { name: "factor"; type: "double" }
        }
        Method { name: "zoomIn"; revision: 513 }
        Method {
            name: "zoomIn"
            revision: 513
            Parameter { name: "rectangle"; type: "QRectF" }
        }
        Method { name: "zoomOut"; revision: 513 }
        Method { name: "zoomReset"; revision: 513 }
        Method { name: "isZoomed"; revision: 513; type: "bool" }
        Method {
            name: "scrollLeft"
            Parameter { name: "pixels"; type: "double" }
        }
        Method {
            name: "scrollRight"
            Parameter { name: "pixels"; type: "double" }
        }
        Method {
            name: "scrollUp"
            Parameter { name: "pixels"; type: "double" }
        }
        Method {
            name: "scrollDown"
            Parameter { name: "pixels"; type: "double" }
        }
        Method {
            name: "mapToValue"
            revision: 513
            type: "QPointF"
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Method {
            name: "mapToValue"
            revision: 513
            type: "QPointF"
            isCloned: true
            Parameter { name: "position"; type: "QPointF" }
        }
        Method {
            name: "mapToPosition"
            revision: 513
            type: "QPointF"
            Parameter { name: "value"; type: "QPointF" }
            Parameter { name: "series"; type: "QAbstractSeries"; isPointer: true }
        }
        Method {
            name: "mapToPosition"
            revision: 513
            type: "QPointF"
            isCloned: true
            Parameter { name: "value"; type: "QPointF" }
        }
    }
    Component {
        file: "private/declarativebarseries_p.h"
        name: "DeclarativeHorizontalBarSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QHorizontalBarSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/HorizontalBarSeries 1.0",
            "QtCharts/HorizontalBarSeries 1.1",
            "QtCharts/HorizontalBarSeries 1.2",
            "QtCharts/HorizontalBarSeries 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 1536]
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 2
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 3
        }
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "appendSeriesChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "at"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "insert"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativebarseries_p.h"
        name: "DeclarativeHorizontalPercentBarSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QHorizontalPercentBarSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/HorizontalPercentBarSeries 1.0",
            "QtCharts/HorizontalPercentBarSeries 1.1",
            "QtCharts/HorizontalPercentBarSeries 1.2",
            "QtCharts/HorizontalPercentBarSeries 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 1536]
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 2
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 3
        }
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "appendSeriesChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "at"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "insert"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativebarseries_p.h"
        name: "DeclarativeHorizontalStackedBarSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QHorizontalStackedBarSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/HorizontalStackedBarSeries 1.0",
            "QtCharts/HorizontalStackedBarSeries 1.1",
            "QtCharts/HorizontalStackedBarSeries 1.2",
            "QtCharts/HorizontalStackedBarSeries 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 1536]
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 2
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 3
        }
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "appendSeriesChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "at"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "insert"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativelineseries_p.h"
        name: "DeclarativeLineSeries"
        accessSemantics: "reference"
        defaultProperty: "declarativeChildren"
        prototype: "QLineSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/LineSeries 1.0",
            "QtCharts/LineSeries 1.1",
            "QtCharts/LineSeries 1.2",
            "QtCharts/LineSeries 1.3",
            "QtCharts/LineSeries 2.1",
            "QtCharts/LineSeries 6.0",
            "QtCharts/LineSeries 6.2"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 259, 513, 1536, 1538]
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 1
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 2
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 3
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 4
        }
        Property {
            name: "axisAngular"
            revision: 259
            type: "QAbstractAxis"
            isPointer: true
            read: "axisAngular"
            write: "setAxisAngular"
            notify: "axisAngularChanged"
            index: 5
        }
        Property {
            name: "axisRadial"
            revision: 259
            type: "QAbstractAxis"
            isPointer: true
            read: "axisRadial"
            write: "setAxisRadial"
            notify: "axisRadialChanged"
            index: 6
        }
        Property {
            name: "width"
            revision: 257
            type: "double"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 7
        }
        Property {
            name: "style"
            revision: 257
            type: "Qt::PenStyle"
            read: "style"
            write: "setStyle"
            notify: "styleChanged"
            index: 8
        }
        Property {
            name: "capStyle"
            revision: 257
            type: "Qt::PenCapStyle"
            read: "capStyle"
            write: "setCapStyle"
            notify: "capStyleChanged"
            index: 9
        }
        Property {
            name: "declarativeChildren"
            type: "QObject"
            isList: true
            read: "declarativeChildren"
            index: 10
            isReadonly: true
        }
        Signal {
            name: "countChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisAngularChanged"
            revision: 259
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisRadialChanged"
            revision: 259
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "widthChanged"
            revision: 257
            Parameter { name: "width"; type: "double" }
        }
        Signal {
            name: "styleChanged"
            revision: 257
            Parameter { name: "style"; type: "Qt::PenStyle" }
        }
        Signal {
            name: "capStyleChanged"
            revision: 257
            Parameter { name: "capStyle"; type: "Qt::PenCapStyle" }
        }
        Method {
            name: "appendDeclarativeChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "handleCountChanged"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "replace"
            Parameter { name: "oldX"; type: "double" }
            Parameter { name: "oldY"; type: "double" }
            Parameter { name: "newX"; type: "double" }
            Parameter { name: "newY"; type: "double" }
        }
        Method {
            name: "replace"
            revision: 259
            Parameter { name: "index"; type: "int" }
            Parameter { name: "newX"; type: "double" }
            Parameter { name: "newY"; type: "double" }
        }
        Method {
            name: "remove"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "remove"
            revision: 259
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "removePoints"
            revision: 513
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "insert"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method { name: "clear" }
        Method {
            name: "at"
            type: "QPointF"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/declarativemargins_p.h"
        name: "DeclarativeMargins"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtCharts/Margins 1.1", "QtCharts/Margins 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536]
        Property { name: "top"; type: "int"; read: "top"; write: "setTop"; notify: "topChanged"; index: 0 }
        Property {
            name: "bottom"
            type: "int"
            read: "bottom"
            write: "setBottom"
            notify: "bottomChanged"
            index: 1
        }
        Property {
            name: "left"
            type: "int"
            read: "left"
            write: "setLeft"
            notify: "leftChanged"
            index: 2
        }
        Property {
            name: "right"
            type: "int"
            read: "right"
            write: "setRight"
            notify: "rightChanged"
            index: 3
        }
        Signal {
            name: "topChanged"
            Parameter { name: "top"; type: "int" }
            Parameter { name: "bottom"; type: "int" }
            Parameter { name: "left"; type: "int" }
            Parameter { name: "right"; type: "int" }
        }
        Signal {
            name: "bottomChanged"
            Parameter { name: "top"; type: "int" }
            Parameter { name: "bottom"; type: "int" }
            Parameter { name: "left"; type: "int" }
            Parameter { name: "right"; type: "int" }
        }
        Signal {
            name: "leftChanged"
            Parameter { name: "top"; type: "int" }
            Parameter { name: "bottom"; type: "int" }
            Parameter { name: "left"; type: "int" }
            Parameter { name: "right"; type: "int" }
        }
        Signal {
            name: "rightChanged"
            Parameter { name: "top"; type: "int" }
            Parameter { name: "bottom"; type: "int" }
            Parameter { name: "left"; type: "int" }
            Parameter { name: "right"; type: "int" }
        }
    }
    Component {
        file: "private/declarativebarseries_p.h"
        name: "DeclarativePercentBarSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QPercentBarSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/PercentBarSeries 1.0",
            "QtCharts/PercentBarSeries 1.1",
            "QtCharts/PercentBarSeries 1.2",
            "QtCharts/PercentBarSeries 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 1536]
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 2
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 3
        }
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "appendSeriesChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "at"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "insert"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativepieseries_p.h"
        name: "DeclarativePieSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QPieSeries"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtCharts/PieSeries 1.0", "QtCharts/PieSeries 6.0"]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "sliceAdded"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Signal {
            name: "sliceRemoved"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Method {
            name: "appendSeriesChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "handleAdded"
            Parameter { name: "slices"; type: "QList<QPieSlice*>" }
        }
        Method {
            name: "handleRemoved"
            Parameter { name: "slices"; type: "QList<QPieSlice*>" }
        }
        Method {
            name: "at"
            type: "QPieSlice"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "find"
            type: "QPieSlice"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
        }
        Method {
            name: "append"
            type: "DeclarativePieSlice"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativepieseries_p.h"
        name: "DeclarativePieSlice"
        accessSemantics: "reference"
        prototype: "QPieSlice"
        exports: ["QtCharts/PieSlice 1.0", "QtCharts/PieSlice 6.0"]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "brushFilename"
            type: "QString"
            read: "brushFilename"
            write: "setBrushFilename"
            notify: "brushFilenameChanged"
            index: 0
        }
        Signal {
            name: "brushFilenameChanged"
            Parameter { name: "brushFilename"; type: "QString" }
        }
        Method { name: "handleBrushChanged" }
    }
    Component {
        file: "private/declarativepolarchart_p.h"
        name: "DeclarativePolarChart"
        accessSemantics: "reference"
        prototype: "DeclarativeChart"
        exports: [
            "QtCharts/PolarChartView 1.3",
            "QtCharts/PolarChartView 2.0",
            "QtCharts/PolarChartView 2.1",
            "QtCharts/PolarChartView 2.4",
            "QtCharts/PolarChartView 2.7",
            "QtCharts/PolarChartView 2.11",
            "QtCharts/PolarChartView 6.0",
            "QtCharts/PolarChartView 6.3"
        ]
        exportMetaObjectRevisions: [259, 512, 513, 516, 519, 523, 1536, 1539]
    }
    Component {
        file: "private/declarativescatterseries_p.h"
        name: "DeclarativeScatterSeries"
        accessSemantics: "reference"
        defaultProperty: "declarativeChildren"
        prototype: "QScatterSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/ScatterSeries 1.0",
            "QtCharts/ScatterSeries 1.1",
            "QtCharts/ScatterSeries 1.2",
            "QtCharts/ScatterSeries 1.3",
            "QtCharts/ScatterSeries 1.4",
            "QtCharts/ScatterSeries 2.1",
            "QtCharts/ScatterSeries 6.0",
            "QtCharts/ScatterSeries 6.2"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 259, 260, 513, 1536, 1538]
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 1
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 2
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 3
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 4
        }
        Property {
            name: "axisAngular"
            revision: 259
            type: "QAbstractAxis"
            isPointer: true
            read: "axisAngular"
            write: "setAxisAngular"
            notify: "axisAngularChanged"
            index: 5
        }
        Property {
            name: "axisRadial"
            revision: 259
            type: "QAbstractAxis"
            isPointer: true
            read: "axisRadial"
            write: "setAxisRadial"
            notify: "axisRadialChanged"
            index: 6
        }
        Property {
            name: "borderWidth"
            revision: 257
            type: "double"
            read: "borderWidth"
            write: "setBorderWidth"
            notify: "borderWidthChanged"
            index: 7
        }
        Property {
            name: "declarativeChildren"
            type: "QObject"
            isList: true
            read: "declarativeChildren"
            index: 8
            isReadonly: true
        }
        Property {
            name: "brushFilename"
            revision: 260
            type: "QString"
            read: "brushFilename"
            write: "setBrushFilename"
            notify: "brushFilenameChanged"
            index: 9
        }
        Property {
            name: "brush"
            revision: 260
            type: "QBrush"
            read: "brush"
            write: "setBrush"
            notify: "brushChanged"
            index: 10
        }
        Signal {
            name: "countChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "borderWidthChanged"
            revision: 257
            Parameter { name: "width"; type: "double" }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisAngularChanged"
            revision: 259
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisRadialChanged"
            revision: 259
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "brushFilenameChanged"
            revision: 260
            Parameter { name: "brushFilename"; type: "QString" }
        }
        Signal { name: "brushChanged"; revision: 260 }
        Method {
            name: "appendDeclarativeChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "handleCountChanged"
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "handleBrushChanged" }
        Method {
            name: "append"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "replace"
            Parameter { name: "oldX"; type: "double" }
            Parameter { name: "oldY"; type: "double" }
            Parameter { name: "newX"; type: "double" }
            Parameter { name: "newY"; type: "double" }
        }
        Method {
            name: "replace"
            revision: 259
            Parameter { name: "index"; type: "int" }
            Parameter { name: "newX"; type: "double" }
            Parameter { name: "newY"; type: "double" }
        }
        Method {
            name: "remove"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "remove"
            revision: 259
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "removePoints"
            revision: 513
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "insert"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method { name: "clear" }
        Method {
            name: "at"
            type: "QPointF"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/declarativesplineseries_p.h"
        name: "DeclarativeSplineSeries"
        accessSemantics: "reference"
        defaultProperty: "declarativeChildren"
        prototype: "QSplineSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/SplineSeries 1.0",
            "QtCharts/SplineSeries 1.1",
            "QtCharts/SplineSeries 1.2",
            "QtCharts/SplineSeries 1.3",
            "QtCharts/SplineSeries 2.1",
            "QtCharts/SplineSeries 6.0",
            "QtCharts/SplineSeries 6.2"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 259, 513, 1536, 1538]
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 1
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 2
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 3
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 4
        }
        Property {
            name: "axisAngular"
            revision: 259
            type: "QAbstractAxis"
            isPointer: true
            read: "axisAngular"
            write: "setAxisAngular"
            notify: "axisAngularChanged"
            index: 5
        }
        Property {
            name: "axisRadial"
            revision: 259
            type: "QAbstractAxis"
            isPointer: true
            read: "axisRadial"
            write: "setAxisRadial"
            notify: "axisRadialChanged"
            index: 6
        }
        Property {
            name: "width"
            revision: 257
            type: "double"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 7
        }
        Property {
            name: "style"
            revision: 257
            type: "Qt::PenStyle"
            read: "style"
            write: "setStyle"
            notify: "styleChanged"
            index: 8
        }
        Property {
            name: "capStyle"
            revision: 257
            type: "Qt::PenCapStyle"
            read: "capStyle"
            write: "setCapStyle"
            notify: "capStyleChanged"
            index: 9
        }
        Property {
            name: "declarativeChildren"
            type: "QObject"
            isList: true
            read: "declarativeChildren"
            index: 10
            isReadonly: true
        }
        Signal {
            name: "countChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisAngularChanged"
            revision: 259
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisRadialChanged"
            revision: 259
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "widthChanged"
            revision: 257
            Parameter { name: "width"; type: "double" }
        }
        Signal {
            name: "styleChanged"
            revision: 257
            Parameter { name: "style"; type: "Qt::PenStyle" }
        }
        Signal {
            name: "capStyleChanged"
            revision: 257
            Parameter { name: "capStyle"; type: "Qt::PenCapStyle" }
        }
        Method {
            name: "appendDeclarativeChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "handleCountChanged"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "replace"
            Parameter { name: "oldX"; type: "double" }
            Parameter { name: "oldY"; type: "double" }
            Parameter { name: "newX"; type: "double" }
            Parameter { name: "newY"; type: "double" }
        }
        Method {
            name: "replace"
            revision: 259
            Parameter { name: "index"; type: "int" }
            Parameter { name: "newX"; type: "double" }
            Parameter { name: "newY"; type: "double" }
        }
        Method {
            name: "remove"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "remove"
            revision: 259
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "removePoints"
            revision: 513
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "insert"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method { name: "clear" }
        Method {
            name: "at"
            type: "QPointF"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/declarativebarseries_p.h"
        name: "DeclarativeStackedBarSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QStackedBarSeries"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtCharts/StackedBarSeries 1.0",
            "QtCharts/StackedBarSeries 1.1",
            "QtCharts/StackedBarSeries 1.2",
            "QtCharts/StackedBarSeries 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 258, 1536]
        Property {
            name: "axisX"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            revision: 257
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisXTop"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisXTop"
            write: "setAxisXTop"
            notify: "axisXTopChanged"
            index: 2
        }
        Property {
            name: "axisYRight"
            revision: 258
            type: "QAbstractAxis"
            isPointer: true
            read: "axisYRight"
            write: "setAxisYRight"
            notify: "axisYRightChanged"
            index: 3
        }
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "axisXChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            revision: 257
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisXTopChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Signal {
            name: "axisYRightChanged"
            revision: 258
            Parameter { name: "axis"; type: "QAbstractAxis"; isPointer: true }
        }
        Method {
            name: "appendSeriesChildren"
            Parameter { name: "list"; type: "QQmlListProperty<QObject>"; isPointer: true }
            Parameter { name: "element"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "at"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "insert"
            type: "DeclarativeBarSet"
            isPointer: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "values"; type: "QVariantList" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/declarativexypoint_p.h"
        name: "DeclarativeXYPoint"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtCharts/XYPoint 1.0", "QtCharts/XYPoint 6.0"]
        exportMetaObjectRevisions: [256, 1536]
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0 }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1 }
    }
    Component {
        file: "qareaseries.h"
        name: "QAreaSeries"
        accessSemantics: "reference"
        prototype: "QAbstractSeries"
        Property {
            name: "upperSeries"
            type: "QLineSeries"
            isPointer: true
            read: "upperSeries"
            index: 0
            isReadonly: true
        }
        Property {
            name: "lowerSeries"
            type: "QLineSeries"
            isPointer: true
            read: "lowerSeries"
            index: 1
            isReadonly: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "borderColor"
            type: "QColor"
            read: "borderColor"
            write: "setBorderColor"
            notify: "borderColorChanged"
            index: 3
        }
        Property {
            name: "pointLabelsFormat"
            type: "QString"
            read: "pointLabelsFormat"
            write: "setPointLabelsFormat"
            notify: "pointLabelsFormatChanged"
            index: 4
        }
        Property {
            name: "pointLabelsVisible"
            type: "bool"
            read: "pointLabelsVisible"
            write: "setPointLabelsVisible"
            notify: "pointLabelsVisibilityChanged"
            index: 5
        }
        Property {
            name: "pointLabelsFont"
            type: "QFont"
            read: "pointLabelsFont"
            write: "setPointLabelsFont"
            notify: "pointLabelsFontChanged"
            index: 6
        }
        Property {
            name: "pointLabelsColor"
            type: "QColor"
            read: "pointLabelsColor"
            write: "setPointLabelsColor"
            notify: "pointLabelsColorChanged"
            index: 7
        }
        Property {
            name: "pointLabelsClipping"
            type: "bool"
            read: "pointLabelsClipping"
            write: "setPointLabelsClipping"
            notify: "pointLabelsClippingChanged"
            index: 8
        }
        Signal {
            name: "clicked"
            Parameter { name: "point"; type: "QPointF" }
        }
        Signal {
            name: "hovered"
            Parameter { name: "point"; type: "QPointF" }
            Parameter { name: "state"; type: "bool" }
        }
        Signal {
            name: "pressed"
            Parameter { name: "point"; type: "QPointF" }
        }
        Signal {
            name: "released"
            Parameter { name: "point"; type: "QPointF" }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "point"; type: "QPointF" }
        }
        Signal { name: "selected" }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "borderColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "pointLabelsFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "pointLabelsVisibilityChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "pointLabelsFontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "pointLabelsColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "pointLabelsClippingChanged"
            Parameter { name: "clipping"; type: "bool" }
        }
    }
    Component {
        file: "qbarseries.h"
        name: "QBarSeries"
        accessSemantics: "reference"
        prototype: "QAbstractBarSeries"
    }
    Component {
        file: "qbarset.h"
        name: "QBarSet"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "label"
            type: "QString"
            read: "label"
            write: "setLabel"
            notify: "labelChanged"
            index: 0
        }
        Property { name: "pen"; type: "QPen"; read: "pen"; write: "setPen"; notify: "penChanged"; index: 1 }
        Property {
            name: "brush"
            type: "QBrush"
            read: "brush"
            write: "setBrush"
            notify: "brushChanged"
            index: 2
        }
        Property {
            name: "labelBrush"
            type: "QBrush"
            read: "labelBrush"
            write: "setLabelBrush"
            notify: "labelBrushChanged"
            index: 3
        }
        Property {
            name: "labelFont"
            type: "QFont"
            read: "labelFont"
            write: "setLabelFont"
            notify: "labelFontChanged"
            index: 4
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 5
        }
        Property {
            name: "borderColor"
            type: "QColor"
            read: "borderColor"
            write: "setBorderColor"
            notify: "borderColorChanged"
            index: 6
        }
        Property {
            name: "labelColor"
            type: "QColor"
            read: "labelColor"
            write: "setLabelColor"
            notify: "labelColorChanged"
            index: 7
        }
        Signal {
            name: "clicked"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "hovered"
            Parameter { name: "status"; type: "bool" }
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "pressed"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "released"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "index"; type: "int" }
        }
        Signal { name: "penChanged" }
        Signal { name: "brushChanged" }
        Signal { name: "labelChanged" }
        Signal { name: "labelBrushChanged" }
        Signal { name: "labelFontChanged" }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "borderColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "labelColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "selectedColorChanged"
            revision: 1538
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "valuesAdded"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "valuesRemoved"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "selectedBarsChanged"
            revision: 1538
            Parameter { name: "indexes"; type: "int"; isList: true }
        }
    }
    Component {
        file: "qboxplotseries.h"
        name: "QBoxPlotSeries"
        accessSemantics: "reference"
        prototype: "QAbstractSeries"
        Property {
            name: "boxOutlineVisible"
            type: "bool"
            read: "boxOutlineVisible"
            write: "setBoxOutlineVisible"
            notify: "boxOutlineVisibilityChanged"
            index: 0
        }
        Property {
            name: "boxWidth"
            type: "double"
            read: "boxWidth"
            write: "setBoxWidth"
            notify: "boxWidthChanged"
            index: 1
        }
        Property { name: "pen"; type: "QPen"; read: "pen"; write: "setPen"; notify: "penChanged"; index: 2 }
        Property {
            name: "brush"
            type: "QBrush"
            read: "brush"
            write: "setBrush"
            notify: "brushChanged"
            index: 3
        }
        Property {
            name: "count"
            revision: 512
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "clicked"
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Signal {
            name: "hovered"
            Parameter { name: "status"; type: "bool" }
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Signal {
            name: "pressed"
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "boxset"; type: "QBoxSet"; isPointer: true }
        }
        Signal { name: "countChanged" }
        Signal { name: "penChanged" }
        Signal { name: "brushChanged" }
        Signal { name: "boxOutlineVisibilityChanged" }
        Signal { name: "boxWidthChanged" }
        Signal {
            name: "boxsetsAdded"
            Parameter { name: "sets"; type: "QList<QBoxSet*>" }
        }
        Signal {
            name: "boxsetsRemoved"
            Parameter { name: "sets"; type: "QList<QBoxSet*>" }
        }
    }
    Component {
        file: "qboxset.h"
        name: "QBoxSet"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "pen"; type: "QPen"; read: "pen"; write: "setPen"; notify: "penChanged"; index: 0 }
        Property {
            name: "brush"
            type: "QBrush"
            read: "brush"
            write: "setBrush"
            notify: "brushChanged"
            index: 1
        }
        Signal { name: "clicked" }
        Signal {
            name: "hovered"
            Parameter { name: "status"; type: "bool" }
        }
        Signal { name: "pressed" }
        Signal { name: "released" }
        Signal { name: "doubleClicked" }
        Signal { name: "penChanged" }
        Signal { name: "brushChanged" }
        Signal { name: "valuesChanged" }
        Signal {
            name: "valueChanged"
            Parameter { name: "index"; type: "int" }
        }
        Signal { name: "cleared" }
    }
    Component {
        file: "qcandlestickseries.h"
        name: "QCandlestickSeries"
        accessSemantics: "reference"
        prototype: "QAbstractSeries"
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "maximumColumnWidth"
            type: "double"
            read: "maximumColumnWidth"
            write: "setMaximumColumnWidth"
            notify: "maximumColumnWidthChanged"
            index: 1
        }
        Property {
            name: "minimumColumnWidth"
            type: "double"
            read: "minimumColumnWidth"
            write: "setMinimumColumnWidth"
            notify: "minimumColumnWidthChanged"
            index: 2
        }
        Property {
            name: "bodyWidth"
            type: "double"
            read: "bodyWidth"
            write: "setBodyWidth"
            notify: "bodyWidthChanged"
            index: 3
        }
        Property {
            name: "bodyOutlineVisible"
            type: "bool"
            read: "bodyOutlineVisible"
            write: "setBodyOutlineVisible"
            notify: "bodyOutlineVisibilityChanged"
            index: 4
        }
        Property {
            name: "capsWidth"
            type: "double"
            read: "capsWidth"
            write: "setCapsWidth"
            notify: "capsWidthChanged"
            index: 5
        }
        Property {
            name: "capsVisible"
            type: "bool"
            read: "capsVisible"
            write: "setCapsVisible"
            notify: "capsVisibilityChanged"
            index: 6
        }
        Property {
            name: "increasingColor"
            type: "QColor"
            read: "increasingColor"
            write: "setIncreasingColor"
            notify: "increasingColorChanged"
            index: 7
        }
        Property {
            name: "decreasingColor"
            type: "QColor"
            read: "decreasingColor"
            write: "setDecreasingColor"
            notify: "decreasingColorChanged"
            index: 8
        }
        Property {
            name: "brush"
            type: "QBrush"
            read: "brush"
            write: "setBrush"
            notify: "brushChanged"
            index: 9
        }
        Property { name: "pen"; type: "QPen"; read: "pen"; write: "setPen"; notify: "penChanged"; index: 10 }
        Signal {
            name: "clicked"
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "hovered"
            Parameter { name: "status"; type: "bool" }
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "pressed"
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "set"; type: "QCandlestickSet"; isPointer: true }
        }
        Signal {
            name: "candlestickSetsAdded"
            Parameter { name: "sets"; type: "QList<QCandlestickSet*>" }
        }
        Signal {
            name: "candlestickSetsRemoved"
            Parameter { name: "sets"; type: "QList<QCandlestickSet*>" }
        }
        Signal { name: "countChanged" }
        Signal { name: "maximumColumnWidthChanged" }
        Signal { name: "minimumColumnWidthChanged" }
        Signal { name: "bodyWidthChanged" }
        Signal { name: "bodyOutlineVisibilityChanged" }
        Signal { name: "capsWidthChanged" }
        Signal { name: "capsVisibilityChanged" }
        Signal { name: "increasingColorChanged" }
        Signal { name: "decreasingColorChanged" }
        Signal { name: "brushChanged" }
        Signal { name: "penChanged" }
    }
    Component {
        file: "qcandlestickset.h"
        name: "QCandlestickSet"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "timestamp"
            type: "double"
            read: "timestamp"
            write: "setTimestamp"
            notify: "timestampChanged"
            index: 0
        }
        Property {
            name: "open"
            type: "double"
            read: "open"
            write: "setOpen"
            notify: "openChanged"
            index: 1
        }
        Property {
            name: "high"
            type: "double"
            read: "high"
            write: "setHigh"
            notify: "highChanged"
            index: 2
        }
        Property { name: "low"; type: "double"; read: "low"; write: "setLow"; notify: "lowChanged"; index: 3 }
        Property {
            name: "close"
            type: "double"
            read: "close"
            write: "setClose"
            notify: "closeChanged"
            index: 4
        }
        Property {
            name: "brush"
            type: "QBrush"
            read: "brush"
            write: "setBrush"
            notify: "brushChanged"
            index: 5
        }
        Property { name: "pen"; type: "QPen"; read: "pen"; write: "setPen"; notify: "penChanged"; index: 6 }
        Signal { name: "clicked" }
        Signal {
            name: "hovered"
            Parameter { name: "status"; type: "bool" }
        }
        Signal { name: "pressed" }
        Signal { name: "released" }
        Signal { name: "doubleClicked" }
        Signal { name: "timestampChanged" }
        Signal { name: "openChanged" }
        Signal { name: "highChanged" }
        Signal { name: "lowChanged" }
        Signal { name: "closeChanged" }
        Signal { name: "brushChanged" }
        Signal { name: "penChanged" }
    }
    Component {
        file: "qcategoryaxis.h"
        name: "QCategoryAxis"
        accessSemantics: "reference"
        prototype: "QValueAxis"
        Enum {
            name: "AxisLabelsPosition"
            values: ["AxisLabelsPositionCenter", "AxisLabelsPositionOnValue"]
        }
        Property {
            name: "startValue"
            type: "double"
            read: "startValue"
            write: "setStartValue"
            index: 0
        }
        Property { name: "count"; type: "int"; read: "count"; index: 1; isReadonly: true }
        Property {
            name: "categoriesLabels"
            type: "QStringList"
            read: "categoriesLabels"
            index: 2
            isReadonly: true
        }
        Property {
            name: "labelsPosition"
            type: "AxisLabelsPosition"
            read: "labelsPosition"
            write: "setLabelsPosition"
            notify: "labelsPositionChanged"
            index: 3
        }
        Signal { name: "categoriesChanged" }
        Signal {
            name: "labelsPositionChanged"
            Parameter { name: "position"; type: "QCategoryAxis::AxisLabelsPosition" }
        }
    }
    Component {
        file: "qgraphicsitem.h"
        name: "QGraphicsObject"
        accessSemantics: "reference"
        defaultProperty: "children"
        prototype: "QObject"
        interfaces: ["QGraphicsItem"]
        Property {
            name: "parent"
            type: "QGraphicsObject"
            isPointer: true
            read: "parentObject"
            write: "setParentItem"
            notify: "parentChanged"
            index: 0
        }
        Property {
            name: "opacity"
            type: "double"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 2
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 3
            isFinal: true
        }
        Property { name: "pos"; type: "QPointF"; read: "pos"; write: "setPos"; index: 4; isFinal: true }
        Property {
            name: "x"
            type: "double"
            read: "x"
            write: "setX"
            notify: "xChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "y"
            type: "double"
            read: "y"
            write: "setY"
            notify: "yChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "z"
            type: "double"
            read: "zValue"
            write: "setZValue"
            notify: "zChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "rotation"
            type: "double"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 8
        }
        Property {
            name: "scale"
            type: "double"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 9
        }
        Property {
            name: "transformOriginPoint"
            type: "QPointF"
            read: "transformOriginPoint"
            write: "setTransformOriginPoint"
            index: 10
        }
        Property {
            name: "effect"
            type: "QGraphicsEffect"
            isPointer: true
            read: "graphicsEffect"
            write: "setGraphicsEffect"
            index: 11
        }
        Property {
            name: "children"
            type: "QDeclarativeListProperty<QGraphicsObject>"
            read: "childrenList"
            notify: "childrenChanged"
            index: 12
            privateClass: "QGraphicsItemPrivate"
            isReadonly: true
        }
        Property {
            name: "width"
            type: "double"
            read: "width"
            write: "setWidth"
            reset: "resetWidth"
            notify: "widthChanged"
            index: 13
            privateClass: "QGraphicsItemPrivate"
            isFinal: true
        }
        Property {
            name: "height"
            type: "double"
            read: "height"
            write: "setHeight"
            reset: "resetHeight"
            notify: "heightChanged"
            index: 14
            privateClass: "QGraphicsItemPrivate"
            isFinal: true
        }
        Signal { name: "parentChanged" }
        Signal { name: "opacityChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "enabledChanged" }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "zChanged" }
        Signal { name: "rotationChanged" }
        Signal { name: "scaleChanged" }
        Signal { name: "childrenChanged" }
        Signal { name: "widthChanged" }
        Signal { name: "heightChanged" }
        Method { name: "updateMicroFocus" }
    }
    Component {
        file: "qgraphicswidget.h"
        name: "QGraphicsWidget"
        accessSemantics: "reference"
        defaultProperty: "children"
        prototype: "QGraphicsObject"
        interfaces: ["QGraphicsItem", "QGraphicsLayoutItem"]
        Property { name: "palette"; type: "QPalette"; read: "palette"; write: "setPalette"; index: 0 }
        Property { name: "font"; type: "QFont"; read: "font"; write: "setFont"; index: 1 }
        Property {
            name: "layoutDirection"
            type: "Qt::LayoutDirection"
            read: "layoutDirection"
            write: "setLayoutDirection"
            reset: "unsetLayoutDirection"
            index: 2
        }
        Property {
            name: "size"
            type: "QSizeF"
            read: "size"
            write: "resize"
            notify: "geometryChanged"
            index: 3
        }
        Property {
            name: "minimumSize"
            type: "QSizeF"
            read: "minimumSize"
            write: "setMinimumSize"
            index: 4
        }
        Property {
            name: "preferredSize"
            type: "QSizeF"
            read: "preferredSize"
            write: "setPreferredSize"
            index: 5
        }
        Property {
            name: "maximumSize"
            type: "QSizeF"
            read: "maximumSize"
            write: "setMaximumSize"
            index: 6
        }
        Property {
            name: "sizePolicy"
            type: "QSizePolicy"
            read: "sizePolicy"
            write: "setSizePolicy"
            index: 7
        }
        Property {
            name: "focusPolicy"
            type: "Qt::FocusPolicy"
            read: "focusPolicy"
            write: "setFocusPolicy"
            index: 8
        }
        Property {
            name: "windowFlags"
            type: "Qt::WindowFlags"
            read: "windowFlags"
            write: "setWindowFlags"
            index: 9
        }
        Property {
            name: "windowTitle"
            type: "QString"
            read: "windowTitle"
            write: "setWindowTitle"
            index: 10
        }
        Property {
            name: "geometry"
            type: "QRectF"
            read: "geometry"
            write: "setGeometry"
            notify: "geometryChanged"
            index: 11
        }
        Property {
            name: "autoFillBackground"
            type: "bool"
            read: "autoFillBackground"
            write: "setAutoFillBackground"
            index: 12
        }
        Property {
            name: "layout"
            type: "QGraphicsLayout"
            isPointer: true
            read: "layout"
            write: "setLayout"
            notify: "layoutChanged"
            index: 13
        }
        Signal { name: "geometryChanged" }
        Signal { name: "layoutChanged" }
        Method { name: "close"; type: "bool" }
    }
    Component {
        file: "qhorizontalbarseries.h"
        name: "QHorizontalBarSeries"
        accessSemantics: "reference"
        prototype: "QAbstractBarSeries"
    }
    Component {
        file: "qhorizontalpercentbarseries.h"
        name: "QHorizontalPercentBarSeries"
        accessSemantics: "reference"
        prototype: "QAbstractBarSeries"
    }
    Component {
        file: "qhorizontalstackedbarseries.h"
        name: "QHorizontalStackedBarSeries"
        accessSemantics: "reference"
        prototype: "QAbstractBarSeries"
    }
    Component {
        file: "qlineseries.h"
        name: "QLineSeries"
        accessSemantics: "reference"
        prototype: "QXYSeries"
    }
    Component {
        file: "qpercentbarseries.h"
        name: "QPercentBarSeries"
        accessSemantics: "reference"
        prototype: "QAbstractBarSeries"
    }
    Component {
        file: "qpieseries.h"
        name: "QPieSeries"
        accessSemantics: "reference"
        prototype: "QAbstractSeries"
        Property {
            name: "horizontalPosition"
            type: "double"
            read: "horizontalPosition"
            write: "setHorizontalPosition"
            index: 0
        }
        Property {
            name: "verticalPosition"
            type: "double"
            read: "verticalPosition"
            write: "setVerticalPosition"
            index: 1
        }
        Property { name: "size"; type: "double"; read: "pieSize"; write: "setPieSize"; index: 2 }
        Property {
            name: "startAngle"
            type: "double"
            read: "pieStartAngle"
            write: "setPieStartAngle"
            index: 3
        }
        Property {
            name: "endAngle"
            type: "double"
            read: "pieEndAngle"
            write: "setPieEndAngle"
            index: 4
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "sum"
            type: "double"
            read: "sum"
            notify: "sumChanged"
            index: 6
            isReadonly: true
        }
        Property { name: "holeSize"; type: "double"; read: "holeSize"; write: "setHoleSize"; index: 7 }
        Signal {
            name: "added"
            Parameter { name: "slices"; type: "QList<QPieSlice*>" }
        }
        Signal {
            name: "removed"
            Parameter { name: "slices"; type: "QList<QPieSlice*>" }
        }
        Signal {
            name: "clicked"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Signal {
            name: "hovered"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
            Parameter { name: "state"; type: "bool" }
        }
        Signal {
            name: "pressed"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Signal { name: "countChanged" }
        Signal { name: "sumChanged" }
    }
    Component {
        file: "qpieslice.h"
        name: "QPieSlice"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "LabelPosition"
            values: [
                "LabelOutside",
                "LabelInsideHorizontal",
                "LabelInsideTangential",
                "LabelInsideNormal"
            ]
        }
        Property {
            name: "label"
            type: "QString"
            read: "label"
            write: "setLabel"
            notify: "labelChanged"
            index: 0
        }
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 1
        }
        Property {
            name: "labelVisible"
            type: "bool"
            read: "isLabelVisible"
            write: "setLabelVisible"
            notify: "labelVisibleChanged"
            index: 2
        }
        Property {
            name: "labelPosition"
            type: "LabelPosition"
            read: "labelPosition"
            write: "setLabelPosition"
            index: 3
        }
        Property { name: "exploded"; type: "bool"; read: "isExploded"; write: "setExploded"; index: 4 }
        Property { name: "pen"; type: "QPen"; read: "pen"; write: "setPen"; notify: "penChanged"; index: 5 }
        Property {
            name: "borderColor"
            type: "QColor"
            read: "borderColor"
            write: "setBorderColor"
            notify: "borderColorChanged"
            index: 6
        }
        Property {
            name: "borderWidth"
            type: "int"
            read: "borderWidth"
            write: "setBorderWidth"
            notify: "borderWidthChanged"
            index: 7
        }
        Property {
            name: "brush"
            type: "QBrush"
            read: "brush"
            write: "setBrush"
            notify: "brushChanged"
            index: 8
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 9
        }
        Property {
            name: "labelBrush"
            type: "QBrush"
            read: "labelBrush"
            write: "setLabelBrush"
            notify: "labelBrushChanged"
            index: 10
        }
        Property {
            name: "labelColor"
            type: "QColor"
            read: "labelColor"
            write: "setLabelColor"
            notify: "labelColorChanged"
            index: 11
        }
        Property {
            name: "labelFont"
            type: "QFont"
            read: "labelFont"
            write: "setLabelFont"
            notify: "labelFontChanged"
            index: 12
        }
        Property {
            name: "labelArmLengthFactor"
            type: "double"
            read: "labelArmLengthFactor"
            write: "setLabelArmLengthFactor"
            index: 13
        }
        Property {
            name: "explodeDistanceFactor"
            type: "double"
            read: "explodeDistanceFactor"
            write: "setExplodeDistanceFactor"
            index: 14
        }
        Property {
            name: "percentage"
            type: "double"
            read: "percentage"
            notify: "percentageChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "startAngle"
            type: "double"
            read: "startAngle"
            notify: "startAngleChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "angleSpan"
            type: "double"
            read: "angleSpan"
            notify: "angleSpanChanged"
            index: 17
            isReadonly: true
        }
        Signal { name: "clicked" }
        Signal {
            name: "hovered"
            Parameter { name: "state"; type: "bool" }
        }
        Signal { name: "pressed" }
        Signal { name: "released" }
        Signal { name: "doubleClicked" }
        Signal { name: "labelChanged" }
        Signal { name: "valueChanged" }
        Signal { name: "labelVisibleChanged" }
        Signal { name: "penChanged" }
        Signal { name: "brushChanged" }
        Signal { name: "labelBrushChanged" }
        Signal { name: "labelFontChanged" }
        Signal { name: "percentageChanged" }
        Signal { name: "startAngleChanged" }
        Signal { name: "angleSpanChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "borderColorChanged" }
        Signal { name: "borderWidthChanged" }
        Signal { name: "labelColorChanged" }
    }
    Component {
        file: "qscatterseries.h"
        name: "QScatterSeries"
        accessSemantics: "reference"
        prototype: "QXYSeries"
        Enum {
            name: "MarkerShape"
            values: [
                "MarkerShapeCircle",
                "MarkerShapeRectangle",
                "MarkerShapeRotatedRectangle",
                "MarkerShapeTriangle",
                "MarkerShapeStar",
                "MarkerShapePentagon"
            ]
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
        }
        Property {
            name: "borderColor"
            type: "QColor"
            read: "borderColor"
            write: "setBorderColor"
            notify: "borderColorChanged"
            index: 1
        }
        Property {
            name: "markerShape"
            type: "MarkerShape"
            read: "markerShape"
            write: "setMarkerShape"
            notify: "markerShapeChanged"
            index: 2
        }
        Property {
            name: "markerSize"
            type: "double"
            read: "markerSize"
            write: "setMarkerSize"
            notify: "markerSizeChanged"
            index: 3
        }
        Property { name: "brush"; type: "QBrush"; read: "brush"; write: "setBrush"; index: 4 }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "borderColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "markerShapeChanged"
            Parameter { name: "shape"; type: "MarkerShape" }
        }
        Signal {
            name: "markerSizeChanged"
            Parameter { name: "size"; type: "double" }
        }
    }
    Component {
        file: "qsplineseries.h"
        name: "QSplineSeries"
        accessSemantics: "reference"
        prototype: "QLineSeries"
    }
    Component {
        file: "qstackedbarseries.h"
        name: "QStackedBarSeries"
        accessSemantics: "reference"
        prototype: "QAbstractBarSeries"
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QValueAxis"
        accessSemantics: "reference"
        prototype: "QAbstractAxis"
        exports: [
            "QtCharts/ValueAxis 1.1",
            "QtCharts/ValueAxis 2.3",
            "QtCharts/ValueAxis 6.0",
            "QtCharts/ValueAxis 6.2"
        ]
        exportMetaObjectRevisions: [257, 515, 1536, 1538]
        Enum {
            name: "TickType"
            values: ["TicksDynamic", "TicksFixed"]
        }
        Property {
            name: "tickCount"
            type: "int"
            read: "tickCount"
            write: "setTickCount"
            notify: "tickCountChanged"
            index: 0
        }
        Property { name: "min"; type: "double"; read: "min"; write: "setMin"; notify: "minChanged"; index: 1 }
        Property { name: "max"; type: "double"; read: "max"; write: "setMax"; notify: "maxChanged"; index: 2 }
        Property {
            name: "labelFormat"
            type: "QString"
            read: "labelFormat"
            write: "setLabelFormat"
            notify: "labelFormatChanged"
            index: 3
        }
        Property {
            name: "minorTickCount"
            type: "int"
            read: "minorTickCount"
            write: "setMinorTickCount"
            notify: "minorTickCountChanged"
            index: 4
        }
        Property {
            name: "tickAnchor"
            revision: 515
            type: "double"
            read: "tickAnchor"
            write: "setTickAnchor"
            notify: "tickAnchorChanged"
            index: 5
        }
        Property {
            name: "tickInterval"
            revision: 515
            type: "double"
            read: "tickInterval"
            write: "setTickInterval"
            notify: "tickIntervalChanged"
            index: 6
        }
        Property {
            name: "tickType"
            revision: 515
            type: "TickType"
            read: "tickType"
            write: "setTickType"
            notify: "tickTypeChanged"
            index: 7
        }
        Signal {
            name: "minChanged"
            Parameter { name: "min"; type: "double" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "max"; type: "double" }
        }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "double" }
            Parameter { name: "max"; type: "double" }
        }
        Signal {
            name: "tickCountChanged"
            Parameter { name: "tickCount"; type: "int" }
        }
        Signal {
            name: "minorTickCountChanged"
            Parameter { name: "tickCount"; type: "int" }
        }
        Signal {
            name: "labelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "tickIntervalChanged"
            revision: 515
            Parameter { name: "interval"; type: "double" }
        }
        Signal {
            name: "tickAnchorChanged"
            revision: 515
            Parameter { name: "anchor"; type: "double" }
        }
        Signal {
            name: "tickTypeChanged"
            revision: 515
            Parameter { name: "type"; type: "QValueAxis::TickType" }
        }
        Method { name: "applyNiceNumbers" }
    }
    Component {
        file: "private/declarativeforeigntypes_p.h"
        name: "QValueAxis"
        accessSemantics: "reference"
        prototype: "QAbstractAxis"
        exports: [
            "QtCharts/ValuesAxis 1.0",
            "QtCharts/ValuesAxis 2.3",
            "QtCharts/ValuesAxis 6.0",
            "QtCharts/ValuesAxis 6.2"
        ]
        exportMetaObjectRevisions: [256, 515, 1536, 1538]
        Enum {
            name: "TickType"
            values: ["TicksDynamic", "TicksFixed"]
        }
        Property {
            name: "tickCount"
            type: "int"
            read: "tickCount"
            write: "setTickCount"
            notify: "tickCountChanged"
            index: 0
        }
        Property { name: "min"; type: "double"; read: "min"; write: "setMin"; notify: "minChanged"; index: 1 }
        Property { name: "max"; type: "double"; read: "max"; write: "setMax"; notify: "maxChanged"; index: 2 }
        Property {
            name: "labelFormat"
            type: "QString"
            read: "labelFormat"
            write: "setLabelFormat"
            notify: "labelFormatChanged"
            index: 3
        }
        Property {
            name: "minorTickCount"
            type: "int"
            read: "minorTickCount"
            write: "setMinorTickCount"
            notify: "minorTickCountChanged"
            index: 4
        }
        Property {
            name: "tickAnchor"
            revision: 515
            type: "double"
            read: "tickAnchor"
            write: "setTickAnchor"
            notify: "tickAnchorChanged"
            index: 5
        }
        Property {
            name: "tickInterval"
            revision: 515
            type: "double"
            read: "tickInterval"
            write: "setTickInterval"
            notify: "tickIntervalChanged"
            index: 6
        }
        Property {
            name: "tickType"
            revision: 515
            type: "TickType"
            read: "tickType"
            write: "setTickType"
            notify: "tickTypeChanged"
            index: 7
        }
        Signal {
            name: "minChanged"
            Parameter { name: "min"; type: "double" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "max"; type: "double" }
        }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "double" }
            Parameter { name: "max"; type: "double" }
        }
        Signal {
            name: "tickCountChanged"
            Parameter { name: "tickCount"; type: "int" }
        }
        Signal {
            name: "minorTickCountChanged"
            Parameter { name: "tickCount"; type: "int" }
        }
        Signal {
            name: "labelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "tickIntervalChanged"
            revision: 515
            Parameter { name: "interval"; type: "double" }
        }
        Signal {
            name: "tickAnchorChanged"
            revision: 515
            Parameter { name: "anchor"; type: "double" }
        }
        Signal {
            name: "tickTypeChanged"
            revision: 515
            Parameter { name: "type"; type: "QValueAxis::TickType" }
        }
        Method { name: "applyNiceNumbers" }
    }
}
