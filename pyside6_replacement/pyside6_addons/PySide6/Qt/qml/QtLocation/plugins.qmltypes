import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qdeclarativecategory_p.h"
        name: "QDeclarativeCategory"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtLocation/Category 5.0", "QtLocation/Category 6.0"]
        exportMetaObjectRevisions: [1280, 1536]
        Enum {
            name: "Visibility"
            values: [
                "UnspecifiedVisibility",
                "DeviceVisibility",
                "PrivateVisibility",
                "PublicVisibility"
            ]
        }
        Enum {
            name: "Status"
            values: ["Ready", "Saving", "Removing", "Error"]
        }
        Property {
            name: "category"
            type: "QPlaceCategory"
            read: "category"
            write: "setCategory"
            index: 0
        }
        Property {
            name: "plugin"
            type: "QDeclarativeGeoServiceProvider"
            isPointer: true
            read: "plugin"
            write: "setPlugin"
            notify: "pluginChanged"
            index: 1
        }
        Property {
            name: "categoryId"
            type: "QString"
            read: "categoryId"
            write: "setCategoryId"
            notify: "categoryIdChanged"
            index: 2
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 3
        }
        Property {
            name: "visibility"
            type: "Visibility"
            read: "visibility"
            write: "setVisibility"
            notify: "visibilityChanged"
            index: 4
        }
        Property {
            name: "icon"
            type: "QPlaceIcon"
            read: "icon"
            write: "setIcon"
            notify: "iconChanged"
            index: 5
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 6
            isReadonly: true
        }
        Signal { name: "pluginChanged" }
        Signal { name: "categoryIdChanged" }
        Signal { name: "nameChanged" }
        Signal { name: "visibilityChanged" }
        Signal { name: "iconChanged" }
        Signal { name: "statusChanged" }
        Method { name: "replyFinished" }
        Method { name: "pluginReady" }
        Method { name: "errorString"; type: "QString" }
        Method {
            name: "save"
            Parameter { name: "parentId"; type: "QString" }
        }
        Method { name: "save"; isCloned: true }
        Method { name: "remove" }
    }
    Component {
        file: "private/qdeclarativecirclemapitem_p.h"
        name: "QDeclarativeCircleMapItem"
        accessSemantics: "reference"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: [
            "QtLocation/MapCircle 5.0",
            "QtLocation/MapCircle 5.14",
            "QtLocation/MapCircle 5.15",
            "QtLocation/MapCircle 6.0",
            "QtLocation/MapCircle 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1294, 1295, 1536, 1539]
        Property {
            name: "center"
            type: "QGeoCoordinate"
            read: "center"
            write: "setCenter"
            notify: "centerChanged"
            index: 0
        }
        Property {
            name: "radius"
            type: "double"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 1
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "border"
            type: "QDeclarativeMapLineProperties"
            isPointer: true
            read: "border"
            index: 3
            isReadonly: true
            isConstant: true
        }
        Signal {
            name: "centerChanged"
            Parameter { name: "center"; type: "QGeoCoordinate" }
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "double" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Method { name: "markSourceDirtyAndUpdate" }
        Method { name: "onLinePropertiesChanged" }
        Method {
            name: "afterViewportChanged"
            Parameter { name: "event"; type: "QGeoMapViewportChangeEvent" }
        }
    }
    Component {
        file: "private/qdeclarativecontactdetails_p.h"
        name: "QDeclarativeContactDetails"
        accessSemantics: "reference"
        prototype: "QQmlPropertyMap"
        exports: [
            "QtLocation/ContactDetails 5.0",
            "QtLocation/ContactDetails 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
    }
    Component {
        file: "private/qdeclarativegeomap_p.h"
        name: "QDeclarativeGeoMap"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtLocation/Map 5.0",
            "QtLocation/Map 5.12",
            "QtLocation/Map 6.0",
            "QtLocation/Map 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1292, 1536, 1539]
        Property {
            name: "plugin"
            type: "QDeclarativeGeoServiceProvider"
            isPointer: true
            read: "plugin"
            write: "setPlugin"
            notify: "pluginChanged"
            index: 0
        }
        Property {
            name: "minimumZoomLevel"
            type: "double"
            read: "minimumZoomLevel"
            write: "setMinimumZoomLevel"
            notify: "minimumZoomLevelChanged"
            index: 1
        }
        Property {
            name: "maximumZoomLevel"
            type: "double"
            read: "maximumZoomLevel"
            write: "setMaximumZoomLevel"
            notify: "maximumZoomLevelChanged"
            index: 2
        }
        Property {
            name: "zoomLevel"
            type: "double"
            read: "zoomLevel"
            write: "setZoomLevel"
            notify: "zoomLevelChanged"
            index: 3
        }
        Property {
            name: "tilt"
            type: "double"
            read: "tilt"
            write: "setTilt"
            notify: "tiltChanged"
            index: 4
        }
        Property {
            name: "minimumTilt"
            type: "double"
            read: "minimumTilt"
            write: "setMinimumTilt"
            notify: "minimumTiltChanged"
            index: 5
        }
        Property {
            name: "maximumTilt"
            type: "double"
            read: "maximumTilt"
            write: "setMaximumTilt"
            notify: "maximumTiltChanged"
            index: 6
        }
        Property {
            name: "bearing"
            type: "double"
            read: "bearing"
            write: "setBearing"
            notify: "bearingChanged"
            index: 7
        }
        Property {
            name: "fieldOfView"
            type: "double"
            read: "fieldOfView"
            write: "setFieldOfView"
            notify: "fieldOfViewChanged"
            index: 8
        }
        Property {
            name: "minimumFieldOfView"
            type: "double"
            read: "minimumFieldOfView"
            write: "setMinimumFieldOfView"
            notify: "minimumFieldOfViewChanged"
            index: 9
        }
        Property {
            name: "maximumFieldOfView"
            type: "double"
            read: "maximumFieldOfView"
            write: "setMaximumFieldOfView"
            notify: "minimumFieldOfViewChanged"
            index: 10
        }
        Property {
            name: "activeMapType"
            type: "QGeoMapType"
            read: "activeMapType"
            write: "setActiveMapType"
            notify: "activeMapTypeChanged"
            index: 11
        }
        Property {
            name: "supportedMapTypes"
            type: "QGeoMapType"
            isList: true
            read: "supportedMapTypes"
            notify: "supportedMapTypesChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "center"
            type: "QGeoCoordinate"
            read: "center"
            write: "setCenter"
            notify: "centerChanged"
            index: 13
        }
        Property {
            name: "mapItems"
            type: "QObjectList"
            read: "mapItems"
            notify: "mapItemsChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "error"
            type: "QGeoServiceProvider::Error"
            read: "error"
            notify: "errorChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "visibleRegion"
            type: "QGeoShape"
            read: "visibleRegion"
            write: "setVisibleRegion"
            notify: "visibleRegionChanged"
            index: 17
        }
        Property {
            name: "copyrightsVisible"
            type: "bool"
            read: "copyrightsVisible"
            write: "setCopyrightsVisible"
            notify: "copyrightsVisibleChanged"
            index: 18
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 19
        }
        Property {
            name: "mapReady"
            type: "bool"
            read: "mapReady"
            notify: "mapReadyChanged"
            index: 20
            isReadonly: true
        }
        Property {
            name: "visibleArea"
            revision: 1292
            type: "QRectF"
            read: "visibleArea"
            write: "setVisibleArea"
            notify: "visibleAreaChanged"
            index: 21
        }
        Signal {
            name: "pluginChanged"
            Parameter { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        }
        Signal {
            name: "zoomLevelChanged"
            Parameter { name: "zoomLevel"; type: "double" }
        }
        Signal {
            name: "centerChanged"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Signal { name: "activeMapTypeChanged" }
        Signal { name: "supportedMapTypesChanged" }
        Signal {
            name: "minimumZoomLevelChanged"
            Parameter { name: "minimumZoomLevel"; type: "double" }
        }
        Signal {
            name: "maximumZoomLevelChanged"
            Parameter { name: "maximumZoomLevel"; type: "double" }
        }
        Signal { name: "mapItemsChanged" }
        Signal { name: "errorChanged" }
        Signal {
            name: "copyrightLinkActivated"
            Parameter { name: "link"; type: "QString" }
        }
        Signal {
            name: "copyrightsVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "bearingChanged"
            Parameter { name: "bearing"; type: "double" }
        }
        Signal {
            name: "tiltChanged"
            Parameter { name: "tilt"; type: "double" }
        }
        Signal {
            name: "fieldOfViewChanged"
            Parameter { name: "fieldOfView"; type: "double" }
        }
        Signal {
            name: "minimumTiltChanged"
            Parameter { name: "minimumTilt"; type: "double" }
        }
        Signal {
            name: "maximumTiltChanged"
            Parameter { name: "maximumTilt"; type: "double" }
        }
        Signal {
            name: "minimumFieldOfViewChanged"
            Parameter { name: "minimumFieldOfView"; type: "double" }
        }
        Signal {
            name: "maximumFieldOfViewChanged"
            Parameter { name: "maximumFieldOfView"; type: "double" }
        }
        Signal {
            name: "copyrightsImageChanged"
            Parameter { name: "copyrightsImage"; type: "QImage" }
        }
        Signal {
            name: "copyrightsChanged"
            Parameter { name: "copyrightsHtml"; type: "QString" }
        }
        Signal {
            name: "mapReadyChanged"
            Parameter { name: "ready"; type: "bool" }
        }
        Signal { name: "visibleAreaChanged" }
        Signal { name: "visibleRegionChanged"; revision: 65294 }
        Method { name: "mappingManagerInitialized" }
        Method { name: "pluginReady" }
        Method { name: "onSupportedMapTypesChanged" }
        Method {
            name: "onCameraCapabilitiesChanged"
            Parameter { name: "oldCameraCapabilities"; type: "QGeoCameraCapabilities" }
        }
        Method { name: "onAttachedCopyrightNoticeVisibilityChanged" }
        Method {
            name: "onCameraDataChanged"
            Parameter { name: "cameraData"; type: "QGeoCameraData" }
        }
        Method {
            name: "setBearing"
            Parameter { name: "bearing"; type: "double" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "alignCoordinateToPoint"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "removeMapItem"
            Parameter { name: "item"; type: "QDeclarativeGeoMapItemBase"; isPointer: true }
        }
        Method {
            name: "addMapItem"
            Parameter { name: "item"; type: "QDeclarativeGeoMapItemBase"; isPointer: true }
        }
        Method {
            name: "addMapItemGroup"
            Parameter { name: "itemGroup"; type: "QDeclarativeGeoMapItemGroup"; isPointer: true }
        }
        Method {
            name: "removeMapItemGroup"
            Parameter { name: "itemGroup"; type: "QDeclarativeGeoMapItemGroup"; isPointer: true }
        }
        Method {
            name: "removeMapItemView"
            Parameter { name: "itemView"; type: "QDeclarativeGeoMapItemView"; isPointer: true }
        }
        Method {
            name: "addMapItemView"
            Parameter { name: "itemView"; type: "QDeclarativeGeoMapItemView"; isPointer: true }
        }
        Method { name: "clearMapItems" }
        Method {
            name: "toCoordinate"
            type: "QGeoCoordinate"
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "clipToViewPort"; type: "bool" }
        }
        Method {
            name: "toCoordinate"
            type: "QGeoCoordinate"
            isCloned: true
            Parameter { name: "position"; type: "QPointF" }
        }
        Method {
            name: "fromCoordinate"
            type: "QPointF"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
            Parameter { name: "clipToViewPort"; type: "bool" }
        }
        Method {
            name: "fromCoordinate"
            type: "QPointF"
            isCloned: true
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "fitViewportToMapItems"
            Parameter { name: "items"; type: "QVariantList" }
        }
        Method { name: "fitViewportToMapItems"; isCloned: true }
        Method { name: "fitViewportToVisibleMapItems" }
        Method {
            name: "pan"
            Parameter { name: "dx"; type: "int" }
            Parameter { name: "dy"; type: "int" }
        }
        Method { name: "prefetchData" }
        Method { name: "clearData" }
        Method {
            name: "fitViewportToGeoShape"
            revision: 65293
            Parameter { name: "shape"; type: "QGeoShape" }
            Parameter { name: "margins"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qdeclarativegeomapcopyrightsnotice_p.h"
        name: "QDeclarativeGeoMapCopyrightNotice"
        accessSemantics: "reference"
        prototype: "QQuickPaintedItem"
        exports: [
            "QtLocation/MapCopyrightNotice 5.0",
            "QtLocation/MapCopyrightNotice 6.0",
            "QtLocation/MapCopyrightNotice 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1539]
        Property {
            name: "mapSource"
            type: "QDeclarativeGeoMap"
            isPointer: true
            read: "mapSource"
            write: "setMapSource"
            notify: "mapSourceChanged"
            index: 0
        }
        Property {
            name: "styleSheet"
            type: "QString"
            read: "styleSheet"
            write: "setStyleSheet"
            notify: "styleSheetChanged"
            index: 1
        }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "QString" }
        }
        Signal { name: "mapSourceChanged" }
        Signal {
            name: "backgroundColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "styleSheetChanged"
            Parameter { name: "styleSheet"; type: "QString" }
        }
        Signal { name: "copyrightsVisibleChanged" }
        Method {
            name: "copyrightsImageChanged"
            Parameter { name: "copyrightsImage"; type: "QImage" }
        }
        Method {
            name: "copyrightsChanged"
            Parameter { name: "copyrightsHtml"; type: "QString" }
        }
        Method {
            name: "onCopyrightsStyleSheetChanged"
            Parameter { name: "styleSheet"; type: "QString" }
        }
    }
    Component {
        file: "private/qdeclarativegeomapitembase_p.h"
        name: "QDeclarativeGeoMapItemBase"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtLocation/GeoMapItemBase 5.0",
            "QtLocation/GeoMapItemBase 5.14",
            "QtLocation/GeoMapItemBase 5.15",
            "QtLocation/GeoMapItemBase 6.0",
            "QtLocation/GeoMapItemBase 6.3"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1294, 1295, 1536, 1539]
        Property { name: "geoShape"; type: "QGeoShape"; read: "geoShape"; write: "setGeoShape"; index: 0 }
        Property {
            name: "autoFadeIn"
            revision: 1294
            type: "bool"
            read: "autoFadeIn"
            write: "setAutoFadeIn"
            index: 1
        }
        Property {
            name: "lodThreshold"
            revision: 1295
            type: "int"
            read: "lodThreshold"
            write: "setLodThreshold"
            notify: "lodThresholdChanged"
            index: 2
        }
        Signal { name: "mapItemOpacityChanged" }
        Signal { name: "addTransitionFinished"; revision: 65292 }
        Signal { name: "removeTransitionFinished"; revision: 65292 }
        Signal { name: "lodThresholdChanged" }
        Method { name: "afterChildrenChanged" }
        Method {
            name: "afterViewportChanged"
            Parameter { name: "event"; type: "QGeoMapViewportChangeEvent" }
        }
        Method { name: "polishAndUpdate" }
        Method {
            name: "baseCameraDataChanged"
            Parameter { name: "camera"; type: "QGeoCameraData" }
        }
        Method { name: "visibleAreaChanged" }
    }
    Component {
        file: "private/qdeclarativegeomapitemgroup_p.h"
        name: "QDeclarativeGeoMapItemGroup"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtLocation/MapItemGroup 5.0",
            "QtLocation/MapItemGroup 6.0",
            "QtLocation/MapItemGroup 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1539]
        Signal { name: "mapItemOpacityChanged" }
        Signal { name: "addTransitionFinished" }
        Signal { name: "removeTransitionFinished" }
        Method { name: "onMapSizeChanged" }
    }
    Component {
        file: "private/qdeclarativegeomapitemview_p.h"
        name: "QDeclarativeGeoMapItemView"
        accessSemantics: "reference"
        prototype: "QDeclarativeGeoMapItemGroup"
        exports: [
            "QtLocation/MapItemView 5.0",
            "QtLocation/MapItemView 5.12",
            "QtLocation/MapItemView 6.0",
            "QtLocation/MapItemView 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1292, 1536, 1539]
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 0
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 1
        }
        Property {
            name: "autoFitViewport"
            type: "bool"
            read: "autoFitViewport"
            write: "setAutoFitViewport"
            notify: "autoFitViewportChanged"
            index: 2
        }
        Property { name: "add"; revision: 1292; type: "QQuickTransition"; isPointer: true; index: 3 }
        Property { name: "remove"; revision: 1292; type: "QQuickTransition"; isPointer: true; index: 4 }
        Property {
            name: "mapItems"
            revision: 1292
            type: "QList<QQuickItem*>"
            read: "mapItems"
            index: 5
            isReadonly: true
        }
        Property {
            name: "incubateDelegates"
            revision: 1292
            type: "bool"
            read: "incubateDelegates"
            write: "setIncubateDelegates"
            notify: "incubateDelegatesChanged"
            index: 6
        }
        Signal { name: "modelChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "autoFitViewportChanged" }
        Signal { name: "incubateDelegatesChanged" }
        Method {
            name: "destroyingItem"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "initItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "createdItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "modelUpdated"
            Parameter { name: "changeSet"; type: "QQmlChangeSet" }
            Parameter { name: "reset"; type: "bool" }
        }
        Method { name: "exitTransitionFinished" }
    }
    Component {
        file: "private/qdeclarativegeomapquickitem_p.h"
        name: "QDeclarativeGeoMapQuickItem"
        accessSemantics: "reference"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: [
            "QtLocation/MapQuickItem 5.0",
            "QtLocation/MapQuickItem 5.14",
            "QtLocation/MapQuickItem 5.15",
            "QtLocation/MapQuickItem 6.0",
            "QtLocation/MapQuickItem 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1294, 1295, 1536, 1539]
        Property {
            name: "coordinate"
            type: "QGeoCoordinate"
            read: "coordinate"
            write: "setCoordinate"
            notify: "coordinateChanged"
            index: 0
        }
        Property {
            name: "anchorPoint"
            type: "QPointF"
            read: "anchorPoint"
            write: "setAnchorPoint"
            notify: "anchorPointChanged"
            index: 1
        }
        Property {
            name: "zoomLevel"
            type: "double"
            read: "zoomLevel"
            write: "setZoomLevel"
            notify: "zoomLevelChanged"
            index: 2
        }
        Property {
            name: "sourceItem"
            type: "QQuickItem"
            isPointer: true
            read: "sourceItem"
            write: "setSourceItem"
            notify: "sourceItemChanged"
            index: 3
        }
        Signal { name: "coordinateChanged" }
        Signal { name: "sourceItemChanged" }
        Signal { name: "anchorPointChanged" }
        Signal { name: "zoomLevelChanged" }
        Method { name: "afterChildrenChanged" }
        Method {
            name: "afterViewportChanged"
            Parameter { name: "event"; type: "QGeoMapViewportChangeEvent" }
        }
    }
    Component {
        file: "private/qdeclarativegeoroutemodel_p.h"
        name: "QDeclarativeGeoRouteModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtLocation/RouteModel 5.0",
            "QtLocation/RouteModel 6.0",
            "QtLocation/RouteModel 6.4"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1540]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Enum {
            name: "RouteError"
            values: [
                "NoError",
                "EngineNotSetError",
                "CommunicationError",
                "ParseError",
                "UnsupportedOptionError",
                "UnknownError",
                "UnknownParameterError",
                "MissingRequiredParameterError"
            ]
        }
        Property {
            name: "plugin"
            type: "QDeclarativeGeoServiceProvider"
            isPointer: true
            read: "plugin"
            write: "setPlugin"
            notify: "pluginChanged"
            index: 0
        }
        Property {
            name: "query"
            type: "QDeclarativeGeoRouteQuery"
            isPointer: true
            read: "query"
            write: "setQuery"
            notify: "queryChanged"
            index: 1
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "autoUpdate"
            type: "bool"
            read: "autoUpdate"
            write: "setAutoUpdate"
            notify: "autoUpdateChanged"
            index: 3
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "error"
            type: "RouteError"
            read: "error"
            notify: "errorChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "measurementSystem"
            type: "QLocale::MeasurementSystem"
            read: "measurementSystem"
            write: "setMeasurementSystem"
            notify: "measurementSystemChanged"
            index: 7
        }
        Signal { name: "countChanged" }
        Signal { name: "pluginChanged" }
        Signal { name: "queryChanged" }
        Signal { name: "autoUpdateChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "errorChanged" }
        Signal { name: "routesChanged" }
        Signal { name: "measurementSystemChanged" }
        Signal { name: "abortRequested" }
        Method { name: "update" }
        Method {
            name: "routingFinished"
            Parameter { name: "reply"; type: "QGeoRouteReply"; isPointer: true }
        }
        Method {
            name: "routingError"
            Parameter { name: "reply"; type: "QGeoRouteReply"; isPointer: true }
            Parameter { name: "error"; type: "QGeoRouteReply::Error" }
            Parameter { name: "errorString"; type: "QString" }
        }
        Method { name: "queryDetailsChanged" }
        Method { name: "pluginReady" }
        Method {
            name: "get"
            type: "QGeoRoute"
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "reset" }
        Method { name: "cancel" }
    }
    Component {
        file: "private/qdeclarativegeoroutemodel_p.h"
        name: "QDeclarativeGeoRouteQuery"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtLocation/RouteQuery 5.0",
            "QtLocation/RouteQuery 5.13",
            "QtLocation/RouteQuery 6.0"
        ]
        exportMetaObjectRevisions: [1280, 1293, 1536]
        Enum {
            name: "TravelMode"
            values: [
                "CarTravel",
                "PedestrianTravel",
                "BicycleTravel",
                "PublicTransitTravel",
                "TruckTravel"
            ]
        }
        Enum {
            name: "TravelModes"
            alias: "TravelMode"
            isFlag: true
            values: [
                "CarTravel",
                "PedestrianTravel",
                "BicycleTravel",
                "PublicTransitTravel",
                "TruckTravel"
            ]
        }
        Enum {
            name: "FeatureType"
            values: [
                "NoFeature",
                "TollFeature",
                "HighwayFeature",
                "PublicTransitFeature",
                "FerryFeature",
                "TunnelFeature",
                "DirtRoadFeature",
                "ParksFeature",
                "MotorPoolLaneFeature",
                "TrafficFeature"
            ]
        }
        Enum {
            name: "FeatureWeight"
            values: [
                "NeutralFeatureWeight",
                "PreferFeatureWeight",
                "RequireFeatureWeight",
                "AvoidFeatureWeight",
                "DisallowFeatureWeight"
            ]
        }
        Enum {
            name: "RouteOptimization"
            values: [
                "ShortestRoute",
                "FastestRoute",
                "MostEconomicRoute",
                "MostScenicRoute"
            ]
        }
        Enum {
            name: "RouteOptimizations"
            alias: "RouteOptimization"
            isFlag: true
            values: [
                "ShortestRoute",
                "FastestRoute",
                "MostEconomicRoute",
                "MostScenicRoute"
            ]
        }
        Enum {
            name: "SegmentDetail"
            values: ["NoSegmentData", "BasicSegmentData"]
        }
        Enum {
            name: "SegmentDetails"
            alias: "SegmentDetail"
            isFlag: true
            values: ["NoSegmentData", "BasicSegmentData"]
        }
        Enum {
            name: "ManeuverDetail"
            values: ["NoManeuvers", "BasicManeuvers"]
        }
        Enum {
            name: "ManeuverDetails"
            alias: "ManeuverDetail"
            isFlag: true
            values: ["NoManeuvers", "BasicManeuvers"]
        }
        Property {
            name: "numberAlternativeRoutes"
            type: "int"
            read: "numberAlternativeRoutes"
            write: "setNumberAlternativeRoutes"
            notify: "numberAlternativeRoutesChanged"
            index: 0
        }
        Property {
            name: "travelModes"
            type: "TravelModes"
            read: "travelModes"
            write: "setTravelModes"
            notify: "travelModesChanged"
            index: 1
        }
        Property {
            name: "routeOptimizations"
            type: "RouteOptimizations"
            read: "routeOptimizations"
            write: "setRouteOptimizations"
            notify: "routeOptimizationsChanged"
            index: 2
        }
        Property {
            name: "segmentDetail"
            type: "SegmentDetail"
            read: "segmentDetail"
            write: "setSegmentDetail"
            notify: "segmentDetailChanged"
            index: 3
        }
        Property {
            name: "maneuverDetail"
            type: "ManeuverDetail"
            read: "maneuverDetail"
            write: "setManeuverDetail"
            notify: "maneuverDetailChanged"
            index: 4
        }
        Property {
            name: "waypoints"
            type: "QGeoCoordinate"
            isList: true
            read: "waypoints"
            write: "setWaypoints"
            notify: "waypointsChanged"
            index: 5
        }
        Property {
            name: "excludedAreas"
            type: "QGeoRectangle"
            isList: true
            read: "excludedAreas"
            write: "setExcludedAreas"
            notify: "excludedAreasChanged"
            index: 6
        }
        Property {
            name: "featureTypes"
            type: "int"
            isList: true
            read: "featureTypes"
            notify: "featureTypesChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "departureTime"
            revision: 1293
            type: "QDateTime"
            read: "departureTime"
            write: "setDepartureTime"
            notify: "departureTimeChanged"
            index: 8
        }
        Signal { name: "numberAlternativeRoutesChanged" }
        Signal { name: "travelModesChanged" }
        Signal { name: "routeOptimizationsChanged" }
        Signal { name: "waypointsChanged" }
        Signal { name: "excludedAreasChanged" }
        Signal { name: "featureTypesChanged" }
        Signal { name: "maneuverDetailChanged" }
        Signal { name: "segmentDetailChanged" }
        Signal { name: "queryDetailsChanged" }
        Signal { name: "departureTimeChanged" }
        Method { name: "excludedAreaCoordinateChanged" }
        Method { name: "waypointChanged" }
        Method {
            name: "addWaypoint"
            Parameter { name: "w"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeWaypoint"
            Parameter { name: "waypoint"; type: "QGeoCoordinate" }
        }
        Method { name: "clearWaypoints" }
        Method {
            name: "addExcludedArea"
            Parameter { name: "area"; type: "QGeoRectangle" }
        }
        Method {
            name: "removeExcludedArea"
            Parameter { name: "area"; type: "QGeoRectangle" }
        }
        Method { name: "clearExcludedAreas" }
        Method {
            name: "setFeatureWeight"
            Parameter { name: "featureType"; type: "FeatureType" }
            Parameter { name: "featureWeight"; type: "FeatureWeight" }
        }
        Method {
            name: "featureWeight"
            type: "int"
            Parameter { name: "featureType"; type: "FeatureType" }
        }
        Method { name: "resetFeatureWeights" }
        Method { name: "doCoordinateChanged" }
    }
    Component {
        file: "private/qdeclarativegeoserviceprovider_p.h"
        name: "QDeclarativeGeoServiceProvider"
        accessSemantics: "reference"
        defaultProperty: "parameters"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtLocation/Plugin 5.0", "QtLocation/Plugin 6.0"]
        exportMetaObjectRevisions: [1280, 1536]
        Enum {
            name: "RoutingFeature"
            values: [
                "NoRoutingFeatures",
                "OnlineRoutingFeature",
                "OfflineRoutingFeature",
                "LocalizedRoutingFeature",
                "RouteUpdatesFeature",
                "AlternativeRoutesFeature",
                "ExcludeAreasRoutingFeature",
                "AnyRoutingFeatures"
            ]
        }
        Enum {
            name: "RoutingFeatures"
            alias: "RoutingFeature"
            isFlag: true
            values: [
                "NoRoutingFeatures",
                "OnlineRoutingFeature",
                "OfflineRoutingFeature",
                "LocalizedRoutingFeature",
                "RouteUpdatesFeature",
                "AlternativeRoutesFeature",
                "ExcludeAreasRoutingFeature",
                "AnyRoutingFeatures"
            ]
        }
        Enum {
            name: "GeocodingFeature"
            values: [
                "NoGeocodingFeatures",
                "OnlineGeocodingFeature",
                "OfflineGeocodingFeature",
                "ReverseGeocodingFeature",
                "LocalizedGeocodingFeature",
                "AnyGeocodingFeatures"
            ]
        }
        Enum {
            name: "GeocodingFeatures"
            alias: "GeocodingFeature"
            isFlag: true
            values: [
                "NoGeocodingFeatures",
                "OnlineGeocodingFeature",
                "OfflineGeocodingFeature",
                "ReverseGeocodingFeature",
                "LocalizedGeocodingFeature",
                "AnyGeocodingFeatures"
            ]
        }
        Enum {
            name: "MappingFeature"
            values: [
                "NoMappingFeatures",
                "OnlineMappingFeature",
                "OfflineMappingFeature",
                "LocalizedMappingFeature",
                "AnyMappingFeatures"
            ]
        }
        Enum {
            name: "MappingFeatures"
            alias: "MappingFeature"
            isFlag: true
            values: [
                "NoMappingFeatures",
                "OnlineMappingFeature",
                "OfflineMappingFeature",
                "LocalizedMappingFeature",
                "AnyMappingFeatures"
            ]
        }
        Enum {
            name: "PlacesFeature"
            values: [
                "NoPlacesFeatures",
                "OnlinePlacesFeature",
                "OfflinePlacesFeature",
                "SavePlaceFeature",
                "RemovePlaceFeature",
                "SaveCategoryFeature",
                "RemoveCategoryFeature",
                "PlaceRecommendationsFeature",
                "SearchSuggestionsFeature",
                "LocalizedPlacesFeature",
                "NotificationsFeature",
                "PlaceMatchingFeature",
                "AnyPlacesFeatures"
            ]
        }
        Enum {
            name: "PlacesFeatures"
            alias: "PlacesFeature"
            isFlag: true
            values: [
                "NoPlacesFeatures",
                "OnlinePlacesFeature",
                "OfflinePlacesFeature",
                "SavePlaceFeature",
                "RemovePlaceFeature",
                "SaveCategoryFeature",
                "RemoveCategoryFeature",
                "PlaceRecommendationsFeature",
                "SearchSuggestionsFeature",
                "LocalizedPlacesFeature",
                "NotificationsFeature",
                "PlaceMatchingFeature",
                "AnyPlacesFeatures"
            ]
        }
        Enum {
            name: "NavigationFeatures"
            alias: "NavigationFeature"
            isFlag: true
            values: [
                "NoNavigationFeatures",
                "OnlineNavigationFeature",
                "OfflineNavigationFeature",
                "AnyNavigationFeatures"
            ]
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
        }
        Property {
            name: "availableServiceProviders"
            type: "QStringList"
            read: "availableServiceProviders"
            index: 1
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "parameters"
            type: "QDeclarativePluginParameter"
            isList: true
            read: "parameters"
            index: 2
            isReadonly: true
        }
        Property {
            name: "required"
            type: "QDeclarativeGeoServiceProviderRequirements"
            isPointer: true
            read: "requirements"
            write: "setRequirements"
            index: 3
        }
        Property {
            name: "locales"
            type: "QStringList"
            read: "locales"
            write: "setLocales"
            notify: "localesChanged"
            index: 4
        }
        Property {
            name: "preferred"
            type: "QStringList"
            read: "preferred"
            write: "setPreferred"
            notify: "preferredChanged"
            index: 5
        }
        Property {
            name: "allowExperimental"
            type: "bool"
            read: "allowExperimental"
            write: "setAllowExperimental"
            notify: "allowExperimentalChanged"
            index: 6
        }
        Property {
            name: "isAttached"
            type: "bool"
            read: "isAttached"
            notify: "attached"
            index: 7
            isReadonly: true
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal { name: "localesChanged" }
        Signal { name: "attached" }
        Signal {
            name: "preferredChanged"
            Parameter { name: "preferences"; type: "QStringList" }
        }
        Signal {
            name: "allowExperimentalChanged"
            Parameter { name: "allow"; type: "bool" }
        }
        Method {
            name: "supportsRouting"
            type: "bool"
            Parameter { name: "feature"; type: "RoutingFeatures" }
        }
        Method { name: "supportsRouting"; type: "bool"; isCloned: true }
        Method {
            name: "supportsGeocoding"
            type: "bool"
            Parameter { name: "feature"; type: "GeocodingFeatures" }
        }
        Method { name: "supportsGeocoding"; type: "bool"; isCloned: true }
        Method {
            name: "supportsMapping"
            type: "bool"
            Parameter { name: "feature"; type: "MappingFeatures" }
        }
        Method { name: "supportsMapping"; type: "bool"; isCloned: true }
        Method {
            name: "supportsPlaces"
            type: "bool"
            Parameter { name: "feature"; type: "PlacesFeatures" }
        }
        Method { name: "supportsPlaces"; type: "bool"; isCloned: true }
        Method {
            name: "supportsNavigation"
            revision: 65291
            type: "bool"
            Parameter { name: "feature"; type: "NavigationFeature" }
        }
        Method { name: "supportsNavigation"; revision: 65291; type: "bool"; isCloned: true }
    }
    Component {
        file: "private/qdeclarativegeoserviceprovider_p.h"
        name: "QDeclarativeGeoServiceProviderRequirements"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtLocation/PluginRequirements 5.0",
            "QtLocation/PluginRequirements 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "mapping"
            type: "QDeclarativeGeoServiceProvider::MappingFeatures"
            read: "mappingRequirements"
            write: "setMappingRequirements"
            notify: "mappingRequirementsChanged"
            index: 0
        }
        Property {
            name: "routing"
            type: "QDeclarativeGeoServiceProvider::RoutingFeatures"
            read: "routingRequirements"
            write: "setRoutingRequirements"
            notify: "routingRequirementsChanged"
            index: 1
        }
        Property {
            name: "geocoding"
            type: "QDeclarativeGeoServiceProvider::GeocodingFeatures"
            read: "geocodingRequirements"
            write: "setGeocodingRequirements"
            notify: "geocodingRequirementsChanged"
            index: 2
        }
        Property {
            name: "places"
            type: "QDeclarativeGeoServiceProvider::PlacesFeatures"
            read: "placesRequirements"
            write: "setPlacesRequirements"
            notify: "placesRequirementsChanged"
            index: 3
        }
        Property {
            name: "navigation"
            type: "QDeclarativeGeoServiceProvider::NavigationFeatures"
            read: "navigationRequirements"
            write: "setNavigationRequirements"
            notify: "navigationRequirementsChanged"
            index: 4
        }
        Signal {
            name: "mappingRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::MappingFeatures" }
        }
        Signal {
            name: "routingRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::RoutingFeatures" }
        }
        Signal {
            name: "geocodingRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::GeocodingFeatures" }
        }
        Signal {
            name: "placesRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::PlacesFeatures" }
        }
        Signal {
            name: "navigationRequirementsChanged"
            Parameter { name: "features"; type: "QDeclarativeGeoServiceProvider::NavigationFeatures" }
        }
        Signal { name: "requirementsChanged" }
        Method {
            name: "matches"
            type: "bool"
            Parameter { name: "provider"; type: "QGeoServiceProvider"; isPointer: true; isConstant: true }
        }
    }
    Component {
        file: "private/qdeclarativegeocodemodel_p.h"
        name: "QDeclarativeGeocodeModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtLocation/GeocodeModel 6.0",
            "QtLocation/GeocodeModel 6.4"
        ]
        exportMetaObjectRevisions: [1536, 1540]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Enum {
            name: "GeocodeError"
            values: [
                "NoError",
                "EngineNotSetError",
                "CommunicationError",
                "ParseError",
                "UnsupportedOptionError",
                "CombinationError",
                "UnknownError",
                "UnknownParameterError",
                "MissingRequiredParameterError"
            ]
        }
        Property {
            name: "plugin"
            type: "QDeclarativeGeoServiceProvider"
            isPointer: true
            read: "plugin"
            write: "setPlugin"
            notify: "pluginChanged"
            index: 0
        }
        Property {
            name: "autoUpdate"
            type: "bool"
            read: "autoUpdate"
            write: "setAutoUpdate"
            notify: "autoUpdateChanged"
            index: 1
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "limit"
            type: "int"
            read: "limit"
            write: "setLimit"
            notify: "limitChanged"
            index: 5
        }
        Property {
            name: "offset"
            type: "int"
            read: "offset"
            write: "setOffset"
            notify: "offsetChanged"
            index: 6
        }
        Property {
            name: "query"
            type: "QVariant"
            read: "query"
            write: "setQuery"
            notify: "queryChanged"
            index: 7
        }
        Property {
            name: "bounds"
            type: "QVariant"
            read: "bounds"
            write: "setBounds"
            notify: "boundsChanged"
            index: 8
        }
        Property {
            name: "error"
            type: "GeocodeError"
            read: "error"
            notify: "errorChanged"
            index: 9
            isReadonly: true
        }
        Signal { name: "countChanged" }
        Signal { name: "pluginChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "errorChanged" }
        Signal { name: "locationsChanged" }
        Signal { name: "autoUpdateChanged" }
        Signal { name: "boundsChanged" }
        Signal { name: "queryChanged" }
        Signal { name: "limitChanged" }
        Signal { name: "offsetChanged" }
        Method { name: "update" }
        Method { name: "queryContentChanged" }
        Method {
            name: "geocodeFinished"
            Parameter { name: "reply"; type: "QGeoCodeReply"; isPointer: true }
        }
        Method {
            name: "geocodeError"
            Parameter { name: "reply"; type: "QGeoCodeReply"; isPointer: true }
            Parameter { name: "error"; type: "QGeoCodeReply::Error" }
            Parameter { name: "errorString"; type: "QString" }
        }
        Method { name: "pluginReady" }
        Method {
            name: "get"
            type: "QDeclarativeGeoLocation"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "reset" }
        Method { name: "cancel" }
    }
    Component {
        file: "private/qdeclarativepolylinemapitem_p.h"
        name: "QDeclarativeMapLineProperties"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtLocation/QDeclarativeMapLineProperties 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "width"
            type: "double"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 0
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "double" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
    }
    Component {
        file: "private/qdeclarativeplace_p.h"
        name: "QDeclarativePlace"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtLocation/Place 5.0", "QtLocation/Place 6.0"]
        exportMetaObjectRevisions: [1280, 1536]
        Enum {
            name: "Status"
            values: ["Ready", "Saving", "Fetching", "Removing", "Error"]
        }
        Enum {
            name: "Visibility"
            values: [
                "UnspecifiedVisibility",
                "DeviceVisibility",
                "PrivateVisibility",
                "PublicVisibility"
            ]
        }
        Property { name: "place"; type: "QPlace"; read: "place"; write: "setPlace"; index: 0 }
        Property {
            name: "plugin"
            type: "QDeclarativeGeoServiceProvider"
            isPointer: true
            read: "plugin"
            write: "setPlugin"
            notify: "pluginChanged"
            index: 1
        }
        Property {
            name: "categories"
            type: "QDeclarativeCategory"
            isList: true
            read: "categories"
            notify: "categoriesChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "location"
            type: "QDeclarativeGeoLocation"
            isPointer: true
            read: "location"
            write: "setLocation"
            notify: "locationChanged"
            index: 3
        }
        Property {
            name: "ratings"
            type: "QPlaceRatings"
            read: "ratings"
            write: "setRatings"
            notify: "ratingsChanged"
            index: 4
        }
        Property {
            name: "supplier"
            type: "QPlaceSupplier"
            read: "supplier"
            write: "setSupplier"
            notify: "supplierChanged"
            index: 5
        }
        Property {
            name: "icon"
            type: "QPlaceIcon"
            read: "icon"
            write: "setIcon"
            notify: "iconChanged"
            index: 6
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 7
        }
        Property {
            name: "placeId"
            type: "QString"
            read: "placeId"
            write: "setPlaceId"
            notify: "placeIdChanged"
            index: 8
        }
        Property {
            name: "attribution"
            type: "QString"
            read: "attribution"
            write: "setAttribution"
            notify: "attributionChanged"
            index: 9
        }
        Property {
            name: "reviewModel"
            type: "QDeclarativePlaceReviewModel"
            isPointer: true
            read: "reviewModel"
            notify: "reviewModelChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "imageModel"
            type: "QDeclarativePlaceImageModel"
            isPointer: true
            read: "imageModel"
            notify: "imageModelChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "editorialModel"
            type: "QDeclarativePlaceEditorialModel"
            isPointer: true
            read: "editorialModel"
            notify: "editorialModelChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "extendedAttributes"
            type: "QObject"
            isPointer: true
            read: "extendedAttributes"
            notify: "extendedAttributesChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "contactDetails"
            type: "QDeclarativeContactDetails"
            isPointer: true
            read: "contactDetails"
            notify: "contactDetailsChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "detailsFetched"
            type: "bool"
            read: "detailsFetched"
            notify: "detailsFetchedChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "primaryPhone"
            type: "QString"
            read: "primaryPhone"
            notify: "primaryPhoneChanged"
            index: 17
            isReadonly: true
        }
        Property {
            name: "primaryFax"
            type: "QString"
            read: "primaryFax"
            notify: "primaryFaxChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "primaryEmail"
            type: "QString"
            read: "primaryEmail"
            notify: "primaryEmailChanged"
            index: 19
            isReadonly: true
        }
        Property {
            name: "primaryWebsite"
            type: "QUrl"
            read: "primaryWebsite"
            notify: "primaryWebsiteChanged"
            index: 20
            isReadonly: true
        }
        Property {
            name: "visibility"
            type: "Visibility"
            read: "visibility"
            write: "setVisibility"
            notify: "visibilityChanged"
            index: 21
        }
        Property {
            name: "favorite"
            type: "QDeclarativePlace"
            isPointer: true
            read: "favorite"
            write: "setFavorite"
            notify: "favoriteChanged"
            index: 22
        }
        Signal { name: "pluginChanged" }
        Signal { name: "categoriesChanged" }
        Signal { name: "locationChanged" }
        Signal { name: "ratingsChanged" }
        Signal { name: "supplierChanged" }
        Signal { name: "iconChanged" }
        Signal { name: "nameChanged" }
        Signal { name: "placeIdChanged" }
        Signal { name: "attributionChanged" }
        Signal { name: "detailsFetchedChanged" }
        Signal { name: "reviewModelChanged" }
        Signal { name: "imageModelChanged" }
        Signal { name: "editorialModelChanged" }
        Signal { name: "primaryPhoneChanged" }
        Signal { name: "primaryFaxChanged" }
        Signal { name: "primaryEmailChanged" }
        Signal { name: "primaryWebsiteChanged" }
        Signal { name: "extendedAttributesChanged" }
        Signal { name: "contactDetailsChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "visibilityChanged" }
        Signal { name: "favoriteChanged" }
        Method { name: "finished" }
        Method {
            name: "contactsModified"
            Parameter { type: "QString" }
            Parameter { type: "QVariant" }
        }
        Method { name: "pluginReady" }
        Method { name: "cleanupDeletedCategories" }
        Method { name: "getDetails" }
        Method { name: "save" }
        Method { name: "remove" }
        Method { name: "errorString"; type: "QString" }
        Method {
            name: "copyFrom"
            Parameter { name: "original"; type: "QDeclarativePlace"; isPointer: true }
        }
        Method {
            name: "initializeFavorite"
            Parameter { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        }
    }
    Component {
        file: "private/qdeclarativeplacecontentmodel_p.h"
        name: "QDeclarativePlaceContentModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        interfaces: ["QQmlParserStatus"]
        Property {
            name: "place"
            type: "QDeclarativePlace"
            isPointer: true
            read: "place"
            write: "setPlace"
            notify: "placeChanged"
            index: 0
        }
        Property {
            name: "batchSize"
            type: "int"
            read: "batchSize"
            write: "setBatchSize"
            notify: "batchSizeChanged"
            index: 1
        }
        Property {
            name: "totalCount"
            type: "int"
            read: "totalCount"
            notify: "totalCountChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "placeChanged" }
        Signal { name: "batchSizeChanged" }
        Signal { name: "totalCountChanged" }
        Method { name: "fetchFinished" }
    }
    Component {
        file: "private/qdeclarativeplacecontentmodel_p.h"
        name: "QDeclarativePlaceEditorialModel"
        accessSemantics: "value"
        prototype: "QDeclarativePlaceContentModel"
        exports: [
            "QtLocation/EditorialModel 5.0",
            "QtLocation/EditorialModel 6.0",
            "QtLocation/EditorialModel 6.4"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536, 1540]
    }
    Component {
        file: "private/qdeclarativeplacecontentmodel_p.h"
        name: "QDeclarativePlaceImageModel"
        accessSemantics: "value"
        prototype: "QDeclarativePlaceContentModel"
        exports: [
            "QtLocation/ImageModel 5.0",
            "QtLocation/ImageModel 6.0",
            "QtLocation/ImageModel 6.4"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536, 1540]
    }
    Component {
        file: "private/qdeclarativeplacecontentmodel_p.h"
        name: "QDeclarativePlaceReviewModel"
        accessSemantics: "value"
        prototype: "QDeclarativePlaceContentModel"
        exports: [
            "QtLocation/ReviewModel 5.0",
            "QtLocation/ReviewModel 6.0",
            "QtLocation/ReviewModel 6.4"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536, 1540]
    }
    Component {
        file: "private/qdeclarativegeoserviceprovider_p.h"
        name: "QDeclarativePluginParameter"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtLocation/PluginParameter 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
        }
        Property {
            name: "value"
            type: "QVariant"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 1
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "QVariant" }
        }
        Signal { name: "initialized" }
    }
    Component {
        file: "private/qdeclarativepolygonmapitem_p.h"
        name: "QDeclarativePolygonMapItem"
        accessSemantics: "reference"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: [
            "QtLocation/MapPolygon 5.0",
            "QtLocation/MapPolygon 5.14",
            "QtLocation/MapPolygon 5.15",
            "QtLocation/MapPolygon 6.0",
            "QtLocation/MapPolygon 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1294, 1295, 1536, 1539]
        Property {
            name: "path"
            type: "QGeoCoordinate"
            isList: true
            read: "path"
            write: "setPath"
            notify: "pathChanged"
            index: 0
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "border"
            type: "QDeclarativeMapLineProperties"
            isPointer: true
            read: "border"
            index: 2
            isReadonly: true
            isConstant: true
        }
        Signal { name: "pathChanged" }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Method { name: "markSourceDirtyAndUpdate" }
        Method { name: "onLinePropertiesChanged" }
        Method {
            name: "afterViewportChanged"
            Parameter { name: "event"; type: "QGeoMapViewportChangeEvent" }
        }
        Method {
            name: "addCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
    }
    Component {
        file: "private/qdeclarativepolylinemapitem_p.h"
        name: "QDeclarativePolylineMapItem"
        accessSemantics: "reference"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: [
            "QtLocation/MapPolyline 5.0",
            "QtLocation/MapPolyline 5.14",
            "QtLocation/MapPolyline 5.15",
            "QtLocation/MapPolyline 6.0",
            "QtLocation/MapPolyline 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1294, 1295, 1536, 1539]
        Property {
            name: "path"
            type: "QGeoCoordinate"
            isList: true
            read: "path"
            write: "setPath"
            notify: "pathChanged"
            index: 0
        }
        Property {
            name: "line"
            type: "QDeclarativeMapLineProperties"
            isPointer: true
            read: "line"
            index: 1
            isReadonly: true
            isConstant: true
        }
        Signal { name: "pathChanged" }
        Method { name: "updateAfterLinePropertiesChanged" }
        Method {
            name: "afterViewportChanged"
            Parameter { name: "event"; type: "QGeoMapViewportChangeEvent" }
        }
        Method { name: "pathLength"; type: "int" }
        Method {
            name: "addCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "insertCoordinate"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "replaceCoordinate"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "coordinateAt"
            type: "QGeoCoordinate"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "containsCoordinate"
            type: "bool"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setPath"
            Parameter { name: "path"; type: "QGeoPath" }
        }
    }
    Component {
        file: "private/qdeclarativerectanglemapitem_p.h"
        name: "QDeclarativeRectangleMapItem"
        accessSemantics: "reference"
        prototype: "QDeclarativeGeoMapItemBase"
        exports: [
            "QtLocation/MapRectangle 5.0",
            "QtLocation/MapRectangle 5.14",
            "QtLocation/MapRectangle 5.15",
            "QtLocation/MapRectangle 6.0",
            "QtLocation/MapRectangle 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1294, 1295, 1536, 1539]
        Property {
            name: "topLeft"
            type: "QGeoCoordinate"
            read: "topLeft"
            write: "setTopLeft"
            notify: "topLeftChanged"
            index: 0
        }
        Property {
            name: "bottomRight"
            type: "QGeoCoordinate"
            read: "bottomRight"
            write: "setBottomRight"
            notify: "bottomRightChanged"
            index: 1
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "border"
            type: "QDeclarativeMapLineProperties"
            isPointer: true
            read: "border"
            index: 3
            isReadonly: true
            isConstant: true
        }
        Signal {
            name: "topLeftChanged"
            Parameter { name: "topLeft"; type: "QGeoCoordinate" }
        }
        Signal {
            name: "bottomRightChanged"
            Parameter { name: "bottomRight"; type: "QGeoCoordinate" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Method { name: "markSourceDirtyAndUpdate" }
        Method { name: "onLinePropertiesChanged" }
        Method {
            name: "afterViewportChanged"
            Parameter { name: "event"; type: "QGeoMapViewportChangeEvent" }
        }
    }
    Component {
        file: "private/qdeclarativeroutemapitem_p.h"
        name: "QDeclarativeRouteMapItem"
        accessSemantics: "reference"
        prototype: "QDeclarativePolylineMapItem"
        exports: [
            "QtLocation/MapRoute 5.0",
            "QtLocation/MapRoute 5.14",
            "QtLocation/MapRoute 5.15",
            "QtLocation/MapRoute 6.0",
            "QtLocation/MapRoute 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1294, 1295, 1536, 1539]
        Property {
            name: "route"
            type: "QGeoRoute"
            read: "route"
            write: "setRoute"
            notify: "routeChanged"
            index: 0
        }
        Signal {
            name: "routeChanged"
            Parameter { name: "route"; type: "QGeoRoute" }
        }
        Method { name: "updateRoutePath" }
    }
    Component {
        file: "private/qdeclarativesearchmodelbase_p.h"
        name: "QDeclarativeSearchModelBase"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        interfaces: ["QQmlParserStatus"]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "plugin"
            type: "QDeclarativeGeoServiceProvider"
            isPointer: true
            read: "plugin"
            write: "setPlugin"
            notify: "pluginChanged"
            index: 0
        }
        Property {
            name: "searchArea"
            type: "QVariant"
            read: "searchArea"
            write: "setSearchArea"
            notify: "searchAreaChanged"
            index: 1
        }
        Property {
            name: "limit"
            type: "int"
            read: "limit"
            write: "setLimit"
            notify: "limitChanged"
            index: 2
        }
        Property {
            name: "previousPagesAvailable"
            type: "bool"
            read: "previousPagesAvailable"
            notify: "previousPagesAvailableChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "nextPagesAvailable"
            type: "bool"
            read: "nextPagesAvailable"
            notify: "nextPagesAvailableChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "pluginChanged" }
        Signal { name: "searchAreaChanged" }
        Signal { name: "limitChanged" }
        Signal { name: "previousPagesAvailableChanged" }
        Signal { name: "nextPagesAvailableChanged" }
        Signal { name: "statusChanged" }
        Method { name: "queryFinished" }
        Method { name: "onContentUpdated" }
        Method { name: "pluginNameChanged" }
        Method { name: "update" }
        Method { name: "cancel" }
        Method { name: "reset" }
        Method { name: "errorString"; type: "QString" }
        Method { name: "previousPage" }
        Method { name: "nextPage" }
    }
    Component {
        file: "private/qdeclarativesearchresultmodel_p.h"
        name: "QDeclarativeSearchResultModel"
        accessSemantics: "reference"
        prototype: "QDeclarativeSearchModelBase"
        exports: [
            "QtLocation/PlaceSearchModel 5.0",
            "QtLocation/PlaceSearchModel 5.12",
            "QtLocation/PlaceSearchModel 6.0",
            "QtLocation/PlaceSearchModel 6.4"
        ]
        exportMetaObjectRevisions: [1280, 1292, 1536, 1540]
        Enum {
            name: "SearchResultType"
            values: [
                "UnknownSearchResult",
                "PlaceResult",
                "ProposedSearchResult"
            ]
        }
        Enum {
            name: "RelevanceHint"
            values: [
                "UnspecifiedHint",
                "DistanceHint",
                "LexicalPlaceNameHint"
            ]
        }
        Property {
            name: "searchTerm"
            type: "QString"
            read: "searchTerm"
            write: "setSearchTerm"
            notify: "searchTermChanged"
            index: 0
        }
        Property {
            name: "categories"
            type: "QDeclarativeCategory"
            isList: true
            read: "categories"
            notify: "categoriesChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "recommendationId"
            type: "QString"
            read: "recommendationId"
            write: "setRecommendationId"
            notify: "recommendationIdChanged"
            index: 2
        }
        Property {
            name: "relevanceHint"
            type: "RelevanceHint"
            read: "relevanceHint"
            write: "setRelevanceHint"
            notify: "relevanceHintChanged"
            index: 3
        }
        Property {
            name: "visibilityScope"
            type: "QDeclarativePlace::Visibility"
            read: "visibilityScope"
            write: "setVisibilityScope"
            notify: "visibilityScopeChanged"
            index: 4
        }
        Property {
            name: "count"
            type: "int"
            read: "rowCount"
            notify: "rowCountChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "favoritesPlugin"
            type: "QDeclarativeGeoServiceProvider"
            isPointer: true
            read: "favoritesPlugin"
            write: "setFavoritesPlugin"
            notify: "favoritesPluginChanged"
            index: 6
        }
        Property {
            name: "favoritesMatchParameters"
            type: "QVariantMap"
            read: "favoritesMatchParameters"
            write: "setFavoritesMatchParameters"
            notify: "favoritesMatchParametersChanged"
            index: 7
        }
        Property {
            name: "incremental"
            revision: 1292
            type: "bool"
            notify: "incrementalChanged"
            index: 8
        }
        Signal { name: "searchTermChanged" }
        Signal { name: "categoriesChanged" }
        Signal { name: "recommendationIdChanged" }
        Signal { name: "relevanceHintChanged" }
        Signal { name: "visibilityScopeChanged" }
        Signal { name: "rowCountChanged" }
        Signal { name: "favoritesPluginChanged" }
        Signal { name: "favoritesMatchParametersChanged" }
        Signal { name: "dataChanged" }
        Signal { name: "incrementalChanged" }
        Method { name: "queryFinished" }
        Method { name: "onContentUpdated" }
        Method {
            name: "updateLayout"
            Parameter { name: "favoritePlaces"; type: "QPlace"; isList: true }
        }
        Method { name: "updateLayout"; isCloned: true }
        Method {
            name: "placeUpdated"
            Parameter { name: "placeId"; type: "QString" }
        }
        Method {
            name: "placeRemoved"
            Parameter { name: "placeId"; type: "QString" }
        }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "roleName"; type: "QString" }
        }
        Method {
            name: "updateWith"
            Parameter { name: "proposedSearchIndex"; type: "int" }
        }
    }
    Component {
        file: "private/qdeclarativesearchsuggestionmodel_p.h"
        name: "QDeclarativeSearchSuggestionModel"
        accessSemantics: "reference"
        prototype: "QDeclarativeSearchModelBase"
        exports: [
            "QtLocation/PlaceSearchSuggestionModel 5.0",
            "QtLocation/PlaceSearchSuggestionModel 6.0",
            "QtLocation/PlaceSearchSuggestionModel 6.4"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1540]
        Property {
            name: "searchTerm"
            type: "QString"
            read: "searchTerm"
            write: "setSearchTerm"
            notify: "searchTermChanged"
            index: 0
        }
        Property {
            name: "suggestions"
            type: "QStringList"
            read: "suggestions"
            notify: "suggestionsChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "searchTermChanged" }
        Signal { name: "suggestionsChanged" }
        Method { name: "queryFinished" }
    }
    Component {
        file: "private/qdeclarativesupportedcategoriesmodel_p.h"
        name: "QDeclarativeSupportedCategoriesModel"
        accessSemantics: "reference"
        prototype: "QAbstractItemModel"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtLocation/CategoryModel 5.0",
            "QtLocation/CategoryModel 6.0",
            "QtLocation/CategoryModel 6.4"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1540]
        Enum {
            name: "Roles"
            values: ["CategoryRole", "ParentCategoryRole"]
        }
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "plugin"
            type: "QDeclarativeGeoServiceProvider"
            isPointer: true
            read: "plugin"
            write: "setPlugin"
            notify: "pluginChanged"
            index: 0
        }
        Property {
            name: "hierarchical"
            type: "bool"
            read: "hierarchical"
            write: "setHierarchical"
            notify: "hierarchicalChanged"
            index: 1
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "pluginChanged" }
        Signal { name: "hierarchicalChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "dataChanged" }
        Method { name: "update" }
        Method { name: "replyFinished" }
        Method {
            name: "addedCategory"
            Parameter { name: "category"; type: "QPlaceCategory" }
            Parameter { name: "parentId"; type: "QString" }
        }
        Method {
            name: "updatedCategory"
            Parameter { name: "category"; type: "QPlaceCategory" }
            Parameter { name: "parentId"; type: "QString" }
        }
        Method {
            name: "removedCategory"
            Parameter { name: "categoryId"; type: "QString" }
            Parameter { name: "parentId"; type: "QString" }
        }
        Method { name: "connectNotificationSignals" }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method { name: "errorString"; type: "QString" }
    }
    Component {
        file: "qgeomaneuver.h"
        name: "QGeoManeuver"
        accessSemantics: "value"
        exports: ["QtLocation/routeManeuver 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "InstructionDirection"
            values: [
                "NoDirection",
                "DirectionForward",
                "DirectionBearRight",
                "DirectionLightRight",
                "DirectionRight",
                "DirectionHardRight",
                "DirectionUTurnRight",
                "DirectionUTurnLeft",
                "DirectionHardLeft",
                "DirectionLeft",
                "DirectionLightLeft",
                "DirectionBearLeft"
            ]
        }
        Property {
            name: "valid"
            type: "bool"
            read: "isValid"
            index: 0
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "position"
            type: "QGeoCoordinate"
            read: "position"
            index: 1
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "instructionText"
            type: "QString"
            read: "instructionText"
            index: 2
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "direction"
            type: "InstructionDirection"
            read: "direction"
            index: 3
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "timeToNextInstruction"
            type: "int"
            read: "timeToNextInstruction"
            index: 4
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "distanceToNextInstruction"
            type: "double"
            read: "distanceToNextInstruction"
            index: 5
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "waypoint"
            type: "QGeoCoordinate"
            read: "waypoint"
            index: 6
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "extendedAttributes"
            type: "QVariantMap"
            read: "extendedAttributes"
            index: 7
            isReadonly: true
            isConstant: true
        }
    }
    Component {
        file: "private/qgeomaneuverderived_p.h"
        name: "QGeoManeuverDerived"
        accessSemantics: "none"
        prototype: "QGeoManeuver"
        exports: ["QtLocation/RouteManeuver 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
    }
    Component {
        file: "private/qgeomaptype_p.h"
        name: "QGeoMapType"
        accessSemantics: "value"
        exports: ["QtLocation/mapType 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "MapStyle"
            values: [
                "NoMap",
                "StreetMap",
                "SatelliteMapDay",
                "SatelliteMapNight",
                "TerrainMap",
                "HybridMap",
                "TransitMap",
                "GrayStreetMap",
                "PedestrianMap",
                "CarNavigationMap",
                "CycleMap",
                "CustomMap"
            ]
        }
        Property {
            name: "style"
            type: "MapStyle"
            read: "style"
            index: 0
            isReadonly: true
            isConstant: true
        }
        Property { name: "name"; type: "QString"; read: "name"; index: 1; isReadonly: true; isConstant: true }
        Property {
            name: "description"
            type: "QString"
            read: "description"
            index: 2
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "mobile"
            type: "bool"
            read: "mobile"
            index: 3
            isReadonly: true
            isConstant: true
        }
        Property { name: "night"; type: "bool"; read: "night"; index: 4; isReadonly: true; isConstant: true }
        Property {
            name: "cameraCapabilities"
            type: "QGeoCameraCapabilities"
            read: "cameraCapabilities"
            index: 5
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "metadata"
            type: "QVariantMap"
            read: "metadata"
            index: 6
            isReadonly: true
            isConstant: true
        }
    }
    Component {
        file: "private/qgeomaptype_p.h"
        name: "QGeoMapType"
        accessSemantics: "none"
        exports: ["QtLocation/MapType 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "MapStyle"
            values: [
                "NoMap",
                "StreetMap",
                "SatelliteMapDay",
                "SatelliteMapNight",
                "TerrainMap",
                "HybridMap",
                "TransitMap",
                "GrayStreetMap",
                "PedestrianMap",
                "CarNavigationMap",
                "CycleMap",
                "CustomMap"
            ]
        }
        Property {
            name: "style"
            type: "MapStyle"
            read: "style"
            index: 0
            isReadonly: true
            isConstant: true
        }
        Property { name: "name"; type: "QString"; read: "name"; index: 1; isReadonly: true; isConstant: true }
        Property {
            name: "description"
            type: "QString"
            read: "description"
            index: 2
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "mobile"
            type: "bool"
            read: "mobile"
            index: 3
            isReadonly: true
            isConstant: true
        }
        Property { name: "night"; type: "bool"; read: "night"; index: 4; isReadonly: true; isConstant: true }
        Property {
            name: "cameraCapabilities"
            type: "QGeoCameraCapabilities"
            read: "cameraCapabilities"
            index: 5
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "metadata"
            type: "QVariantMap"
            read: "metadata"
            index: 6
            isReadonly: true
            isConstant: true
        }
    }
    Component {
        file: "qgeoroute.h"
        name: "QGeoRoute"
        accessSemantics: "value"
        exports: ["QtLocation/route 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "routeId"
            type: "QString"
            read: "routeId"
            index: 0
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "bounds"
            type: "QGeoRectangle"
            read: "bounds"
            index: 1
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "travelTime"
            type: "int"
            read: "travelTime"
            index: 2
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "distance"
            type: "double"
            read: "distance"
            index: 3
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "path"
            type: "QGeoCoordinate"
            isList: true
            read: "path"
            write: "setPath"
            index: 4
        }
        Property {
            name: "routeLegs"
            type: "QGeoRoute"
            isList: true
            read: "routeLegs"
            index: 5
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "extendedAttributes"
            type: "QVariantMap"
            read: "extendedAttributes"
            index: 6
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "legIndex"
            type: "int"
            read: "legIndex"
            index: 7
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "overallRoute"
            type: "QGeoRoute"
            read: "overallRoute"
            index: 8
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "segmentsCount"
            type: "qsizetype"
            read: "segmentsCount"
            index: 9
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "segments"
            type: "QGeoRouteSegment"
            isList: true
            read: "segments"
            index: 10
            isReadonly: true
            isConstant: true
        }
    }
    Component {
        file: "qgeoroutesegment.h"
        name: "QGeoRouteSegment"
        accessSemantics: "value"
        exports: ["QtLocation/routeSegment 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "travelTime"
            type: "int"
            read: "travelTime"
            index: 0
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "distance"
            type: "double"
            read: "distance"
            index: 1
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "path"
            type: "QGeoCoordinate"
            isList: true
            read: "path"
            index: 2
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "maneuver"
            type: "QGeoManeuver"
            read: "maneuver"
            index: 3
            isReadonly: true
            isConstant: true
        }
    }
    Component {
        file: "qplaceattribute.h"
        name: "QPlaceAttribute"
        accessSemantics: "value"
        exports: ["QtLocation/placeAttribute 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property { name: "label"; type: "QString"; read: "label"; write: "setLabel"; index: 0 }
        Property { name: "text"; type: "QString"; read: "text"; write: "setText"; index: 1 }
    }
    Component {
        file: "qplacecontactdetail.h"
        name: "QPlaceContactDetail"
        accessSemantics: "value"
        exports: ["QtLocation/contactDetail 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property { name: "label"; type: "QString"; read: "label"; write: "setLabel"; index: 0 }
        Property { name: "value"; type: "QString"; read: "value"; write: "setValue"; index: 1 }
    }
    Component {
        file: "qplaceicon.h"
        name: "QPlaceIcon"
        accessSemantics: "value"
        exports: ["QtLocation/icon 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "parameters"
            type: "QVariantMap"
            read: "parameters"
            write: "setParameters"
            index: 0
        }
        Property {
            name: "manager"
            type: "QPlaceManager"
            isPointer: true
            read: "manager"
            write: "setManager"
            index: 1
        }
        Method {
            name: "url"
            type: "QUrl"
            Parameter { name: "size"; type: "QSize" }
        }
        Method { name: "url"; type: "QUrl"; isCloned: true }
    }
    Component {
        file: "qplaceratings.h"
        name: "QPlaceRatings"
        accessSemantics: "value"
        exports: ["QtLocation/ratings 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property { name: "average"; type: "double"; read: "average"; write: "setAverage"; index: 0 }
        Property { name: "maximum"; type: "double"; read: "maximum"; write: "setMaximum"; index: 1 }
        Property { name: "count"; type: "int"; read: "count"; write: "setCount"; index: 2 }
    }
    Component {
        file: "qplacesupplier.h"
        name: "QPlaceSupplier"
        accessSemantics: "value"
        exports: ["QtLocation/supplier 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 0 }
        Property {
            name: "supplierId"
            type: "QString"
            read: "supplierId"
            write: "setSupplierId"
            index: 1
        }
        Property { name: "url"; type: "QUrl"; read: "url"; write: "setUrl"; index: 2 }
        Property { name: "icon"; type: "QPlaceIcon"; read: "icon"; write: "setIcon"; index: 3 }
    }
    Component {
        file: "qplaceuser.h"
        name: "QPlaceUser"
        accessSemantics: "value"
        exports: ["QtLocation/user 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property { name: "userId"; type: "QString"; read: "userId"; write: "setUserId"; index: 0 }
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 1 }
    }
    Component {
        file: "qqmlpropertymap.h"
        name: "QQmlPropertyMap"
        accessSemantics: "reference"
        prototype: "QObject"
        Signal {
            name: "valueChanged"
            Parameter { name: "key"; type: "QString" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method { name: "keys"; type: "QStringList" }
    }
}
