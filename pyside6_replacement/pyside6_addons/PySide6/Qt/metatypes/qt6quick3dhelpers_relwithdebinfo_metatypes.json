[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "GridGeometry"}], "className": "GridGeometry", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "horizontalLines", "notify": "horizontalLinesChanged", "read": "horizontalLines", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHorizontalLines"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "verticalLines", "notify": "verticalLinesChanged", "read": "verticalLines", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setVerticalLines"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "horizontalStep", "notify": "horizontalStepChanged", "read": "horizontalStep", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHorizontalStep"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "verticalStep", "notify": "verticalStepChanged", "read": "verticalStep", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVerticalStep"}], "qualifiedClassName": "GridGeometry", "signals": [{"access": "public", "name": "horizontalLinesChanged", "returnType": "void"}, {"access": "public", "name": "verticalLinesChanged", "returnType": "void"}, {"access": "public", "name": "horizontalStepChanged", "returnType": "void"}, {"access": "public", "name": "verticalStepChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "setHorizontalLines", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "setVerticalLines", "returnType": "void"}, {"access": "public", "arguments": [{"name": "step", "type": "float"}], "name": "setHorizontalStep", "returnType": "void"}, {"access": "public", "arguments": [{"name": "step", "type": "float"}], "name": "setVerticalStep", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "gridgeometry_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "HeightFieldGeometry"}], "className": "HeightFieldGeometry", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "revision": 1541, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "smoothShading", "notify": "smoothShadingChanged", "read": "smoothShading", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSmoothShading"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "extents", "notify": "extentsChanged", "read": "extents", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setExtents"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "heightMap", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}], "qualifiedClassName": "HeightFieldGeometry", "signals": [{"access": "public", "name": "sourceChanged", "returnType": "void"}, {"access": "public", "name": "smoothShadingChanged", "returnType": "void"}, {"access": "public", "name": "extentsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "heightfieldgeometry_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "InfiniteGrid"}], "className": "QQuick3DInfiniteGrid", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "visible", "notify": "visibleChanged", "read": "visible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "gridInterval", "notify": "gridIntervalChanged", "read": "gridInterval", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setGridInterval"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "gridAxes", "notify": "gridAxesChanged", "read": "gridAxes", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGridAxes"}], "qualifiedClassName": "QQuick3DInfiniteGrid", "signals": [{"access": "public", "name": "visibleChanged", "returnType": "void"}, {"access": "public", "name": "gridIntervalChanged", "returnType": "void"}, {"access": "public", "name": "gridAxesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "infinitegrid_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "InstanceModel"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "InstanceModel", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "instancingTable", "notify": "instancingChanged", "read": "instancing", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstancing*", "user": false, "write": "setInstancing"}], "qualifiedClassName": "InstanceModel", "signals": [{"access": "public", "name": "instancingChanged", "returnType": "void"}], "slots": [{"access": "private", "name": "reset", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}]}, {"classInfos": [{"name": "QML.Element", "value": "In<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "In<PERSON><PERSON><PERSON><PERSON><PERSON>", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "instancingTable", "notify": "instancingChanged", "read": "instancing", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstancing*", "user": false, "write": "setInstancing"}], "qualifiedClassName": "In<PERSON><PERSON><PERSON><PERSON><PERSON>", "signals": [{"access": "public", "name": "instancingChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DRepeater"}]}], "inputFile": "instancerepeater_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "LookAtNode"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "LookAtNode", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DNode*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "LookAtNode", "signals": [{"access": "public", "name": "targetChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "node", "type": "QQuick3DNode*"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "private", "name": "updateLookAt", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DNode"}]}], "inputFile": "lookatnode_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}], "className": "ProceduralSkyTextureData", "enums": [{"isClass": true, "isFlag": false, "name": "SkyTextureQuality", "values": ["SkyTextureQualityLow", "SkyTextureQualityMedium", "SkyTextureQualityHigh", "SkyTextureQualityVeryHigh"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "skyTopColor", "notify": "skyTopColorChanged", "read": "skyTopColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSkyTopColor"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "skyHorizonColor", "notify": "skyHorizonColorChanged", "read": "skyHorizonColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSkyHorizonColor"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "skyCurve", "notify": "skyCurveChanged", "read": "skyCurve", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSkyCurve"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "skyEnergy", "notify": "skyEnergyChanged", "read": "skyEnergy", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSkyEnergy"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "groundBottomColor", "notify": "groundBottomColorChanged", "read": "groundBottomColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setGroundBottomColor"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "groundHorizonColor", "notify": "groundHorizonColorChanged", "read": "groundHorizonColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setGroundHorizonColor"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "groundCurve", "notify": "groundCurveChanged", "read": "groundCurve", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setGroundCurve"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "groundEnergy", "notify": "groundEnergyChanged", "read": "groundEnergy", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setGroundEnergy"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "sunColor", "notify": "sunColorChanged", "read": "sunColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSunColor"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "sunLatitude", "notify": "sunLatitudeChanged", "read": "sunLatitude", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunLatitude"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "sunLongitude", "notify": "sunLongitudeChanged", "read": "sunLongitude", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunLongitude"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "sunAngleMin", "notify": "sunAngleMinChanged", "read": "sunAngleMin", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunAngleMin"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "sunAngleMax", "notify": "sunAngleMaxChanged", "read": "sunAngleMax", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunAngleMax"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "sunCurve", "notify": "sunCurveChanged", "read": "sunCurve", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunCurve"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "sunEnergy", "notify": "sunEnergyChanged", "read": "sunEnergy", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunEnergy"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "textureQuality", "notify": "textureQualityChanged", "read": "textureQuality", "required": false, "scriptable": true, "stored": true, "type": "SkyTextureQuality", "user": false, "write": "setTextureQuality"}], "qualifiedClassName": "ProceduralSkyTextureData", "signals": [{"access": "public", "arguments": [{"name": "skyTopColor", "type": "QColor"}], "name": "skyTopColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyHorizonColor", "type": "QColor"}], "name": "skyHorizonColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyCurve", "type": "float"}], "name": "skyCurveChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyEnergy", "type": "float"}], "name": "skyEnergyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundBottomColor", "type": "QColor"}], "name": "groundBottomColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundHorizonColor", "type": "QColor"}], "name": "groundHorizonColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundCurve", "type": "float"}], "name": "groundCurveChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundEnergy", "type": "float"}], "name": "groundEnergyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunColor", "type": "QColor"}], "name": "sunColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunLatitude", "type": "float"}], "name": "sunLatitudeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunLongitude", "type": "float"}], "name": "sunLongitudeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunAngleMin", "type": "float"}], "name": "sunAngleMinChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunAngleMax", "type": "float"}], "name": "sunAngleMaxChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunCurve", "type": "float"}], "name": "sunCurveChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunEnergy", "type": "float"}], "name": "sunEnergyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureQuality", "type": "SkyTextureQuality"}], "name": "textureQualityChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "skyTopColor", "type": "QColor"}], "name": "setSkyTopColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyHorizonColor", "type": "QColor"}], "name": "setSkyHorizonColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyCurve", "type": "float"}], "name": "setSkyCurve", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyEnergy", "type": "float"}], "name": "setSkyEnergy", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundBottomColor", "type": "QColor"}], "name": "setGroundBottomColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundHorizonColor", "type": "QColor"}], "name": "setGroundHorizonColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundCurve", "type": "float"}], "name": "setGroundCurve", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundEnergy", "type": "float"}], "name": "setGroundEnergy", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunColor", "type": "QColor"}], "name": "setSunColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunLatitude", "type": "float"}], "name": "setSunLatitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunLongitude", "type": "float"}], "name": "setSunLongitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunAngleMin", "type": "float"}], "name": "setSunAngleMin", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunAngleMax", "type": "float"}], "name": "setSunAngleMax", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunCurve", "type": "float"}], "name": "setSunCurve", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunEnergy", "type": "float"}], "name": "setSunEnergy", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureQuality", "type": "SkyTextureQuality"}], "name": "setTextureQuality", "returnType": "void"}, {"access": "public", "name": "generateRGBA16FTexture", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DTextureData"}]}], "inputFile": "proceduralskytexturedata_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.AddedInVersion", "value": "1538"}, {"name": "QML.Element", "value": "InstanceRange"}], "className": "QQuick3DInstanceRange", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "from", "notify": "fromChanged", "read": "from", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setFrom"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "to", "notify": "to<PERSON><PERSON><PERSON>", "read": "to", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setTo"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "proportional", "notify": "proportionalChanged", "read": "proportional", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setProportional"}], "qualifiedClassName": "QQuick3DInstanceRange", "signals": [{"access": "public", "name": "fromChanged", "returnType": "void"}, {"access": "public", "name": "to<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "proportionalChanged", "returnType": "void"}, {"access": "public", "name": "changed", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "from", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setFrom", "returnType": "void"}, {"access": "public", "arguments": [{"name": "to", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setTo", "returnType": "void"}, {"access": "public", "arguments": [{"name": "proportional", "type": "bool"}], "name": "setProportional", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "RandomInstancing"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DRandomInstancing", "enums": [{"isClass": true, "isFlag": false, "name": "ColorModel", "values": ["RGB", "HSV", "HSL"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "instanceCount", "notify": "instanceCountChanged", "read": "instanceCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setInstanceCount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "scale", "notify": "scaleChanged", "read": "scale", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setScale"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "rotation", "notify": "rotationChanged", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setRotation"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "colorModel", "notify": "colorModelChanged", "read": "colorModel", "required": false, "scriptable": true, "stored": true, "type": "ColorModel", "user": false, "write": "setColorModel"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "customData", "notify": "customDataChanged", "read": "customData", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setCustomData"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "randomSeed", "notify": "randomSeedChanged", "read": "randomSeed", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRandomSeed"}], "qualifiedClassName": "QQuick3DRandomInstancing", "signals": [{"access": "public", "name": "instanceCountChanged", "returnType": "void"}, {"access": "public", "name": "randomSeedChanged", "returnType": "void"}, {"access": "public", "name": "positionChanged", "returnType": "void"}, {"access": "public", "name": "scaleChanged", "returnType": "void"}, {"access": "public", "name": "rotationChanged", "returnType": "void"}, {"access": "public", "name": "colorChanged", "returnType": "void"}, {"access": "public", "name": "customDataChanged", "returnType": "void"}, {"access": "public", "name": "colorModelChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "instanceCount", "type": "int"}], "name": "setInstanceCount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "randomSeed", "type": "int"}], "name": "setRandomSeed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QQuick3DInstanceRange*"}], "name": "setPosition", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scale", "type": "QQuick3DInstanceRange*"}], "name": "setScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "QQuick3DInstanceRange*"}], "name": "setRotation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QQuick3DInstanceRange*"}], "name": "setColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "customData", "type": "QQuick3DInstanceRange*"}], "name": "setCustomData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "colorModel", "type": "ColorModel"}], "name": "setColorModel", "returnType": "void"}, {"access": "private", "name": "handleChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DInstancing"}]}], "inputFile": "randominstancing_p.h", "outputRevision": 68}]