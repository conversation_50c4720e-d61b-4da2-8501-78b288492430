[{"classes": [{"className": "QAbstractOAuth", "enums": [{"isClass": true, "isFlag": false, "name": "Status", "values": ["NotAuthenticated", "TemporaryCredentialsReceived", "Granted", "RefreshingToken"]}, {"isClass": true, "isFlag": false, "name": "Stage", "values": ["RequestingTemporaryCredentials", "RequestingAuthorization", "RequestingAccessToken", "RefreshingAccessToken"]}, {"isClass": true, "isFlag": false, "name": "Error", "values": ["NoError", "NetworkError", "ServerError", "OAuthTokenNotFoundError", "OAuthTokenSecretNotFoundError", "OAuthCallbackNotVerified"]}], "methods": [{"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "head", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "head", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "get", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "get", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "post", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "post", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "put", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "put", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "deleteResource", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "deleteResource", "returnType": "QNetworkReply*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "clientIdentifier", "notify": "clientIdentifierChanged", "read": "clientIdentifier", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setClientIdentifier"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "token", "notify": "tokenChanged", "read": "token", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setToken"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "extraTokens", "notify": "extraTokensChanged", "read": "extraTokens", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "authorizationUrl", "notify": "authorizationUrlChanged", "read": "authorizationUrl", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setAuthorizationUrl"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "contentType", "notify": "contentTypeChanged", "read": "contentType", "required": false, "scriptable": true, "stored": true, "type": "QAbstractOAuth::ContentType", "user": false, "write": "setContentType"}], "qualifiedClassName": "QAbstractOAuth", "signals": [{"access": "public", "arguments": [{"name": "clientIdentifier", "type": "QString"}], "name": "clientIdentifierChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "token", "type": "QString"}], "name": "tokenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "Status"}], "name": "statusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "name": "authorizationUrlChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tokens", "type": "QVariantMap"}], "name": "extraTokensChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "contentType", "type": "ContentType"}], "name": "contentTypeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "Error"}], "name": "requestFailed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "name": "authorize<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "granted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QNetworkReply*"}], "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QByteArray"}], "name": "replyDataReceived", "returnType": "void"}], "slots": [{"access": "public", "name": "grant", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractoauth.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractOAuthReplyHandler", "object": true, "qualifiedClassName": "QAbstractOAuthReplyHandler", "signals": [{"access": "public", "arguments": [{"name": "values", "type": "QVariantMap"}], "name": "callback<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tokens", "type": "QVariantMap"}], "name": "tokensReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QByteArray"}], "name": "replyDataReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QByteArray"}], "name": "callbackDataReceived", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "reply", "type": "QNetworkReply*"}], "name": "networkReplyFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractoauthreplyhandler.h", "outputRevision": 68}, {"classes": [{"className": "QOAuth1", "enums": [{"isClass": true, "isFlag": false, "name": "SignatureMethod", "values": ["Hmac_Sha1", "Rsa_Sha1", "PlainText"]}], "object": true, "qualifiedClassName": "QOAuth1", "signals": [{"access": "public", "arguments": [{"name": "method", "type": "QOAuth1::SignatureMethod"}], "name": "signatureMethodChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "credential", "type": "QString"}], "name": "clientSharedSecretChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "token", "type": "QString"}], "name": "tokenSecretChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "name": "temporaryCredentialsUrlChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "name": "tokenCredentialsUrlChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "grant", "returnType": "void"}, {"access": "public", "arguments": [{"name": "verifier", "type": "QString"}], "name": "continueGrantWithVerifier", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractOAuth"}]}], "inputFile": "qoauth1.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractOAuth2", "methods": [{"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "createAuthenticatedUrl", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "createAuthenticatedUrl", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "head", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "head", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "get", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "get", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "post", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "post", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "data", "type": "QByteArray"}], "name": "post", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "multiPart", "type": "QHttpMultiPart*"}], "name": "post", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "put", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "put", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "data", "type": "QByteArray"}], "name": "put", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "multiPart", "type": "QHttpMultiPart*"}], "name": "put", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parameters", "type": "QVariantMap"}], "name": "deleteResource", "returnType": "QNetworkReply*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "isCloned": true, "name": "deleteResource", "returnType": "QNetworkReply*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "scope", "notify": "scopeChanged", "read": "scope", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setScope"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "userAgent", "notify": "userAgentChanged", "read": "userAgent", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setUserAgent"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "clientIdentifierSharedKey", "notify": "clientIdentifierSharedKeyChanged", "read": "clientIdentifierSharedKey", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setClientIdentifierSharedKey"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "state", "notify": "stateChanged", "read": "state", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setState"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "expiration", "notify": "expirationAtChanged", "read": "expirationAt", "required": false, "scriptable": true, "stored": true, "type": "QDateTime", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "refreshToken", "notify": "refreshTokenChanged", "read": "refreshToken", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRefreshToken"}], "qualifiedClassName": "QAbstractOAuth2", "signals": [{"access": "public", "arguments": [{"name": "scope", "type": "QString"}], "name": "scopeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "userAgent", "type": "QString"}], "name": "userAgentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "responseType", "type": "QString"}], "name": "responseTypeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clientIdentifierSharedKey", "type": "QString"}], "name": "clientIdentifierSharedKeyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QString"}], "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "expiration", "type": "QDateTime"}], "name": "expirationAtChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "refreshToken", "type": "QString"}], "name": "refreshTokenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "configuration", "type": "QSslConfiguration"}], "name": "sslConfigurationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QString"}, {"name": "errorDescription", "type": "QString"}, {"name": "uri", "type": "QUrl"}], "name": "error", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QVariantMap"}], "name": "authorizationCallbackReceived", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractOAuth"}]}], "inputFile": "qabstractoauth2.h", "outputRevision": 68}, {"classes": [{"className": "QOAuth2AuthorizationCodeFlow", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "accessTokenUrl", "notify": "accessTokenUrlChanged", "read": "accessTokenUrl", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setAccessTokenUrl"}], "qualifiedClassName": "QOAuth2AuthorizationCodeFlow", "signals": [{"access": "public", "arguments": [{"name": "accessTokenUrl", "type": "QUrl"}], "name": "accessTokenUrlChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "grant", "returnType": "void"}, {"access": "public", "name": "refreshAccessToken", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractOAuth2"}]}], "inputFile": "qoauth2authorizationcodeflow.h", "outputRevision": 68}, {"classes": [{"className": "QOAuthHttpServerReplyHandler", "object": true, "qualifiedClassName": "QOAuthHttpServerReplyHandler", "superClasses": [{"access": "public", "name": "QOAuthOobReplyHandler"}]}], "inputFile": "qoauthhttpserverreplyhandler.h", "outputRevision": 68}, {"classes": [{"className": "QOAuthOobReplyHandler", "object": true, "qualifiedClassName": "QOAuthOobReplyHandler", "superClasses": [{"access": "public", "name": "QAbstractOAuthReplyHandler"}]}], "inputFile": "qoauthoobreplyhandler.h", "outputRevision": 68}]