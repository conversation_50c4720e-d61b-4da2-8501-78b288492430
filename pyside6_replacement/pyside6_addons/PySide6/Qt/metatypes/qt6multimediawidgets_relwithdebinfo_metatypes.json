[{"classes": [{"className": "QGraphicsVideoItem", "methods": [{"access": "public", "name": "videoSink", "returnType": "QVideoSink*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "aspectRatioMode", "read": "aspectRatioMode", "required": false, "scriptable": true, "stored": true, "type": "Qt::AspectRatioMode", "user": false, "write": "setAspectRatioMode"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "offset", "read": "offset", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setOffset"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "size", "read": "size", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setSize"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "nativeSize", "notify": "nativeSizeChanged", "read": "nativeSize", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "videoSink", "read": "videoSink", "required": false, "scriptable": true, "stored": true, "type": "QVideoSink*", "user": false}], "qualifiedClassName": "QGraphicsVideoItem", "signals": [{"access": "public", "arguments": [{"name": "size", "type": "QSizeF"}], "name": "nativeSizeChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"type": "QVideoFrame"}], "name": "_q_present", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGraphicsObject"}]}], "inputFile": "qgraphicsvideoitem.h", "outputRevision": 68}, {"classes": [{"className": "QVideoWidget", "methods": [{"access": "public", "name": "videoSink", "returnType": "QVideoSink*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fullScreen", "notify": "fullScreenChanged", "read": "isFullScreen", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFullScreen"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "aspectRatioMode", "notify": "aspectRatioModeChanged", "read": "aspectRatioMode", "required": false, "scriptable": true, "stored": true, "type": "Qt::AspectRatioMode", "user": false, "write": "setAspectRatioMode"}], "qualifiedClassName": "QVideoWidget", "signals": [{"access": "public", "arguments": [{"name": "fullScreen", "type": "bool"}], "name": "fullScreenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "Qt::AspectRatioMode"}], "name": "aspectRatioModeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "fullScreen", "type": "bool"}], "name": "setFullScreen", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "Qt::AspectRatioMode"}], "name": "setAspectRatioMode", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qvideowidget.h", "outputRevision": 68}]