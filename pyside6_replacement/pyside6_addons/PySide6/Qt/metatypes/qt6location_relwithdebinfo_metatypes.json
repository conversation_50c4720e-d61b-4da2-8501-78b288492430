[{"classes": [{"className": "QAbstractGeoTileCache", "object": true, "qualifiedClassName": "QAbstractGeoTileCache", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractgeotilecache_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoCodeReply", "object": true, "qualifiedClassName": "QGeoCodeReply", "signals": [{"access": "public", "name": "finished", "returnType": "void"}, {"access": "public", "name": "aborted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeocodereply.h", "outputRevision": 68}, {"classes": [{"className": "QGeoCodingManager", "object": true, "qualifiedClassName": "QGeoCodingManager", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}], "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeocodingmanager.h", "outputRevision": 68}, {"classes": [{"className": "QGeoCodingManagerEngine", "object": true, "qualifiedClassName": "QGeoCodingManagerEngine", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}], "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeocodingmanagerengine.h", "outputRevision": 68}, {"classes": [{"className": "QGeoFileTileCache", "object": true, "qualifiedClassName": "QGeoFileTileCache", "superClasses": [{"access": "public", "name": "QAbstractGeoTileCache"}]}], "inputFile": "qgeofiletilecache_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoManeuverDerived", "gadget": true, "qualifiedClassName": "QGeoManeuverDerived", "superClasses": [{"access": "public", "name": "QGeoManeuver"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoManeuverDerived"}, {"name": "QML.Element", "value": "RouteManeuver"}], "className": "QGeoManeuverForeignNamespace", "namespace": true, "qualifiedClassName": "QGeoManeuverForeignNamespace"}], "inputFile": "qgeomaneuverderived_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoMap", "enums": [{"isClass": false, "isFlag": false, "name": "Capability", "values": ["SupportsNothing", "SupportsVisibleRegion", "SupportsSetBearing", "SupportsAnchoringCoordinate", "SupportsFittingViewportToGeoRectangle", "SupportsVisibleArea"]}, {"alias": "Capability", "isClass": false, "isFlag": true, "name": "Capabilities", "values": ["SupportsNothing", "SupportsVisibleRegion", "SupportsSetBearing", "SupportsAnchoringCoordinate", "SupportsFittingViewportToGeoRectangle", "SupportsVisibleArea"]}], "object": true, "qualifiedClassName": "QGeoMap", "signals": [{"access": "public", "arguments": [{"name": "cameraData", "type": "QGeoCameraData"}], "name": "cameraDataChanged", "returnType": "void"}, {"access": "public", "name": "sgNodeChanged", "returnType": "void"}, {"access": "public", "name": "activeMapTypeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "oldCameraCapabilities", "type": "QGeoCameraCapabilities"}], "name": "cameraCapabilitiesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsImage", "type": "QImage"}], "name": "copyrightsImageChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsHtml", "type": "QString"}], "name": "copyrightsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "styleSheet", "type": "QString"}], "name": "copyrightsStyleSheetChanged", "returnType": "void"}, {"access": "public", "name": "visibleAreaChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeomap_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoMappingManager", "object": true, "qualifiedClassName": "QGeoMappingManager", "signals": [{"access": "public", "name": "initialized", "returnType": "void"}, {"access": "public", "name": "supportedMapTypesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeomappingmanager_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoMappingManagerEngine", "object": true, "qualifiedClassName": "QGeoMappingManagerEngine", "signals": [{"access": "public", "name": "initialized", "returnType": "void"}, {"access": "public", "name": "supportedMapTypesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeomappingmanagerengine_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoRouteParser", "enums": [{"isClass": false, "isFlag": false, "name": "TrafficSide", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "trafficSide", "notify": "trafficSideChanged", "read": "trafficSide", "required": false, "scriptable": true, "stored": true, "type": "TrafficSide", "user": false, "write": "setTrafficSide"}], "qualifiedClassName": "QGeoRouteParser", "signals": [{"access": "public", "arguments": [{"name": "trafficSide", "type": "TrafficSide"}], "name": "trafficSideChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "trafficSide", "type": "TrafficSide"}], "name": "setTrafficSide", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeorouteparser_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoRouteParserOsrmV5", "object": true, "qualifiedClassName": "QGeoRouteParserOsrmV5", "superClasses": [{"access": "public", "name": "QGeoRouteParser"}]}], "inputFile": "qgeorouteparserosrmv5_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoRouteReply", "object": true, "qualifiedClassName": "QGeoRouteReply", "signals": [{"access": "public", "name": "finished", "returnType": "void"}, {"access": "public", "name": "aborted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeoroutereply.h", "outputRevision": 68}, {"classes": [{"className": "QGeoRoutingManager", "object": true, "qualifiedClassName": "QGeoRoutingManager", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}], "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeoroutingmanager.h", "outputRevision": 68}, {"classes": [{"className": "QGeoRoutingManagerEngine", "object": true, "qualifiedClassName": "QGeoRoutingManagerEngine", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}], "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeoroutingmanagerengine.h", "outputRevision": 68}, {"classes": [{"className": "QGeoServiceProvider", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "NotSupportedError", "UnknownParameterError", "MissingRequiredParameterError", "ConnectionError", "LoaderError"]}, {"alias": "RoutingFeature", "isClass": false, "isFlag": true, "name": "RoutingFeatures", "values": ["NoRoutingFeatures", "OnlineRoutingFeature", "OfflineRoutingFeature", "LocalizedRoutingFeature", "RouteUpdatesFeature", "AlternativeRoutesFeature", "ExcludeAreasRoutingFeature", "AnyRoutingFeatures"]}, {"alias": "GeocodingFeature", "isClass": false, "isFlag": true, "name": "GeocodingFeatures", "values": ["NoGeocodingFeatures", "OnlineGeocodingFeature", "OfflineGeocodingFeature", "ReverseGeocodingFeature", "LocalizedGeocodingFeature", "AnyGeocodingFeatures"]}, {"alias": "MappingFeature", "isClass": false, "isFlag": true, "name": "MappingFeatures", "values": ["NoMappingFeatures", "OnlineMappingFeature", "OfflineMappingFeature", "LocalizedMappingFeature", "AnyMappingFeatures"]}, {"alias": "PlacesFeature", "isClass": false, "isFlag": true, "name": "PlacesFeatures", "values": ["NoPlacesFeatures", "OnlinePlacesFeature", "OfflinePlacesFeature", "SavePlaceFeature", "RemovePlaceFeature", "SaveCategoryFeature", "RemoveCategoryFeature", "PlaceRecommendationsFeature", "SearchSuggestionsFeature", "LocalizedPlacesFeature", "NotificationsFeature", "PlaceMatchingFeature", "AnyPlacesFeatures"]}, {"alias": "NavigationFeature", "isClass": false, "isFlag": true, "name": "NavigationFeatures", "values": ["NoNavigationFeatures", "OnlineNavigationFeature", "OfflineNavigationFeature", "AnyNavigationFeatures"]}], "object": true, "qualifiedClassName": "QGeoServiceProvider", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeoserviceprovider.h", "outputRevision": 68}, {"classes": [{"className": "QGeoTiledMap", "object": true, "qualifiedClassName": "QGeoTiledMap", "slots": [{"access": "public", "arguments": [{"name": "mapId", "type": "int"}], "name": "clearScene", "returnType": "void"}, {"access": "private", "name": "handleTileVersionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGeoMap"}]}], "inputFile": "qgeotiledmap_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoTiledMappingManagerEngine", "object": true, "qualifiedClassName": "QGeoTiledMappingManagerEngine", "signals": [{"access": "public", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "errorString", "type": "QString"}], "name": "tileError", "returnType": "void"}, {"access": "public", "name": "tileVersionChanged", "returnType": "void"}], "slots": [{"access": "protected", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "bytes", "type": "QByteArray"}, {"name": "format", "type": "QString"}], "name": "engineTileFinished", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "errorString", "type": "QString"}], "name": "engineTileError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGeoMappingManagerEngine"}]}], "inputFile": "qgeotiledmappingmanagerengine_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoTiledMapReply", "object": true, "qualifiedClassName": "QGeoTiledMapReply", "signals": [{"access": "public", "name": "finished", "returnType": "void"}, {"access": "public", "name": "aborted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoTiledMapReply::Error"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoTiledMapReply::Error"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeotiledmapreply_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoTiledMapScene", "object": true, "qualifiedClassName": "QGeoTiledMapScene", "signals": [{"access": "public", "arguments": [{"name": "newTiles", "type": "QSet<QGeoTileSpec>"}], "name": "newTilesVisible", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeotiledmapscene_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoTileFetcher", "object": true, "qualifiedClassName": "QGeoTileFetcher", "signals": [{"access": "public", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "bytes", "type": "QByteArray"}, {"name": "format", "type": "QString"}], "name": "tileFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "errorString", "type": "QString"}], "name": "tileError", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "tilesAdded", "type": "QSet<QGeoTileSpec>"}, {"name": "tilesRemoved", "type": "QSet<QGeoTileSpec>"}], "name": "updateTileRequests", "returnType": "void"}, {"access": "private", "arguments": [{"name": "tiles", "type": "QSet<QGeoTileSpec>"}], "name": "cancelTileRequests", "returnType": "void"}, {"access": "private", "name": "requestNextTile", "returnType": "void"}, {"access": "private", "name": "finished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeotilefetcher_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Category"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeCategory", "enums": [{"isClass": false, "isFlag": false, "name": "Visibility", "values": ["UnspecifiedVisibility", "DeviceVisibility", "PrivateVisibility", "PublicVisibility"]}, {"isClass": false, "isFlag": false, "name": "Status", "values": ["Ready", "Saving", "Removing", "Error"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "name": "errorString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "parentId", "type": "QString"}], "name": "save", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "save", "returnType": "void"}, {"access": "public", "name": "remove", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "category", "read": "category", "required": false, "scriptable": true, "stored": true, "type": "QPlaceCategory", "user": false, "write": "setCategory"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "categoryId", "notify": "categoryIdChanged", "read": "categoryId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCategoryId"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "visibility", "notify": "visibilityChanged", "read": "visibility", "required": false, "scriptable": true, "stored": true, "type": "Visibility", "user": false, "write": "setVisibility"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "icon", "notify": "iconChanged", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QPlaceIcon", "user": false, "write": "setIcon"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "QDeclarativeCategory", "signals": [{"access": "public", "name": "pluginChanged", "returnType": "void"}, {"access": "public", "name": "categoryIdChanged", "returnType": "void"}, {"access": "public", "name": "nameChanged", "returnType": "void"}, {"access": "public", "name": "visibilityChanged", "returnType": "void"}, {"access": "public", "name": "iconChanged", "returnType": "void"}, {"access": "public", "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "private", "name": "replyFinished", "returnType": "void"}, {"access": "private", "name": "pluginReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativecategory_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ContactDetails"}, {"name": "QML.AddedInVersion", "value": "1280"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "ContactDetails instances cannot be instantiated. Only Place types have ContactDetails and they cannot be re-assigned (but can be modified)."}], "className": "QDeclarativeContactDetails", "object": true, "qualifiedClassName": "QDeclarativeContactDetails", "superClasses": [{"access": "public", "name": "QQmlPropertyMap"}]}], "inputFile": "qdeclarativecontactdetails_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Place"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePlace", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["Ready", "Saving", "Fetching", "Removing", "Error"]}, {"isClass": false, "isFlag": false, "name": "Visibility", "values": ["UnspecifiedVisibility", "DeviceVisibility", "PrivateVisibility", "PublicVisibility"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "name": "getDetails", "returnType": "void"}, {"access": "public", "name": "save", "returnType": "void"}, {"access": "public", "name": "remove", "returnType": "void"}, {"access": "public", "name": "errorString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "original", "type": "QDeclarativePlace*"}], "name": "copyFrom", "returnType": "void"}, {"access": "public", "arguments": [{"name": "plugin", "type": "QDeclarativeGeoServiceProvider*"}], "name": "initializeFavorite", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "place", "read": "place", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON>lace", "user": false, "write": "setPlace"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "categories", "notify": "categoriesChanged", "read": "categories", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QDeclarativeCategory>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "location", "notify": "locationChanged", "read": "location", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoLocation*", "user": false, "write": "setLocation"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "ratings", "notify": "ratingsChanged", "read": "ratings", "required": false, "scriptable": true, "stored": true, "type": "QPlaceRatings", "user": false, "write": "setRatings"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "supplier", "notify": "supplierChanged", "read": "supplier", "required": false, "scriptable": true, "stored": true, "type": "QPlaceSupplier", "user": false, "write": "setSupplier"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "icon", "notify": "iconChanged", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QPlaceIcon", "user": false, "write": "setIcon"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "placeId", "notify": "placeIdChanged", "read": "placeId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setPlaceId"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "attribution", "notify": "attributionChanged", "read": "attribution", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setAttribution"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "reviewModel", "notify": "reviewModelChanged", "read": "reviewModel", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlaceReviewModel*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "imageModel", "notify": "imageModelChanged", "read": "imageModel", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlaceImageModel*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "editorialModel", "notify": "editorialModelChanged", "read": "editorialModel", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlaceEditorialModel*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "extendedAttributes", "notify": "extendedAttributesChanged", "read": "extendedAttributes", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "contactDetails", "notify": "contactDetailsChanged", "read": "contactDetails", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeContactDetails*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "detailsFetched", "notify": "detailsFetchedChanged", "read": "detailsFetched", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "primaryPhone", "notify": "primaryPhoneChanged", "read": "primaryPhone", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "primaryFax", "notify": "primaryFaxChanged", "read": "primaryFax", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "primaryEmail", "notify": "primaryEmailChanged", "read": "primaryEmail", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "primaryWebsite", "notify": "primaryWebsiteChanged", "read": "primaryWebsite", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "visibility", "notify": "visibilityChanged", "read": "visibility", "required": false, "scriptable": true, "stored": true, "type": "Visibility", "user": false, "write": "setVisibility"}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "favorite", "notify": "favoriteChanged", "read": "favorite", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlace*", "user": false, "write": "setFavorite"}], "qualifiedClassName": "QDeclarativePlace", "signals": [{"access": "public", "name": "pluginChanged", "returnType": "void"}, {"access": "public", "name": "categoriesChanged", "returnType": "void"}, {"access": "public", "name": "locationChanged", "returnType": "void"}, {"access": "public", "name": "ratingsChanged", "returnType": "void"}, {"access": "public", "name": "supplierChanged", "returnType": "void"}, {"access": "public", "name": "iconChanged", "returnType": "void"}, {"access": "public", "name": "nameChanged", "returnType": "void"}, {"access": "public", "name": "placeIdChanged", "returnType": "void"}, {"access": "public", "name": "attributionChanged", "returnType": "void"}, {"access": "public", "name": "detailsFetchedChanged", "returnType": "void"}, {"access": "public", "name": "reviewModelChanged", "returnType": "void"}, {"access": "public", "name": "imageModelChanged", "returnType": "void"}, {"access": "public", "name": "editorialModelChanged", "returnType": "void"}, {"access": "public", "name": "primaryPhoneChanged", "returnType": "void"}, {"access": "public", "name": "primaryFaxChanged", "returnType": "void"}, {"access": "public", "name": "primaryEmailChanged", "returnType": "void"}, {"access": "public", "name": "primaryWebsiteChanged", "returnType": "void"}, {"access": "public", "name": "extendedAttributesChanged", "returnType": "void"}, {"access": "public", "name": "contactDetailsChanged", "returnType": "void"}, {"access": "public", "name": "statusChanged", "returnType": "void"}, {"access": "public", "name": "visibilityChanged", "returnType": "void"}, {"access": "public", "name": "favoriteChanged", "returnType": "void"}], "slots": [{"access": "private", "name": "finished", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QString"}, {"type": "Q<PERSON><PERSON><PERSON>"}], "name": "contactsModified", "returnType": "void"}, {"access": "private", "name": "pluginReady", "returnType": "void"}, {"access": "private", "name": "cleanupDeletedCategories", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativeplace_p.h", "outputRevision": 68}, {"classes": [{"className": "QDeclarativePlaceContentModel", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "place", "notify": "placeChanged", "read": "place", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlace*", "user": false, "write": "setPlace"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "batchSize", "notify": "batchSizeChanged", "read": "batchSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBatchSize"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "totalCount", "notify": "totalCountChanged", "read": "totalCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QDeclarativePlaceContentModel", "signals": [{"access": "public", "name": "placeChanged", "returnType": "void"}, {"access": "public", "name": "batchSizeChanged", "returnType": "void"}, {"access": "public", "name": "totalCountChanged", "returnType": "void"}], "slots": [{"access": "private", "name": "fetchFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "QML.Element", "value": "ReviewModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePlaceReviewModel", "gadget": true, "qualifiedClassName": "QDeclarativePlaceReviewModel", "superClasses": [{"access": "public", "name": "QDeclarativePlaceContentModel"}]}, {"classInfos": [{"name": "QML.Element", "value": "EditorialModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePlaceEditorialModel", "gadget": true, "qualifiedClassName": "QDeclarativePlaceEditorialModel", "superClasses": [{"access": "public", "name": "QDeclarativePlaceContentModel"}]}, {"classInfos": [{"name": "QML.Element", "value": "ImageModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePlaceImageModel", "gadget": true, "qualifiedClassName": "QDeclarativePlaceImageModel", "superClasses": [{"access": "public", "name": "QDeclarativePlaceContentModel"}]}], "inputFile": "qdeclarativeplacecontentmodel_p.h", "outputRevision": 68}, {"classes": [{"className": "QDeclarativeSearchModelBase", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "name": "update", "returnType": "void"}, {"access": "public", "name": "cancel", "returnType": "void"}, {"access": "public", "name": "reset", "returnType": "void"}, {"access": "public", "name": "errorString", "returnType": "QString"}, {"access": "public", "name": "previousPage", "returnType": "void"}, {"access": "public", "name": "nextPage", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "searchArea", "notify": "searchAreaChanged", "read": "searchArea", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setSearchArea"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "limit", "notify": "limitChanged", "read": "limit", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLimit"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "previousPagesAvailable", "notify": "previousPagesAvailableChanged", "read": "previousPagesAvailable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "nextPagesAvailable", "notify": "nextPagesAvailableChanged", "read": "nextPagesAvailable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "QDeclarativeSearchModelBase", "signals": [{"access": "public", "name": "pluginChanged", "returnType": "void"}, {"access": "public", "name": "searchAreaChanged", "returnType": "void"}, {"access": "public", "name": "limitChanged", "returnType": "void"}, {"access": "public", "name": "previousPagesAvailableChanged", "returnType": "void"}, {"access": "public", "name": "nextPagesAvailableChanged", "returnType": "void"}, {"access": "public", "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "protected", "name": "queryFinished", "returnType": "void"}, {"access": "protected", "name": "onContentUpdated", "returnType": "void"}, {"access": "private", "name": "pluginNameChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativesearchmodelbase_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PlaceSearchModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeSearchResultModel", "enums": [{"isClass": false, "isFlag": false, "name": "SearchResultType", "values": ["UnknownSearchResult", "PlaceResult", "ProposedSearchResult"]}, {"isClass": false, "isFlag": false, "name": "RelevanceHint", "values": ["UnspecifiedHint", "DistanceHint", "LexicalPlaceNameHint"]}], "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "<PERSON><PERSON><PERSON>", "type": "QString"}], "name": "data", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "proposedSearchIndex", "type": "int"}], "name": "updateWith", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "searchTerm", "notify": "searchTermChanged", "read": "searchTerm", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSearchTerm"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "categories", "notify": "categoriesChanged", "read": "categories", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QDeclarativeCategory>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "recommendationId", "notify": "recommendationIdChanged", "read": "recommendationId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRecommendationId"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "relevanceHint", "notify": "relevanceHintChanged", "read": "relevanceHint", "required": false, "scriptable": true, "stored": true, "type": "RelevanceHint", "user": false, "write": "setRelevanceHint"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "visibilityScope", "notify": "visibilityScopeChanged", "read": "visibilityScope", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlace::Visibility", "user": false, "write": "setVisibilityScope"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "count", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "favoritesPlugin", "notify": "favorites<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "favoritesPlugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setFavoritesPlugin"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "favoritesMatchParameters", "notify": "favoritesMatchParametersChanged", "read": "favoritesMatchParameters", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setFavoritesMatchParameters"}, {"constant": false, "designable": true, "final": false, "index": 8, "member": "m_incremental", "name": "incremental", "notify": "incrementalChanged", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QDeclarativeSearchResultModel", "signals": [{"access": "public", "name": "searchTermChanged", "returnType": "void"}, {"access": "public", "name": "categoriesChanged", "returnType": "void"}, {"access": "public", "name": "recommendationIdChanged", "returnType": "void"}, {"access": "public", "name": "relevanceHintChanged", "returnType": "void"}, {"access": "public", "name": "visibilityScopeChanged", "returnType": "void"}, {"access": "public", "name": "rowCountChanged", "returnType": "void"}, {"access": "public", "name": "favorites<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "favoritesMatchParametersChanged", "returnType": "void"}, {"access": "public", "name": "dataChanged", "returnType": "void"}, {"access": "public", "name": "incrementalChanged", "returnType": "void"}], "slots": [{"access": "protected", "name": "queryFinished", "returnType": "void"}, {"access": "protected", "name": "onContentUpdated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "favoritePlaces", "type": "QList<QPlace>"}], "name": "updateLayout", "returnType": "void"}, {"access": "private", "isCloned": true, "name": "updateLayout", "returnType": "void"}, {"access": "private", "arguments": [{"name": "placeId", "type": "QString"}], "name": "placeUpdated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "placeId", "type": "QString"}], "name": "placeRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeSearchModelBase"}]}], "inputFile": "qdeclarativesearchresultmodel_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PlaceSearchSuggestionModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeSearchSuggestionModel", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "searchTerm", "notify": "searchTermChanged", "read": "searchTerm", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSearchTerm"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "suggestions", "notify": "suggestions<PERSON><PERSON><PERSON>", "read": "suggestions", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}], "qualifiedClassName": "QDeclarativeSearchSuggestionModel", "signals": [{"access": "public", "name": "searchTermChanged", "returnType": "void"}, {"access": "public", "name": "suggestions<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "protected", "name": "queryFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeSearchModelBase"}]}], "inputFile": "qdeclarativesearchsuggestionmodel_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "CategoryModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeSupportedCategoriesModel", "enums": [{"isClass": false, "isFlag": false, "name": "Roles", "values": ["CategoryRole", "ParentCategoryRole"]}, {"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}, {"name": "role", "type": "int"}], "name": "data", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "name": "errorString", "returnType": "QString"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "hierarchical", "notify": "hierarchicalChanged", "read": "hierarchical", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHierarchical"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "QDeclarativeSupportedCategoriesModel", "signals": [{"access": "public", "name": "pluginChanged", "returnType": "void"}, {"access": "public", "name": "hierarchicalChanged", "returnType": "void"}, {"access": "public", "name": "statusChanged", "returnType": "void"}, {"access": "public", "name": "dataChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "update", "returnType": "void"}, {"access": "private", "name": "replyFinished", "returnType": "void"}, {"access": "private", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentId", "type": "QString"}], "name": "addedCategory", "returnType": "void"}, {"access": "private", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentId", "type": "QString"}], "name": "updatedCategory", "returnType": "void"}, {"access": "private", "arguments": [{"name": "categoryId", "type": "QString"}, {"name": "parentId", "type": "QString"}], "name": "removedCategory", "returnType": "void"}, {"access": "private", "name": "connectNotificationSignals", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractItemModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativesupportedcategoriesmodel_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "GeocodeModel"}], "className": "QDeclarativeGeocodeModel", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}, {"isClass": false, "isFlag": false, "name": "GeocodeError", "values": ["NoError", "EngineNotSetError", "CommunicationError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UnsupportedOptionError", "CombinationError", "UnknownE<PERSON>r", "UnknownParameterError", "MissingRequiredParameterError"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "get", "returnType": "QDeclarativeGeoLocation*"}, {"access": "public", "name": "reset", "returnType": "void"}, {"access": "public", "name": "cancel", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "autoUpdate", "notify": "autoUpdateChanged", "read": "autoUpdate", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoUpdate"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "limit", "notify": "limitChanged", "read": "limit", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLimit"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "offset", "notify": "offsetChanged", "read": "offset", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setOffset"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "query", "notify": "queryChanged", "read": "query", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "bounds", "notify": "boundsChanged", "read": "bounds", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setBounds"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "GeocodeError", "user": false}], "qualifiedClassName": "QDeclarativeGeocodeModel", "signals": [{"access": "public", "name": "countChanged", "returnType": "void"}, {"access": "public", "name": "pluginChanged", "returnType": "void"}, {"access": "public", "name": "statusChanged", "returnType": "void"}, {"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "name": "locationsChanged", "returnType": "void"}, {"access": "public", "name": "autoUpdateChanged", "returnType": "void"}, {"access": "public", "name": "boundsChanged", "returnType": "void"}, {"access": "public", "name": "queryChanged", "returnType": "void"}, {"access": "public", "name": "limitChanged", "returnType": "void"}, {"access": "public", "name": "offsetChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "update", "returnType": "void"}, {"access": "protected", "name": "queryContentChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}], "name": "geocodeFinished", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "name": "geocodeError", "returnType": "void"}, {"access": "protected", "name": "pluginReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativegeocodemodel_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "RouteModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoRouteModel", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}, {"isClass": false, "isFlag": false, "name": "RouteError", "values": ["NoError", "EngineNotSetError", "CommunicationError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UnsupportedOptionError", "UnknownE<PERSON>r", "UnknownParameterError", "MissingRequiredParameterError"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "get", "returnType": "QGeoRoute"}, {"access": "public", "name": "reset", "returnType": "void"}, {"access": "public", "name": "cancel", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "query", "notify": "queryChanged", "read": "query", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoRouteQuery*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "autoUpdate", "notify": "autoUpdateChanged", "read": "autoUpdate", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoUpdate"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "RouteError", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "measurementSystem", "notify": "measurementSystemChanged", "read": "measurementSystem", "required": false, "scriptable": true, "stored": true, "type": "QLocale::MeasurementSystem", "user": false, "write": "setMeasurementSystem"}], "qualifiedClassName": "QDeclarativeGeoRouteModel", "signals": [{"access": "public", "name": "countChanged", "returnType": "void"}, {"access": "public", "name": "pluginChanged", "returnType": "void"}, {"access": "public", "name": "queryChanged", "returnType": "void"}, {"access": "public", "name": "autoUpdateChanged", "returnType": "void"}, {"access": "public", "name": "statusChanged", "returnType": "void"}, {"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "name": "routesChanged", "returnType": "void"}, {"access": "public", "name": "measurementSystemChanged", "returnType": "void"}, {"access": "public", "name": "abortRequested", "returnType": "void"}], "slots": [{"access": "public", "name": "update", "returnType": "void"}, {"access": "private", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}], "name": "routingFinished", "returnType": "void"}, {"access": "private", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "name": "routingError", "returnType": "void"}, {"access": "private", "name": "queryDetailsChanged", "returnType": "void"}, {"access": "private", "name": "pluginReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoRouteQuery", "enums": [{"isClass": false, "isFlag": false, "name": "TravelMode", "values": ["CarTravel", "PedestrianTravel", "BicycleTravel", "PublicTransitTravel", "TruckTravel"]}, {"alias": "TravelMode", "isClass": false, "isFlag": true, "name": "TravelModes", "values": ["CarTravel", "PedestrianTravel", "BicycleTravel", "PublicTransitTravel", "TruckTravel"]}, {"isClass": false, "isFlag": false, "name": "FeatureType", "values": ["NoFeature", "TollFeature", "HighwayFeature", "PublicTransitFeature", "FerryFeature", "TunnelFeature", "DirtRoadFeature", "ParksFeature", "MotorPoolLaneFeature", "TrafficFeature"]}, {"isClass": false, "isFlag": false, "name": "FeatureWeight", "values": ["NeutralFeatureWeight", "PreferFeatureWeight", "RequireFeatureWeight", "AvoidFeatureWeight", "DisallowFeatureWeight"]}, {"isClass": false, "isFlag": false, "name": "RouteOptimization", "values": ["ShortestRoute", "FastestRoute", "MostEconomicRoute", "MostScenicRoute"]}, {"alias": "RouteOptimization", "isClass": false, "isFlag": true, "name": "RouteOptimizations", "values": ["ShortestRoute", "FastestRoute", "MostEconomicRoute", "MostScenicRoute"]}, {"isClass": false, "isFlag": false, "name": "SegmentDetail", "values": ["NoSegmentData", "BasicSegmentData"]}, {"alias": "SegmentDetail", "isClass": false, "isFlag": true, "name": "SegmentDetails", "values": ["NoSegmentData", "BasicSegmentData"]}, {"isClass": false, "isFlag": false, "name": "ManeuverDetail", "values": ["NoManeuvers", "BasicManeuvers"]}, {"alias": "ManeuverDetail", "isClass": false, "isFlag": true, "name": "ManeuverDetails", "values": ["NoManeuvers", "BasicManeuvers"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "arguments": [{"name": "w", "type": "QGeoCoordinate"}], "name": "addWaypoint", "returnType": "void"}, {"access": "public", "arguments": [{"name": "waypoint", "type": "QGeoCoordinate"}], "name": "removeWaypoint", "returnType": "void"}, {"access": "public", "name": "clearWaypoints", "returnType": "void"}, {"access": "public", "arguments": [{"name": "area", "type": "QGeoRectangle"}], "name": "addExcludedArea", "returnType": "void"}, {"access": "public", "arguments": [{"name": "area", "type": "QGeoRectangle"}], "name": "removeExcludedArea", "returnType": "void"}, {"access": "public", "name": "clearExcludedAreas", "returnType": "void"}, {"access": "public", "arguments": [{"name": "featureType", "type": "FeatureType"}, {"name": "featureWeight", "type": "FeatureWeight"}], "name": "setFeatureWeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "featureType", "type": "FeatureType"}], "name": "featureWeight", "returnType": "int"}, {"access": "public", "name": "resetFeatureWeights", "returnType": "void"}, {"access": "private", "name": "doCoordinateChanged", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "numberAlternativeRoutes", "notify": "numberAlternativeRoutesChanged", "read": "numberAlternativeRoutes", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setNumberAlternativeRoutes"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "travelModes", "notify": "travelModesChanged", "read": "travelModes", "required": false, "scriptable": true, "stored": true, "type": "TravelModes", "user": false, "write": "setTravelModes"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "routeOptimizations", "notify": "routeOptimizationsChanged", "read": "routeOptimizations", "required": false, "scriptable": true, "stored": true, "type": "RouteOptimizations", "user": false, "write": "setRouteOptimizations"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "segmentDetail", "notify": "segmentDetailChanged", "read": "segmentDetail", "required": false, "scriptable": true, "stored": true, "type": "SegmentDetail", "user": false, "write": "setSegmentDetail"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "maneuverDetail", "notify": "maneuverDetailChanged", "read": "maneuverDetail", "required": false, "scriptable": true, "stored": true, "type": "ManeuverDetail", "user": false, "write": "setManeuverDetail"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "waypoints", "notify": "waypointsChanged", "read": "waypoints", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false, "write": "setWaypoints"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoRectangle>", "user": false, "write": "setExcludedAreas"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "featureTypes", "notify": "featureTypesChanged", "read": "featureTypes", "required": false, "scriptable": true, "stored": true, "type": "QList<int>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "departureTime", "notify": "departureTimeChanged", "read": "departureTime", "required": false, "revision": 1293, "scriptable": true, "stored": true, "type": "QDateTime", "user": false, "write": "setDepartureTime"}], "qualifiedClassName": "QDeclarativeGeoRouteQuery", "signals": [{"access": "public", "name": "numberAlternativeRoutesChanged", "returnType": "void"}, {"access": "public", "name": "travelModesChanged", "returnType": "void"}, {"access": "public", "name": "routeOptimizationsChanged", "returnType": "void"}, {"access": "public", "name": "waypointsChanged", "returnType": "void"}, {"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "featureTypesChanged", "returnType": "void"}, {"access": "public", "name": "maneuverDetailChanged", "returnType": "void"}, {"access": "public", "name": "segmentDetailChanged", "returnType": "void"}, {"access": "public", "name": "queryDetailsChanged", "returnType": "void"}, {"access": "public", "name": "departureTimeChanged", "returnType": "void"}], "slots": [{"access": "private", "name": "excludedAreaCoordinateChanged", "returnType": "void"}, {"access": "private", "name": "waypointChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativegeoroutemodel_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Foreign", "value": "QDeclarativePluginParameter"}, {"name": "QML.Element", "value": "PluginParameter"}], "className": "QDeclarativePluginParameterForeign", "gadget": true, "qualifiedClassName": "QDeclarativePluginParameterForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Plugin"}, {"name": "QML.AddedInVersion", "value": "1280"}, {"name": "DefaultProperty", "value": "parameters"}], "className": "QDeclarativeGeoServiceProvider", "enums": [{"isClass": false, "isFlag": false, "name": "RoutingFeature", "values": ["NoRoutingFeatures", "OnlineRoutingFeature", "OfflineRoutingFeature", "LocalizedRoutingFeature", "RouteUpdatesFeature", "AlternativeRoutesFeature", "ExcludeAreasRoutingFeature", "AnyRoutingFeatures"]}, {"alias": "RoutingFeature", "isClass": false, "isFlag": true, "name": "RoutingFeatures", "values": ["NoRoutingFeatures", "OnlineRoutingFeature", "OfflineRoutingFeature", "LocalizedRoutingFeature", "RouteUpdatesFeature", "AlternativeRoutesFeature", "ExcludeAreasRoutingFeature", "AnyRoutingFeatures"]}, {"isClass": false, "isFlag": false, "name": "GeocodingFeature", "values": ["NoGeocodingFeatures", "OnlineGeocodingFeature", "OfflineGeocodingFeature", "ReverseGeocodingFeature", "LocalizedGeocodingFeature", "AnyGeocodingFeatures"]}, {"alias": "GeocodingFeature", "isClass": false, "isFlag": true, "name": "GeocodingFeatures", "values": ["NoGeocodingFeatures", "OnlineGeocodingFeature", "OfflineGeocodingFeature", "ReverseGeocodingFeature", "LocalizedGeocodingFeature", "AnyGeocodingFeatures"]}, {"isClass": false, "isFlag": false, "name": "MappingFeature", "values": ["NoMappingFeatures", "OnlineMappingFeature", "OfflineMappingFeature", "LocalizedMappingFeature", "AnyMappingFeatures"]}, {"alias": "MappingFeature", "isClass": false, "isFlag": true, "name": "MappingFeatures", "values": ["NoMappingFeatures", "OnlineMappingFeature", "OfflineMappingFeature", "LocalizedMappingFeature", "AnyMappingFeatures"]}, {"isClass": false, "isFlag": false, "name": "PlacesFeature", "values": ["NoPlacesFeatures", "OnlinePlacesFeature", "OfflinePlacesFeature", "SavePlaceFeature", "RemovePlaceFeature", "SaveCategoryFeature", "RemoveCategoryFeature", "PlaceRecommendationsFeature", "SearchSuggestionsFeature", "LocalizedPlacesFeature", "NotificationsFeature", "PlaceMatchingFeature", "AnyPlacesFeatures"]}, {"alias": "PlacesFeature", "isClass": false, "isFlag": true, "name": "PlacesFeatures", "values": ["NoPlacesFeatures", "OnlinePlacesFeature", "OfflinePlacesFeature", "SavePlaceFeature", "RemovePlaceFeature", "SaveCategoryFeature", "RemoveCategoryFeature", "PlaceRecommendationsFeature", "SearchSuggestionsFeature", "LocalizedPlacesFeature", "NotificationsFeature", "PlaceMatchingFeature", "AnyPlacesFeatures"]}, {"alias": "NavigationFeature", "isClass": false, "isFlag": true, "name": "NavigationFeatures", "values": ["NoNavigationFeatures", "OnlineNavigationFeature", "OfflineNavigationFeature", "AnyNavigationFeatures"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "arguments": [{"name": "feature", "type": "RoutingFeatures"}], "name": "supportsRouting", "returnType": "bool"}, {"access": "public", "isCloned": true, "name": "supportsRouting", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "feature", "type": "GeocodingFeatures"}], "name": "supportsGeocoding", "returnType": "bool"}, {"access": "public", "isCloned": true, "name": "supportsGeocoding", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "feature", "type": "MappingFeatures"}], "name": "supportsMapping", "returnType": "bool"}, {"access": "public", "isCloned": true, "name": "supportsMapping", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "feature", "type": "PlacesFeatures"}], "name": "supportsPlaces", "returnType": "bool"}, {"access": "public", "isCloned": true, "name": "supportsPlaces", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "feature", "type": "NavigationFeature"}], "name": "supportsNavigation", "returnType": "bool", "revision": 65291}, {"access": "public", "isCloned": true, "name": "supportsNavigation", "returnType": "bool", "revision": 65291}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "availableServiceProviders", "read": "availableServiceProviders", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "parameters", "read": "parameters", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QDeclarativePluginParameter>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "required", "read": "requirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProviderRequirements*", "user": false, "write": "setRequirements"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "locales", "notify": "localesChanged", "read": "locales", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setLocales"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "preferred", "notify": "preferredChanged", "read": "preferred", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setPreferred"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "allowExperimental", "notify": "allowExperimentalChanged", "read": "allowExperimental", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAllowExperimental"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "isAttached", "notify": "attached", "read": "isAttached", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QDeclarativeGeoServiceProvider", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "nameChanged", "returnType": "void"}, {"access": "public", "name": "localesChanged", "returnType": "void"}, {"access": "public", "name": "attached", "returnType": "void"}, {"access": "public", "arguments": [{"name": "preferences", "type": "QStringList"}], "name": "preferredChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "allow", "type": "bool"}], "name": "allowExperimentalChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "QML.Element", "value": "PluginRequirements"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "PluginRequirements is not intended instantiable by developer."}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoServiceProviderRequirements", "methods": [{"access": "public", "arguments": [{"name": "provider", "type": "const QGeoServiceProvider*"}], "name": "matches", "returnType": "bool"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "mapping", "notify": "mappingRequirementsChanged", "read": "mappingRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::MappingFeatures", "user": false, "write": "setMappingRequirements"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "routing", "notify": "routingRequirementsChanged", "read": "routingRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::RoutingFeatures", "user": false, "write": "setRoutingRequirements"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "geocoding", "notify": "geocodingRequirementsChanged", "read": "geocodingRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::GeocodingFeatures", "user": false, "write": "setGeocodingRequirements"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "places", "notify": "placesRequirementsChanged", "read": "placesRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::PlacesFeatures", "user": false, "write": "setPlacesRequirements"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "navigation", "notify": "navigationRequirementsChanged", "read": "navigationRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::NavigationFeatures", "user": false, "write": "setNavigationRequirements"}], "qualifiedClassName": "QDeclarativeGeoServiceProviderRequirements", "signals": [{"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::MappingFeatures"}], "name": "mappingRequirementsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::RoutingFeatures"}], "name": "routingRequirementsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::GeocodingFeatures"}], "name": "geocodingRequirementsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::PlacesFeatures"}], "name": "placesRequirementsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::NavigationFeatures"}], "name": "navigationRequirementsChanged", "returnType": "void"}, {"access": "public", "name": "requirementsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdeclarativegeoserviceprovider_p.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceContentReply", "object": true, "qualifiedClassName": "QPlaceContentReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacecontentreply.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceDetailsReply", "object": true, "qualifiedClassName": "QPlaceDetailsReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacedetailsreply.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceIdReply", "object": true, "qualifiedClassName": "QPlaceIdReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplaceidreply.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceManager", "object": true, "qualifiedClassName": "QPlaceManager", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QPlaceReply*"}], "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QPlaceReply*"}, {"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QPlaceReply*"}, {"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "name": "placeAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "name": "placeUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "name": "placeRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentId", "type": "QString"}], "name": "categoryAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentId", "type": "QString"}], "name": "categoryUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "categoryId", "type": "QString"}, {"name": "parentId", "type": "QString"}], "name": "categoryRemoved", "returnType": "void"}, {"access": "public", "name": "dataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplacemanager.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceManagerEngine", "object": true, "qualifiedClassName": "QPlaceManagerEngine", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QPlaceReply*"}], "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QPlaceReply*"}, {"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QPlaceReply*"}, {"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "name": "placeAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "name": "placeUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "name": "placeRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentCategoryId", "type": "QString"}], "name": "categoryAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentCategoryId", "type": "QString"}], "name": "categoryUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "categoryId", "type": "QString"}, {"name": "parentCategoryId", "type": "QString"}], "name": "categoryRemoved", "returnType": "void"}, {"access": "public", "name": "dataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplacemanagerengine.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceMatchReply", "object": true, "qualifiedClassName": "QPlaceMatchReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacematchreply.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceReply", "object": true, "qualifiedClassName": "QPlaceReply", "signals": [{"access": "public", "name": "finished", "returnType": "void"}, {"access": "public", "name": "contentUpdated", "returnType": "void"}, {"access": "public", "name": "aborted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}], "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "name": "abort", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplacereply.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceSearchReply", "object": true, "qualifiedClassName": "QPlaceSearchReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacesearchreply.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceSearchSuggestionReply", "object": true, "qualifiedClassName": "QPlaceSearchSuggestionReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacesearchsuggestionreply.h", "outputRevision": 68}, {"classes": [{"className": "QPlaceDetailsReplyUnsupported", "object": true, "qualifiedClassName": "QPlaceDetailsReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceDetailsReply"}]}, {"className": "QPlaceContentReplyUnsupported", "object": true, "qualifiedClassName": "QPlaceContentReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceContentReply"}]}, {"className": "QPlaceSearchReplyUnsupported", "object": true, "qualifiedClassName": "QPlaceSearchReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceSearchReply"}]}, {"className": "QPlaceSearchSuggestionReplyUnsupported", "object": true, "qualifiedClassName": "QPlaceSearchSuggestionReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceSearchSuggestionReply"}]}, {"className": "QPlaceIdReplyUnsupported", "object": true, "qualifiedClassName": "QPlaceIdReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceIdReply"}]}, {"className": "QPlaceReplyUnsupported", "object": true, "qualifiedClassName": "QPlaceReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}, {"className": "QPlaceMatchReplyUnsupported", "object": true, "qualifiedClassName": "QPlaceMatchReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceMatchReply"}]}], "inputFile": "unsupportedreplies_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapCircle"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeCircleMapItem", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "center", "notify": "centerChanged", "read": "center", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCenter"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "border", "read": "border", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeMapLineProperties*", "user": false}], "qualifiedClassName": "QDeclarativeCircleMapItem", "signals": [{"access": "public", "arguments": [{"name": "center", "type": "QGeoCoordinate"}], "name": "centerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "qreal"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "protected", "name": "markSourceDirtyAndUpdate", "returnType": "void"}, {"access": "protected", "name": "onLinePropertiesChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativecirclemapitem_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Map"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMap", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "methods": [{"access": "public", "arguments": [{"name": "bearing", "type": "qreal"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "name": "setBearing", "returnType": "void"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}, {"name": "point", "type": "QPointF"}], "name": "alignCoordinateToPoint", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QDeclarativeGeoMapItemBase*"}], "name": "removeMapItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QDeclarativeGeoMapItemBase*"}], "name": "addMapItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "itemGroup", "type": "QDeclarativeGeoMapItemGroup*"}], "name": "addMapItemGroup", "returnType": "void"}, {"access": "public", "arguments": [{"name": "itemGroup", "type": "QDeclarativeGeoMapItemGroup*"}], "name": "removeMapItemGroup", "returnType": "void"}, {"access": "public", "arguments": [{"name": "itemView", "type": "QDeclarativeGeoMapItemView*"}], "name": "removeMapItemView", "returnType": "void"}, {"access": "public", "arguments": [{"name": "itemView", "type": "QDeclarativeGeoMapItemView*"}], "name": "addMapItemView", "returnType": "void"}, {"access": "public", "name": "clearMapItems", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPointF"}, {"name": "clipToViewPort", "type": "bool"}], "name": "toCoordinate", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "isCloned": true, "name": "toCoordinate", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}, {"name": "clipToViewPort", "type": "bool"}], "name": "fromCoordinate", "returnType": "QPointF"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "isCloned": true, "name": "fromCoordinate", "returnType": "QPointF"}, {"access": "public", "arguments": [{"name": "items", "type": "QVariantList"}], "name": "fitViewportToMapItems", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "fitViewportToMapItems", "returnType": "void"}, {"access": "public", "name": "fitViewportToVisibleMapItems", "returnType": "void"}, {"access": "public", "arguments": [{"name": "dx", "type": "int"}, {"name": "dy", "type": "int"}], "name": "pan", "returnType": "void"}, {"access": "public", "name": "prefetchData", "returnType": "void"}, {"access": "public", "name": "clearData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shape", "type": "QGeoShape"}, {"name": "margins", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "fitViewportToGeoShape", "returnType": "void", "revision": 65293}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "minimumZoomLevel", "notify": "minimumZoomLevelChanged", "read": "minimumZoomLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMinimumZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "maximumZoomLevel", "notify": "maximumZoomLevelChanged", "read": "maximumZoomLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMaximumZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "zoomLevel", "notify": "zoomLevelChanged", "read": "zoomLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "tilt", "notify": "tiltChanged", "read": "tilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTilt"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "minimumTilt", "notify": "minimumTiltChanged", "read": "minimumTilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMinimumTilt"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "maximumTilt", "notify": "maximumTiltChanged", "read": "maximumTilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMaximumTilt"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "bearing", "notify": "bearingChanged", "read": "bearing", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBearing"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "fieldOfView", "notify": "fieldOfViewChanged", "read": "fieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setFieldOfView"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "minimumFieldOfView", "notify": "minimumFieldOfViewChanged", "read": "minimumFieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMinimumFieldOfView"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "maximumFieldOfView", "notify": "minimumFieldOfViewChanged", "read": "maximumFieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMaximumFieldOfView"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "activeMapType", "notify": "activeMapTypeChanged", "read": "activeMapType", "required": false, "scriptable": true, "stored": true, "type": "QGeoMapType", "user": false, "write": "setActiveMapType"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "supportedMapTypes", "notify": "supportedMapTypesChanged", "read": "supportedMapTypes", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoMapType>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "center", "notify": "centerChanged", "read": "center", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCenter"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "mapItems", "notify": "mapItemsChanged", "read": "mapItems", "required": false, "scriptable": true, "stored": true, "type": "QList<QObject*>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "QGeoServiceProvider::<PERSON><PERSON><PERSON>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "visibleRegion", "notify": "visibleRegionChanged", "read": "visibleRegion", "required": false, "scriptable": true, "stored": true, "type": "QGeoShape", "user": false, "write": "setVisibleRegion"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "copyrightsVisible", "notify": "copyrightsVisibleChanged", "read": "copyrightsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setCopyrightsVisible"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "mapReady", "notify": "mapReadyChanged", "read": "mapReady", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "visibleArea", "notify": "visibleAreaChanged", "read": "visibleArea", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "QRectF", "user": false, "write": "setVisibleArea"}], "qualifiedClassName": "QDeclarativeGeoMap", "signals": [{"access": "public", "arguments": [{"name": "plugin", "type": "QDeclarativeGeoServiceProvider*"}], "name": "pluginChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zoomLevel", "type": "qreal"}], "name": "zoomLevelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "centerChanged", "returnType": "void"}, {"access": "public", "name": "activeMapTypeChanged", "returnType": "void"}, {"access": "public", "name": "supportedMapTypesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minimumZoomLevel", "type": "qreal"}], "name": "minimumZoomLevelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "maximumZoomLevel", "type": "qreal"}], "name": "maximumZoomLevelChanged", "returnType": "void"}, {"access": "public", "name": "mapItemsChanged", "returnType": "void"}, {"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "link", "type": "QString"}], "name": "copyrightLinkActivated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "copyrightsVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bearing", "type": "qreal"}], "name": "bearingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tilt", "type": "qreal"}], "name": "tiltChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fieldOfView", "type": "qreal"}], "name": "fieldOfViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minimumTilt", "type": "qreal"}], "name": "minimumTiltChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "maximumTilt", "type": "qreal"}], "name": "maximumTiltChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minimumFieldOfView", "type": "qreal"}], "name": "minimumFieldOfViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "maximumFieldOfView", "type": "qreal"}], "name": "maximumFieldOfViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsImage", "type": "QImage"}], "name": "copyrightsImageChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsHtml", "type": "QString"}], "name": "copyrightsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ready", "type": "bool"}], "name": "mapReadyChanged", "returnType": "void"}, {"access": "public", "name": "visibleAreaChanged", "returnType": "void"}, {"access": "public", "name": "visibleRegionChanged", "returnType": "void", "revision": 65294}], "slots": [{"access": "private", "name": "mappingManagerInitialized", "returnType": "void"}, {"access": "private", "name": "pluginReady", "returnType": "void"}, {"access": "private", "name": "onSupportedMapTypesChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "oldCameraCapabilities", "type": "QGeoCameraCapabilities"}], "name": "onCameraCapabilitiesChanged", "returnType": "void"}, {"access": "private", "name": "onAttachedCopyrightNoticeVisibilityChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "cameraData", "type": "QGeoCameraData"}], "name": "onCameraDataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qdeclarativegeomap_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapCopyrightNotice"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMapCopyrightNotice", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "mapSource", "notify": "mapSourceChanged", "read": "mapSource", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoMap*", "user": false, "write": "setMapSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "styleSheet", "notify": "styleSheetChanged", "read": "styleSheet", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setStyleSheet"}], "qualifiedClassName": "QDeclarativeGeoMapCopyrightNotice", "signals": [{"access": "public", "arguments": [{"name": "link", "type": "QString"}], "name": "linkActivated", "returnType": "void"}, {"access": "public", "name": "mapSourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "backgroundColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "styleSheet", "type": "QString"}], "name": "styleSheetChanged", "returnType": "void"}, {"access": "public", "name": "copyrightsVisibleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "copyrightsImage", "type": "QImage"}], "name": "copyrightsImageChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsHtml", "type": "QString"}], "name": "copyrightsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "styleSheet", "type": "QString"}], "name": "onCopyrightsStyleSheetChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickPaintedItem"}]}], "inputFile": "qdeclarativegeomapcopyrightsnotice_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "GeoMapItemBase"}, {"name": "QML.AddedInVersion", "value": "1280"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "GeoMapItemBase is not intended instantiable by developer."}], "className": "QDeclarativeGeoMapItemBase", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "geoShape", "read": "geoShape", "required": false, "scriptable": true, "stored": false, "type": "QGeoShape", "user": false, "write": "setGeoShape"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "autoFadeIn", "read": "autoFadeIn", "required": false, "revision": 1294, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoFadeIn"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "lodThreshold", "notify": "lodThresholdChanged", "read": "lodThreshold", "required": false, "revision": 1295, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLodThreshold"}], "qualifiedClassName": "QDeclarativeGeoMapItemBase", "signals": [{"access": "public", "name": "mapItemOpacityChanged", "returnType": "void"}, {"access": "public", "name": "addTransitionFinished", "returnType": "void", "revision": 65292}, {"access": "public", "name": "removeTransitionFinished", "returnType": "void", "revision": 65292}, {"access": "public", "name": "lodThresholdChanged", "returnType": "void"}], "slots": [{"access": "protected", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "name": "afterViewportChanged", "returnType": "void"}, {"access": "protected", "name": "polishAndUpdate", "returnType": "void"}, {"access": "private", "arguments": [{"name": "camera", "type": "QGeoCameraData"}], "name": "baseCameraDataChanged", "returnType": "void"}, {"access": "private", "name": "visibleAreaChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}, {"className": "QDeclarativeGeoMapPainterPath", "object": true, "qualifiedClassName": "QDeclarativeGeoMapPainterPath", "superClasses": [{"access": "public", "name": "QQuickCurve"}]}], "inputFile": "qdeclarativegeomapitembase_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapItemGroup"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMapItemGroup", "object": true, "qualifiedClassName": "QDeclarativeGeoMapItemGroup", "signals": [{"access": "public", "name": "mapItemOpacityChanged", "returnType": "void"}, {"access": "public", "name": "addTransitionFinished", "returnType": "void"}, {"access": "public", "name": "removeTransitionFinished", "returnType": "void"}], "slots": [{"access": "protected", "name": "onMapSizeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qdeclarativegeomapitemgroup_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapItemView"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMapItemView", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "model", "notify": "modelChanged", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "delegate", "notify": "delegate<PERSON><PERSON><PERSON>", "read": "delegate", "required": false, "scriptable": true, "stored": true, "type": "QQmlComponent*", "user": false, "write": "setDelegate"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "autoFitViewport", "notify": "autoFitViewportChanged", "read": "autoFitViewport", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoFitViewport"}, {"constant": false, "designable": true, "final": false, "index": 3, "member": "m_enter", "name": "add", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "QQuickTransition*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "member": "m_exit", "name": "remove", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "QQuickTransition*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "mapItems", "read": "mapItems", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "QList<QQuickItem*>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "incubateDelegates", "notify": "incubateDelegatesChanged", "read": "incubateDelegates", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIncubateDelegates"}], "qualifiedClassName": "QDeclarativeGeoMapItemView", "signals": [{"access": "public", "name": "modelChanged", "returnType": "void"}, {"access": "public", "name": "delegate<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "autoFitViewportChanged", "returnType": "void"}, {"access": "public", "name": "incubateDelegatesChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "name": "destroyingItem", "returnType": "void"}, {"access": "private", "arguments": [{"name": "index", "type": "int"}, {"name": "object", "type": "QObject*"}], "name": "initItem", "returnType": "void"}, {"access": "private", "arguments": [{"name": "index", "type": "int"}, {"name": "object", "type": "QObject*"}], "name": "createdItem", "returnType": "void"}, {"access": "private", "arguments": [{"name": "changeSet", "type": "QQmlChangeSet"}, {"name": "reset", "type": "bool"}], "name": "modelUpdated", "returnType": "void"}, {"access": "private", "name": "exitTransitionFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemGroup"}]}], "inputFile": "qdeclarativegeomapitemview_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapQuickItem"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMapQuickItem", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "coordinate", "notify": "coordinateChanged", "read": "coordinate", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCoordinate"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "anchorPoint", "notify": "anchorPointChanged", "read": "anchorPoint", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setAnchorPoint"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zoomLevel", "notify": "zoomLevelChanged", "read": "zoomLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "sourceItem", "notify": "sourceItemChanged", "read": "sourceItem", "required": false, "scriptable": true, "stored": true, "type": "QQuickItem*", "user": false, "write": "setSourceItem"}], "qualifiedClassName": "QDeclarativeGeoMapQuickItem", "signals": [{"access": "public", "name": "coordinateChanged", "returnType": "void"}, {"access": "public", "name": "sourceItemChanged", "returnType": "void"}, {"access": "public", "name": "anchorPointChanged", "returnType": "void"}, {"access": "public", "name": "zoomLevelChanged", "returnType": "void"}], "slots": [{"access": "protected", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativegeomapquickitem_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapPolygon"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePolygonMapItem", "methods": [{"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "addCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "removeCoordinate", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "path", "notify": "pathChanged", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false, "write": "set<PERSON>ath"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "border", "read": "border", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeMapLineProperties*", "user": false}], "qualifiedClassName": "QDeclarativePolygonMapItem", "signals": [{"access": "public", "name": "pathChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "protected", "name": "markSourceDirtyAndUpdate", "returnType": "void"}, {"access": "protected", "name": "onLinePropertiesChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativepolygonmapitem_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.Element", "value": "anonymous"}], "className": "QDeclarativeMapLineProperties", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}], "qualifiedClassName": "QDeclarativeMapLineProperties", "signals": [{"access": "public", "arguments": [{"name": "width", "type": "qreal"}], "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "MapPolyline"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePolylineMapItem", "methods": [{"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "int"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "addCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "name": "insertCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "name": "replaceCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "coordinateAt", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "containsCoordinate", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "removeCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "removeCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QGeoPath"}], "name": "set<PERSON>ath", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "path", "notify": "pathChanged", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false, "write": "set<PERSON>ath"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "line", "read": "line", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeMapLineProperties*", "user": false}], "qualifiedClassName": "QDeclarativePolylineMapItem", "signals": [{"access": "public", "name": "pathChanged", "returnType": "void"}], "slots": [{"access": "protected", "name": "updateAfterLinePropertiesChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativepolylinemapitem_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapRectangle"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeRectangleMapItem", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "topLeft", "notify": "topLeftChanged", "read": "topLeft", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setTopLeft"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "bottomRight", "notify": "bottomRightChanged", "read": "bottomRight", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setBottomRight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "border", "read": "border", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeMapLineProperties*", "user": false}], "qualifiedClassName": "QDeclarativeRectangleMapItem", "signals": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QGeoCoordinate"}], "name": "topLeftChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRight", "type": "QGeoCoordinate"}], "name": "bottomRightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "protected", "name": "markSourceDirtyAndUpdate", "returnType": "void"}, {"access": "protected", "name": "onLinePropertiesChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativerectanglemapitem_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapRoute"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeRouteMapItem", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "route", "notify": "routeChanged", "read": "route", "required": false, "scriptable": true, "stored": true, "type": "QGeoRoute", "user": false, "write": "setRoute"}], "qualifiedClassName": "QDeclarativeRouteMapItem", "signals": [{"access": "public", "arguments": [{"name": "route", "type": "QGeoRoute"}], "name": "routeChanged", "returnType": "void"}], "slots": [{"access": "private", "name": "updateRoutePath", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativePolylineMapItem"}]}], "inputFile": "qdeclarativeroutemapitem_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoCameraCapabilities", "gadget": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "minimumZoomLevel", "read": "minimumZoomLevelAt256", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "maximumZoomLevel", "read": "maximumZoomLevelAt256", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "minimumTilt", "read": "minimumTilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "maximumTilt", "read": "maximumTilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "minimumFieldOfView", "read": "minimumFieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "maximumFieldOfView", "read": "maximumFieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QGeoCameraCapabilities"}], "inputFile": "qgeocameracapabilities_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "routeManeuver"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QGeoManeuver", "enums": [{"isClass": false, "isFlag": false, "name": "InstructionDirection", "values": ["NoDirection", "DirectionForward", "DirectionBearRight", "DirectionLightRight", "DirectionRight", "DirectionHardRight", "DirectionUTurnRight", "DirectionUTurnLeft", "DirectionHardLeft", "DirectionLeft", "DirectionLightLeft", "DirectionBearLeft"]}], "gadget": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "valid", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "position", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "instructionText", "read": "instructionText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "direction", "read": "direction", "required": false, "scriptable": true, "stored": true, "type": "InstructionDirection", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "timeToNextInstruction", "read": "timeToNextInstruction", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "distanceToNextInstruction", "read": "distanceToNextInstruction", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "waypoint", "read": "waypoint", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "extendedAttributes", "read": "extendedAttributes", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false}], "qualifiedClassName": "QGeoManeuver"}], "inputFile": "qgeomaneuver.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "mapType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QGeoMapType", "enums": [{"isClass": false, "isFlag": false, "name": "MapStyle", "values": ["NoMap", "StreetMap", "SatelliteMapDay", "SatelliteMapNight", "TerrainMap", "HybridMap", "TransitMap", "GrayStreetMap", "PedestrianMap", "CarNavigationMap", "CycleMap", "CustomMap"]}], "gadget": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "style", "read": "style", "required": false, "scriptable": true, "stored": true, "type": "MapStyle", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "description", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "mobile", "read": "mobile", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "night", "read": "night", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "cameraCapabilities", "read": "cameraCapabilities", "required": false, "scriptable": true, "stored": true, "type": "QGeoCameraCapabilities", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "metadata", "read": "metadata", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false}], "qualifiedClassName": "QGeoMapType"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoMapType"}, {"name": "QML.Element", "value": "MapType"}], "className": "QGeoMapTypeForeignNamespace", "namespace": true, "qualifiedClassName": "QGeoMapTypeForeignNamespace"}], "inputFile": "qgeomaptype_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "route"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QGeoRoute", "gadget": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "routeId", "read": "routeId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "bounds", "read": "bounds", "required": false, "scriptable": true, "stored": true, "type": "QGeoRectangle", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "travelTime", "read": "travelTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "distance", "read": "distance", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "path", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false, "write": "set<PERSON>ath"}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "routeLegs", "read": "routeLegs", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoRoute>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "extendedAttributes", "read": "extendedAttributes", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "legIndex", "read": "legIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "overallRoute", "read": "overallRoute", "required": false, "scriptable": true, "stored": true, "type": "QGeoRoute", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "segmentsCount", "read": "segmentsCount", "required": false, "scriptable": true, "stored": true, "type": "qsizetype", "user": false}, {"constant": true, "designable": true, "final": false, "index": 10, "name": "segments", "read": "segments", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoRouteSegment>", "user": false}], "qualifiedClassName": "QGeoRoute"}], "inputFile": "qgeoroute.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "routeSegment"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QGeoRouteSegment", "gadget": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "travelTime", "read": "travelTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "distance", "read": "distance", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "path", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "maneuver", "read": "maneuver", "required": false, "scriptable": true, "stored": true, "type": "QGeoManeuver", "user": false}], "qualifiedClassName": "QGeoRouteSegment"}], "inputFile": "qgeoroutesegment.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "placeAttribute"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceAttribute", "gadget": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "label", "read": "label", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "text", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}], "qualifiedClassName": "QPlaceAttribute"}], "inputFile": "qplaceattribute.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "contactDetail"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceContactDetail", "gadget": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "label", "read": "label", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "value", "read": "value", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setValue"}], "qualifiedClassName": "QPlaceContactDetail"}], "inputFile": "qplacecontactdetail.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "icon"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceIcon", "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "size", "type": "QSize"}], "name": "url", "returnType": "QUrl"}, {"access": "public", "isCloned": true, "name": "url", "returnType": "QUrl"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "parameters", "read": "parameters", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setParameters"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "manager", "read": "manager", "required": false, "scriptable": true, "stored": true, "type": "QPlaceManager*", "user": false, "write": "setManager"}], "qualifiedClassName": "QPlaceIcon"}], "inputFile": "qplaceicon.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ratings"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceRatings", "gadget": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "average", "read": "average", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAverage"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "maximum", "read": "maximum", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMaximum"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "count", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCount"}], "qualifiedClassName": "QPlaceRatings"}], "inputFile": "qplaceratings.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "supplier"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceSupplier", "gadget": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "supplierId", "read": "supplierId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSupplierId"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "url", "read": "url", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setUrl"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "icon", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QPlaceIcon", "user": false, "write": "setIcon"}], "qualifiedClassName": "QPlaceSupplier"}], "inputFile": "qplacesupplier.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "user"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceUser", "gadget": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "userId", "read": "userId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setUserId"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}], "qualifiedClassName": "QPlaceUser"}], "inputFile": "qplaceuser.h", "outputRevision": 68}, {"classes": [{"className": "RetryFuture", "object": true, "qualifiedClassName": "RetryFuture", "slots": [{"access": "public", "name": "retry", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeotilerequestmanager.cpp", "outputRevision": 68}]