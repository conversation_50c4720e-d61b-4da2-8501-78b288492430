[{"classes": [{"className": "QSignalEventGenerator", "object": true, "qualifiedClassName": "QSignalEventGenerator", "slots": [{"access": "private", "name": "execute", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsignaleventgenerator_p.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractState", "object": true, "properties": [{"bindable": "bindableActive", "constant": false, "designable": true, "final": false, "index": 0, "name": "active", "notify": "activeChanged", "read": "active", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QAbstractState", "signals": [{"access": "public", "name": "entered", "returnType": "void"}, {"access": "public", "name": "exited", "returnType": "void"}, {"access": "public", "arguments": [{"name": "active", "type": "bool"}], "name": "activeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractstate.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractTransition", "enums": [{"isClass": false, "isFlag": false, "name": "TransitionType", "values": ["ExternalTransition", "InternalTransition"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceState", "read": "sourceState", "required": false, "scriptable": true, "stored": true, "type": "QState*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "targetState", "notify": "targetStateChanged", "read": "targetState", "required": false, "scriptable": true, "stored": true, "type": "QAbstractState*", "user": false, "write": "setTargetState"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "targetStates", "notify": "targetStatesChanged", "read": "targetStates", "required": false, "scriptable": true, "stored": true, "type": "QList<QAbstractState*>", "user": false, "write": "setTargetStates"}, {"bindable": "bindableTransitionType", "constant": false, "designable": true, "final": false, "index": 3, "name": "transitionType", "read": "transitionType", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "TransitionType", "user": false, "write": "setTransitionType"}], "qualifiedClassName": "QAbstractTransition", "signals": [{"access": "public", "name": "triggered", "returnType": "void"}, {"access": "public", "name": "targetStateChanged", "returnType": "void"}, {"access": "public", "name": "targetStatesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstracttransition.h", "outputRevision": 68}, {"classes": [{"className": "QBasicKeyEventTransition", "object": true, "qualifiedClassName": "QBasicKeyEventTransition", "superClasses": [{"access": "public", "name": "QAbstractTransition"}]}], "inputFile": "qbasickeyeventtransition_p.h", "outputRevision": 68}, {"classes": [{"className": "QBasicMouseEventTransition", "object": true, "qualifiedClassName": "QBasicMouseEventTransition", "superClasses": [{"access": "public", "name": "QAbstractTransition"}]}], "inputFile": "qbasicmouseeventtransition_p.h", "outputRevision": 68}, {"classes": [{"className": "QEventTransition", "object": true, "properties": [{"bindable": "bindableEventSource", "constant": false, "designable": true, "final": false, "index": 0, "name": "eventSource", "read": "eventSource", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setEventSource"}, {"bindable": "bindableEventType", "constant": false, "designable": true, "final": false, "index": 1, "name": "eventType", "read": "eventType", "required": false, "scriptable": true, "stored": true, "type": "QEvent::Type", "user": false, "write": "setEventType"}], "qualifiedClassName": "QEventTransition", "superClasses": [{"access": "public", "name": "QAbstractTransition"}]}], "inputFile": "qeventtransition.h", "outputRevision": 68}, {"classes": [{"className": "QFinalState", "object": true, "qualifiedClassName": "QFinalState", "superClasses": [{"access": "public", "name": "QAbstractState"}]}], "inputFile": "qfinalstate.h", "outputRevision": 68}, {"classes": [{"className": "QHistoryState", "enums": [{"isClass": false, "isFlag": false, "name": "HistoryType", "values": ["ShallowHistory", "DeepHistory"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "defaultState", "notify": "defaultStateChanged", "read": "defaultState", "required": false, "scriptable": true, "stored": true, "type": "QAbstractState*", "user": false, "write": "setDefaultState"}, {"bindable": "bindableDefaultTransition", "constant": false, "designable": true, "final": false, "index": 1, "name": "defaultTransition", "notify": "defaultTransitionChanged", "read": "defaultTransition", "required": false, "scriptable": true, "stored": true, "type": "QAbstractTransition*", "user": false, "write": "setDefaultTransition"}, {"bindable": "bindableHistoryType", "constant": false, "designable": true, "final": false, "index": 2, "name": "historyType", "notify": "historyTypeChanged", "read": "historyType", "required": false, "scriptable": true, "stored": true, "type": "HistoryType", "user": false, "write": "setHistoryType"}], "qualifiedClassName": "QHistoryState", "signals": [{"access": "public", "name": "defaultTransitionChanged", "returnType": "void"}, {"access": "public", "name": "defaultStateChanged", "returnType": "void"}, {"access": "public", "name": "historyTypeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractState"}]}], "inputFile": "qhistorystate.h", "outputRevision": 68}, {"classes": [{"className": "QKeyEventTransition", "object": true, "properties": [{"bindable": "bindableKey", "constant": false, "designable": true, "final": false, "index": 0, "name": "key", "read": "key", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON>"}, {"bindable": "bindableModifierMask", "constant": false, "designable": true, "final": false, "index": 1, "name": "modifierMask", "read": "modifierMask", "required": false, "scriptable": true, "stored": true, "type": "Qt::KeyboardModifiers", "user": false, "write": "setModifierMask"}], "qualifiedClassName": "QKeyEventTransition", "superClasses": [{"access": "public", "name": "QEventTransition"}]}], "inputFile": "qkeyeventtransition.h", "outputRevision": 68}, {"classes": [{"className": "QMouseEventTransition", "object": true, "properties": [{"bindable": "bindableButton", "constant": false, "designable": true, "final": false, "index": 0, "name": "button", "read": "button", "required": false, "scriptable": true, "stored": true, "type": "Qt::<PERSON><PERSON><PERSON><PERSON>", "user": false, "write": "setButton"}, {"bindable": "bindableModifierMask", "constant": false, "designable": true, "final": false, "index": 1, "name": "modifierMask", "read": "modifierMask", "required": false, "scriptable": true, "stored": true, "type": "Qt::KeyboardModifiers", "user": false, "write": "setModifierMask"}], "qualifiedClassName": "QMouseEventTransition", "superClasses": [{"access": "public", "name": "QEventTransition"}]}], "inputFile": "qmouseeventtransition.h", "outputRevision": 68}, {"classes": [{"className": "QSignalTransition", "object": true, "properties": [{"bindable": "bindableSenderObject", "constant": false, "designable": true, "final": false, "index": 0, "name": "senderObject", "notify": "senderObjectChanged", "read": "senderObject", "required": false, "scriptable": true, "stored": true, "type": "const QObject*", "user": false, "write": "setSenderObject"}, {"bindable": "bindableSignal", "constant": false, "designable": true, "final": false, "index": 1, "name": "signal", "notify": "signalChanged", "read": "signal", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false, "write": "setSignal"}], "qualifiedClassName": "QSignalTransition", "signals": [{"access": "public", "name": "senderObjectChanged", "returnType": "void"}, {"access": "public", "name": "signalChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractTransition"}]}], "inputFile": "qsignaltransition.h", "outputRevision": 68}, {"classes": [{"className": "QState", "enums": [{"isClass": false, "isFlag": false, "name": "ChildMode", "values": ["ExclusiveStates", "ParallelStates"]}, {"isClass": false, "isFlag": false, "name": "RestorePolicy", "values": ["DontRestoreProperties", "RestoreProperties"]}], "object": true, "properties": [{"bindable": "bindableInitialState", "constant": false, "designable": true, "final": false, "index": 0, "name": "initialState", "notify": "initialStateChanged", "read": "initialState", "required": false, "scriptable": true, "stored": true, "type": "QAbstractState*", "user": false, "write": "setInitialState"}, {"bindable": "bindableErrorState", "constant": false, "designable": true, "final": false, "index": 1, "name": "errorState", "notify": "errorStateChanged", "read": "errorState", "required": false, "scriptable": true, "stored": true, "type": "QAbstractState*", "user": false, "write": "setErrorState"}, {"bindable": "bindableChildMode", "constant": false, "designable": true, "final": false, "index": 2, "name": "childMode", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "childMode", "required": false, "scriptable": true, "stored": true, "type": "ChildMode", "user": false, "write": "setChildMode"}], "qualifiedClassName": "QState", "signals": [{"access": "public", "name": "finished", "returnType": "void"}, {"access": "public", "name": "propertiesAssigned", "returnType": "void"}, {"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "initialStateChanged", "returnType": "void"}, {"access": "public", "name": "errorStateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractState"}]}], "inputFile": "qstate.h", "outputRevision": 68}, {"classes": [{"className": "QStateMachine", "object": true, "properties": [{"bindable": "bindableErrorString", "constant": false, "designable": true, "final": false, "index": 0, "name": "errorString", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"bindable": "bindableGlobalRestorePolicy", "constant": false, "designable": true, "final": false, "index": 1, "name": "globalRestorePolicy", "read": "globalRestorePolicy", "required": false, "scriptable": true, "stored": true, "type": "QState::RestorePolicy", "user": false, "write": "setGlobalRestorePolicy"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "running", "notify": "running<PERSON><PERSON>ed", "read": "isRunning", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRunning"}, {"bindable": "bindableAnimated", "constant": false, "designable": true, "final": false, "index": 3, "name": "animated", "read": "isAnimated", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAnimated"}], "qualifiedClassName": "QStateMachine", "signals": [{"access": "public", "name": "started", "returnType": "void"}, {"access": "public", "name": "stopped", "returnType": "void"}, {"access": "public", "arguments": [{"name": "running", "type": "bool"}], "name": "running<PERSON><PERSON>ed", "returnType": "void"}], "slots": [{"access": "public", "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "running", "type": "bool"}], "name": "setRunning", "returnType": "void"}, {"access": "private", "name": "_q_start", "returnType": "void"}, {"access": "private", "name": "_q_process", "returnType": "void"}, {"access": "private", "name": "_q_animationFinished", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}, {"type": "int"}], "name": "_q_startDelayedEventTimer", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}, {"type": "int"}], "name": "_q_kill<PERSON><PERSON><PERSON>EventTimer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QState"}]}], "inputFile": "qstatemachine.h", "outputRevision": 68}, {"classes": [{"className": "DefaultStateTransition", "object": true, "qualifiedClassName": "DefaultStateTransition", "superClasses": [{"access": "public", "name": "QAbstractTransition"}]}], "inputFile": "qhistorystate.cpp", "outputRevision": 68}, {"classes": [{"className": "GoToStateTransition", "object": true, "qualifiedClassName": "_QStateMachine_Internal::GoToStateTransition", "superClasses": [{"access": "public", "name": "QAbstractTransition"}]}], "inputFile": "qstatemachine.cpp", "outputRevision": 68}]