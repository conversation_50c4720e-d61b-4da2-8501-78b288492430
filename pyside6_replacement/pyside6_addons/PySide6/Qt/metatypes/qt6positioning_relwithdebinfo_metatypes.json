[{"classes": [{"className": "QGeoSatelliteInfo", "enums": [{"isClass": false, "isFlag": false, "name": "Attribute", "values": ["Elevation", "Azimuth"]}, {"isClass": false, "isFlag": false, "name": "SatelliteSystem", "values": ["Undefined", "GPS", "GLONASS", "GALILEO", "BEIDOU", "QZSS", "Multiple", "CustomType"]}], "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "attribute", "type": "Attribute"}], "name": "attribute", "returnType": "qreal"}, {"access": "public", "arguments": [{"name": "attribute", "type": "Attribute"}], "name": "hasAttribute", "returnType": "bool"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "satelliteSystem", "read": "satelliteSystem", "required": false, "scriptable": true, "stored": true, "type": "SatelliteSystem", "user": false}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "satelliteIdentifier", "read": "satelliteIdentifier", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "signalStrength", "read": "signalStrength", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QGeoSatelliteInfo"}], "inputFile": "qgeosatelliteinfo.h", "outputRevision": 68}, {"classes": [{"className": "QGeoAreaMonitorSource", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["AccessError", "InsufficientPositionInfo", "UnknownSourceError", "NoError"]}], "object": true, "qualifiedClassName": "QGeoAreaMonitorSource", "signals": [{"access": "public", "arguments": [{"name": "monitor", "type": "QGeoAreaMonitorInfo"}, {"name": "update", "type": "QGeoPositionInfo"}], "name": "areaEntered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "monitor", "type": "QGeoAreaMonitorInfo"}, {"name": "update", "type": "QGeoPositionInfo"}], "name": "areaExited", "returnType": "void"}, {"access": "public", "arguments": [{"name": "monitor", "type": "QGeoAreaMonitorInfo"}], "name": "monitorExpired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoAreaMonitorSource::Error"}], "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeoareamonitorsource.h", "outputRevision": 68}, {"classes": [{"className": "QGeoCircle", "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "degreesLatitude", "type": "double"}, {"name": "degreesLongitude", "type": "double"}], "name": "translate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "degreesLatitude", "type": "double"}, {"name": "degreesLongitude", "type": "double"}], "name": "translated", "returnType": "QGeoCircle"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "extendCircle", "returnType": "void"}, {"access": "public", "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "center", "read": "center", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCenter"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "radius", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRadius"}], "qualifiedClassName": "QGeoCircle", "superClasses": [{"access": "public", "name": "QGeoShape"}]}], "inputFile": "qgeocircle.h", "outputRevision": 68}, {"classes": [{"className": "QGeoCoordinate", "enums": [{"isClass": false, "isFlag": false, "name": "CoordinateFormat", "values": ["Degrees", "DegreesWithHemisphere", "DegreesMinutes", "DegreesMinutesWithHemisphere", "DegreesMinutesSeconds", "DegreesMinutesSecondsWithHemisphere"]}], "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "other", "type": "QGeoCoordinate"}], "name": "distanceTo", "returnType": "qreal"}, {"access": "public", "arguments": [{"name": "other", "type": "QGeoCoordinate"}], "name": "azimuthTo", "returnType": "qreal"}, {"access": "public", "arguments": [{"name": "distance", "type": "qreal"}, {"name": "azimuth", "type": "qreal"}, {"name": "distanceUp", "type": "qreal"}], "name": "atDistanceAndAzimuth", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "distance", "type": "qreal"}, {"name": "azimuth", "type": "qreal"}], "isCloned": true, "name": "atDistanceAndAzimuth", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "format", "type": "CoordinateFormat"}], "name": "toString", "returnType": "QString"}, {"access": "public", "isCloned": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "latitude", "read": "latitude", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false, "write": "setLatitude"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "longitude", "read": "longitude", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false, "write": "setLongitude"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "altitude", "read": "altitude", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false, "write": "setAltitude"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QGeoCoordinate"}], "inputFile": "qgeocoordinate.h", "outputRevision": 68}, {"classes": [{"className": "QGeoCoordinateObject", "object": true, "properties": [{"bindable": "bindableCoordinate", "constant": false, "designable": true, "final": false, "index": 0, "name": "coordinate", "notify": "coordinateChanged", "read": "coordinate", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCoordinate"}], "qualifiedClassName": "QGeoCoordinateObject", "signals": [{"access": "public", "name": "coordinateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeocoordinateobject_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoPath", "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "degreesLatitude", "type": "double"}, {"name": "degreesLongitude", "type": "double"}], "name": "translate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "degreesLatitude", "type": "double"}, {"name": "degreesLongitude", "type": "double"}], "name": "translated", "returnType": "QGeoPath"}, {"access": "public", "arguments": [{"name": "indexFrom", "type": "qsizetype"}, {"name": "indexTo", "type": "qsizetype"}], "name": "length", "returnType": "double"}, {"access": "public", "arguments": [{"name": "indexFrom", "type": "qsizetype"}], "isCloned": true, "name": "length", "returnType": "double"}, {"access": "public", "isCloned": true, "name": "length", "returnType": "double"}, {"access": "public", "name": "size", "returnType": "qsizetype"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "addCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "name": "insertCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "name": "replaceCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}], "name": "coordinateAt", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "containsCoordinate", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "removeCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}], "name": "removeCoordinate", "returnType": "void"}, {"access": "public", "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "path", "read": "variantPath", "required": false, "scriptable": true, "stored": true, "type": "QVariantList", "user": false, "write": "setVariantPath"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "width", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QGeoPath", "superClasses": [{"access": "public", "name": "QGeoShape"}]}], "inputFile": "qgeopath.h", "outputRevision": 68}, {"classes": [{"className": "QGeoPathEager", "gadget": true, "qualifiedClassName": "QGeoPathEager", "superClasses": [{"access": "public", "name": "QGeoPath"}]}], "inputFile": "qgeopath_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoPolygon", "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "holePath", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "addHole", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}], "name": "hole", "returnType": "QVariantList"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}], "name": "removeHole", "returnType": "void"}, {"access": "public", "name": "holesCount", "returnType": "qsizetype"}, {"access": "public", "arguments": [{"name": "degreesLatitude", "type": "double"}, {"name": "degreesLongitude", "type": "double"}], "name": "translate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "degreesLatitude", "type": "double"}, {"name": "degreesLongitude", "type": "double"}], "name": "translated", "returnType": "QGeoPolygon"}, {"access": "public", "arguments": [{"name": "indexFrom", "type": "qsizetype"}, {"name": "indexTo", "type": "qsizetype"}], "name": "length", "returnType": "double"}, {"access": "public", "arguments": [{"name": "indexFrom", "type": "qsizetype"}], "isCloned": true, "name": "length", "returnType": "double"}, {"access": "public", "isCloned": true, "name": "length", "returnType": "double"}, {"access": "public", "name": "size", "returnType": "qsizetype"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "addCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "name": "insertCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "name": "replaceCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}], "name": "coordinateAt", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "containsCoordinate", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "removeCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "qsizetype"}], "name": "removeCoordinate", "returnType": "void"}, {"access": "public", "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "perimeter", "read": "perimeter", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false, "write": "setPerimeter"}], "qualifiedClassName": "QGeoPolygon", "superClasses": [{"access": "public", "name": "QGeoShape"}]}], "inputFile": "qgeopolygon.h", "outputRevision": 68}, {"classes": [{"className": "QGeoPolygonEager", "gadget": true, "qualifiedClassName": "QGeoPolygonEager", "superClasses": [{"access": "public", "name": "QGeoPolygon"}]}], "inputFile": "qgeopolygon_p.h", "outputRevision": 68}, {"classes": [{"className": "QGeoPositionInfoSource", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["AccessError", "ClosedError", "UnknownSourceError", "NoError", "UpdateTimeoutError"]}], "object": true, "properties": [{"bindable": "bindableUpdateInterval", "constant": false, "designable": true, "final": false, "index": 0, "name": "updateInterval", "read": "updateInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setUpdateInterval"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "minimumUpdateInterval", "read": "minimumUpdateInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "sourceName", "read": "sourceName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"bindable": "bindablePreferredPositioningMethods", "constant": false, "designable": true, "final": false, "index": 3, "name": "preferredPositioningMethods", "read": "preferredPositioningMethods", "required": false, "scriptable": true, "stored": true, "type": "PositioningMethods", "user": false, "write": "setPreferredPositioningMethods"}], "qualifiedClassName": "QGeoPositionInfoSource", "signals": [{"access": "public", "arguments": [{"name": "update", "type": "QGeoPositionInfo"}], "name": "positionUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QGeoPositionInfoSource::Error"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "name": "supportedPositioningMethodsChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "startUpdates", "returnType": "void"}, {"access": "public", "name": "stopUpdates", "returnType": "void"}, {"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "name": "requestUpdate", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "requestUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeopositioninfosource.h", "outputRevision": 68}, {"classes": [{"className": "QGeoRectangle", "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "rectangle", "type": "QGeoRectangle"}], "name": "intersects", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "degreesLatitude", "type": "double"}, {"name": "degreesLongitude", "type": "double"}], "name": "translate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "degreesLatitude", "type": "double"}, {"name": "degreesLongitude", "type": "double"}], "name": "translated", "returnType": "QGeoRectangle"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "extendRectangle", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rectangle", "type": "QGeoRectangle"}], "name": "united", "returnType": "QGeoRectangle"}, {"access": "public", "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "bottomLeft", "read": "bottomLeft", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setBottomLeft"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "bottomRight", "read": "bottomRight", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setBottomRight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "topLeft", "read": "topLeft", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setTopLeft"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "topRight", "read": "topRight", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setTopRight"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "center", "read": "center", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCenter"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "height", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "width", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QGeoRectangle", "superClasses": [{"access": "public", "name": "QGeoShape"}]}], "inputFile": "qgeorectangle.h", "outputRevision": 68}, {"classes": [{"className": "QGeoSatelliteInfoSource", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["AccessError", "ClosedError", "NoError", "UnknownSourceError", "UpdateTimeoutError"]}], "object": true, "properties": [{"bindable": "bindableUpdateInterval", "constant": false, "designable": true, "final": false, "index": 0, "name": "updateInterval", "read": "updateInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setUpdateInterval"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "minimumUpdateInterval", "read": "minimumUpdateInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QGeoSatelliteInfoSource", "signals": [{"access": "public", "arguments": [{"name": "satellites", "type": "QList<QGeoSatelliteInfo>"}], "name": "satellitesInViewUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "satellites", "type": "QList<QGeoSatelliteInfo>"}], "name": "satellitesInUseUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QGeoSatelliteInfoSource::Error"}], "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "name": "startUpdates", "returnType": "void"}, {"access": "public", "name": "stopUpdates", "returnType": "void"}, {"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "name": "requestUpdate", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "requestUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeosatelliteinfosource.h", "outputRevision": 68}, {"classes": [{"className": "QGeoShape", "constructors": [{"access": "public", "arguments": [{"name": "other", "type": "QGeoShape"}], "name": "QGeoShape", "returnType": ""}], "enums": [{"isClass": false, "isFlag": false, "name": "ShapeType", "values": ["UnknownType", "RectangleType", "CircleType", "PathType", "PolygonType"]}], "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "name": "contains", "returnType": "bool"}, {"access": "public", "name": "boundingGeoRectangle", "returnType": "QGeoRectangle"}, {"access": "public", "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "ShapeType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "isEmpty", "read": "isEmpty", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "center", "read": "center", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false}], "qualifiedClassName": "QGeoShape"}], "inputFile": "qgeoshape.h", "outputRevision": 68}, {"classes": [{"className": "QNmeaPositionInfoSource", "object": true, "qualifiedClassName": "QNmeaPositionInfoSource", "slots": [{"access": "public", "name": "startUpdates", "returnType": "void"}, {"access": "public", "name": "stopUpdates", "returnType": "void"}, {"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "name": "requestUpdate", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "requestUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGeoPositionInfoSource"}]}], "inputFile": "qnmeapositioninfosource.h", "outputRevision": 68}, {"classes": [{"className": "QNmeaPositionInfoSourcePrivate", "object": true, "qualifiedClassName": "QNmeaPositionInfoSourcePrivate", "slots": [{"access": "public", "name": "readyRead", "returnType": "void"}, {"access": "private", "name": "emitPendingUpdate", "returnType": "void"}, {"access": "private", "name": "sourceDataClosed", "returnType": "void"}, {"access": "private", "name": "updateRequestTimeout", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QNmeaSimulatedReader", "object": true, "qualifiedClassName": "QNmeaSimulatedReader", "slots": [{"access": "private", "name": "simulatePendingUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QNmeaReader"}]}], "inputFile": "qnmeapositioninfosource_p.h", "outputRevision": 68}, {"classes": [{"className": "QNmeaSatelliteInfoSource", "object": true, "qualifiedClassName": "QNmeaSatelliteInfoSource", "slots": [{"access": "public", "name": "startUpdates", "returnType": "void"}, {"access": "public", "name": "stopUpdates", "returnType": "void"}, {"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "name": "requestUpdate", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "requestUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGeoSatelliteInfoSource"}]}], "inputFile": "qnmeasatelliteinfosource.h", "outputRevision": 68}, {"classes": [{"className": "QNmeaSatelliteInfoSourcePrivate", "object": true, "qualifiedClassName": "QNmeaSatelliteInfoSourcePrivate", "slots": [{"access": "public", "name": "readyRead", "returnType": "void"}, {"access": "public", "name": "emitPendingUpdate", "returnType": "void"}, {"access": "public", "name": "sourceDataClosed", "returnType": "void"}, {"access": "public", "name": "updateRequestTimeout", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnmeasatelliteinfosource_p.h", "outputRevision": 68}]