[{"classes": [{"className": "QMetaObjectPublisher", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "propertyUpdateIntervalTime", "read": "propertyUpdateInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setPropertyUpdateInterval"}], "qualifiedClassName": "QMetaObjectPublisher", "signals": [{"access": "public", "arguments": [{"name": "block", "type": "bool"}], "name": "blockUpdatesChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "message", "type": "QJsonObject"}, {"name": "transport", "type": "QWebChannelAbstractTransport*"}], "name": "handleMessage", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmetaobjectpublisher_p.h", "outputRevision": 68}, {"classes": [{"className": "QQmlWebChannelAttached", "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "id", "notify": "idChanged", "read": "id", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setId"}], "qualifiedClassName": "QQmlWebChannelAttached", "signals": [{"access": "public", "arguments": [{"name": "id", "type": "QString"}], "name": "idChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlwebchannelattached_p.h", "outputRevision": 68}, {"classes": [{"className": "QWebChannelAbstractTransport", "object": true, "qualifiedClassName": "QWebChannelAbstractTransport", "signals": [{"access": "public", "arguments": [{"name": "message", "type": "QJsonObject"}, {"name": "transport", "type": "QWebChannelAbstractTransport*"}], "name": "messageReceived", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "message", "type": "QJsonObject"}], "name": "sendMessage", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwebchannelabstracttransport.h", "outputRevision": 68}, {"classes": [{"className": "QQmlWebChannel", "methods": [{"access": "public", "arguments": [{"name": "objects", "type": "QVariantMap"}], "name": "registerObjects", "returnType": "void"}, {"access": "public", "arguments": [{"name": "transport", "type": "QObject*"}], "name": "connectTo", "returnType": "void"}, {"access": "public", "arguments": [{"name": "transport", "type": "QObject*"}], "name": "disconnectFrom", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "transports", "read": "transports", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "registeredObjects", "read": "registeredObjects", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "QQmlWebChannel", "slots": [{"access": "private", "arguments": [{"name": "newId", "type": "QString"}], "name": "_q_objectIdChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWebChannel"}]}], "inputFile": "qqmlwebchannel.h", "outputRevision": 68}, {"classes": [{"className": "QWebChannel", "methods": [{"access": "public", "arguments": [{"name": "id", "type": "QString"}, {"name": "object", "type": "QObject*"}], "name": "registerObject", "returnType": "void"}, {"access": "public", "arguments": [{"name": "object", "type": "QObject*"}], "name": "deregisterObject", "returnType": "void"}], "object": true, "properties": [{"bindable": "bindableBlockUpdates", "constant": false, "designable": true, "final": false, "index": 0, "name": "blockUpdates", "notify": "blockUpdatesChanged", "read": "blockUpdates", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBlockUpdates"}, {"bindable": "bindablePropertyUpdateInterval", "constant": false, "designable": true, "final": false, "index": 1, "name": "propertyUpdateInterval", "read": "propertyUpdateInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setPropertyUpdateInterval"}], "qualifiedClassName": "QWebChannel", "signals": [{"access": "public", "arguments": [{"name": "block", "type": "bool"}], "name": "blockUpdatesChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "transport", "type": "QWebChannelAbstractTransport*"}], "name": "connectTo", "returnType": "void"}, {"access": "public", "arguments": [{"name": "transport", "type": "QWebChannelAbstractTransport*"}], "name": "disconnectFrom", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QObject*"}], "name": "_q_transportDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwebchannel.h", "outputRevision": 68}]