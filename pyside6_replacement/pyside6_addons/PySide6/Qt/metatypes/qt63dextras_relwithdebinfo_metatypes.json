[{"classes": [{"className": "DistanceFieldTextRenderer", "object": true, "qualifiedClassName": "Qt3DExtras::DistanceFieldTextRenderer", "superClasses": [{"access": "public", "name": "Qt3DCore::QEntity"}]}], "inputFile": "distancefieldtextrenderer_p.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractCameraController", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "camera", "notify": "cameraChanged", "read": "camera", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QCamera*", "user": false, "write": "setCamera"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "linearSpeed", "notify": "linearSpeedChanged", "read": "linearSpeed", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLinearSpeed"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "lookSpeed", "notify": "lookSpeedChanged", "read": "lookSpeed", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLookSpeed"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "acceleration", "notify": "accelerationChanged", "read": "acceleration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAcceleration"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "deceleration", "notify": "decelerationChanged", "read": "deceleration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDeceleration"}], "qualifiedClassName": "Qt3DExtras::QAbstractCameraController", "signals": [{"access": "public", "name": "cameraChanged", "returnType": "void"}, {"access": "public", "name": "linearSpeedChanged", "returnType": "void"}, {"access": "public", "name": "lookSpeedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "acceleration", "type": "float"}], "name": "accelerationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "deceleration", "type": "float"}], "name": "decelerationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QEntity"}]}], "inputFile": "qabstractcameracontroller.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractSpriteSheet", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "texture", "notify": "textureChanged", "read": "texture", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setTexture"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "textureTransform", "notify": "textureTransformChanged", "read": "textureTransform", "required": false, "scriptable": true, "stored": true, "type": "QMatrix3x3", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "currentIndex", "notify": "currentIndexChanged", "read": "currentIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCurrentIndex"}], "qualifiedClassName": "Qt3DExtras::QAbstractSpriteSheet", "signals": [{"access": "public", "arguments": [{"name": "texture", "type": "Qt3DRender::QAbstractTexture*"}], "name": "textureChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureTransform", "type": "QMatrix3x3"}], "name": "textureTransformChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "currentIndex", "type": "int"}], "name": "currentIndexChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "texture", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setTexture", "returnType": "void"}, {"access": "public", "arguments": [{"name": "currentIndex", "type": "int"}], "name": "setCurrentIndex", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractspritesheet.h", "outputRevision": 68}, {"classes": [{"className": "QConeGeometry", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "hasTopEndcap", "notify": "hasTopEndcapChanged", "read": "hasTopEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasTopEndcap"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "hasBottomEndcap", "notify": "hasBottomEndcapChanged", "read": "hasBottomEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasBottomEndcap"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "topRadius", "notify": "topRadiusChanged", "read": "topRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTopRadius"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "bottomRadius", "notify": "bottomRadiusChanged", "read": "bottomRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBottomRadius"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 10, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QConeGeometry", "signals": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "name": "hasTopEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "name": "hasBottomEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "name": "topRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "name": "bottomRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "name": "setHasTopEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "name": "setHasBottomEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "name": "setTopRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "name": "setBottomRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qconegeometry.h", "outputRevision": 68}, {"classes": [{"className": "QConeGeometryView", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "hasTopEndcap", "notify": "hasTopEndcapChanged", "read": "hasTopEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasTopEndcap"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "hasBottomEndcap", "notify": "hasBottomEndcapChanged", "read": "hasBottomEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasBottomEndcap"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "topRadius", "notify": "topRadiusChanged", "read": "topRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTopRadius"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "bottomRadius", "notify": "bottomRadiusChanged", "read": "bottomRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBottomRadius"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QConeGeometryView", "signals": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "name": "hasTopEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "name": "hasBottomEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "name": "topRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "name": "bottomRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "name": "setHasTopEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "name": "setHasBottomEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "name": "setTopRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "name": "setBottomRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qconegeometryview.h", "outputRevision": 68}, {"classes": [{"className": "QConeMesh", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "hasTopEndcap", "notify": "hasTopEndcapChanged", "read": "hasTopEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasTopEndcap"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "hasBottomEndcap", "notify": "hasBottomEndcapChanged", "read": "hasBottomEndcap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasBottomEndcap"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "topRadius", "notify": "topRadiusChanged", "read": "topRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTopRadius"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "bottomRadius", "notify": "bottomRadiusChanged", "read": "bottomRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBottomRadius"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QConeMesh", "signals": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "name": "hasTopEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "name": "hasBottomEndcapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "name": "topRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "name": "bottomRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "hasTopEndcap", "type": "bool"}], "name": "setHasTopEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hasBottomEndcap", "type": "bool"}], "name": "setHasBottomEndcap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topRadius", "type": "float"}], "name": "setTopRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRadius", "type": "float"}], "name": "setBottomRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qconemesh.h", "outputRevision": 68}, {"classes": [{"className": "QCuboidGeometry", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "xExtent", "notify": "xExtentChanged", "read": "xExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setXExtent"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "yExtent", "notify": "yExtentChanged", "read": "yExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setYExtent"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zExtent", "notify": "zExtentChanged", "read": "zExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZExtent"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "xyMeshResolution", "notify": "xyMeshResolutionChanged", "read": "xyMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXYMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "yzMeshResolution", "notify": "yzMeshResolutionChanged", "read": "yzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setYZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "xzMeshResolution", "notify": "xzMeshResolutionChanged", "read": "xzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXZMeshResolution"}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "tangentAttribute", "read": "tangentAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 10, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QCuboidGeometry", "signals": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "name": "xExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "name": "yExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "name": "zExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yzMeshResolution", "type": "QSize"}], "name": "yzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xzMeshResolution", "type": "QSize"}], "name": "xzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xyMeshResolution", "type": "QSize"}], "name": "xyMeshResolutionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "name": "setXExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "name": "setYExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "name": "setZExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setYZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setXZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setXYMeshResolution", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qcuboidgeometry.h", "outputRevision": 68}, {"classes": [{"className": "QCuboidGeometryView", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "xExtent", "notify": "xExtentChanged", "read": "xExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setXExtent"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "yExtent", "notify": "yExtentChanged", "read": "yExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setYExtent"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zExtent", "notify": "zExtentChanged", "read": "zExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZExtent"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "yzMeshResolution", "notify": "yzMeshResolutionChanged", "read": "yzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setYZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "xzMeshResolution", "notify": "xzMeshResolutionChanged", "read": "xzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "xyMeshResolution", "notify": "xyMeshResolutionChanged", "read": "xyMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXYMeshResolution"}], "qualifiedClassName": "Qt3DExtras::QCuboidGeometryView", "signals": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "name": "xExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "name": "yExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "name": "zExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yzMeshResolution", "type": "QSize"}], "name": "yzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xzMeshResolution", "type": "QSize"}], "name": "xzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xyMeshResolution", "type": "QSize"}], "name": "xyMeshResolutionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "name": "setXExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "name": "setYExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "name": "setZExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setYZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setXZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setXYMeshResolution", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qcuboidgeometryview.h", "outputRevision": 68}, {"classes": [{"className": "QCuboidMesh", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "xExtent", "notify": "xExtentChanged", "read": "xExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setXExtent"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "yExtent", "notify": "yExtentChanged", "read": "yExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setYExtent"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zExtent", "notify": "zExtentChanged", "read": "zExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZExtent"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "yzMeshResolution", "notify": "yzMeshResolutionChanged", "read": "yzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setYZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "xzMeshResolution", "notify": "xzMeshResolutionChanged", "read": "xzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXZMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "xyMeshResolution", "notify": "xyMeshResolutionChanged", "read": "xyMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXYMeshResolution"}], "qualifiedClassName": "Qt3DExtras::QCuboidMesh", "signals": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "name": "xExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "name": "yExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "name": "zExtentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yzMeshResolution", "type": "QSize"}], "name": "yzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xzMeshResolution", "type": "QSize"}], "name": "xzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "xyMeshResolution", "type": "QSize"}], "name": "xyMeshResolutionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "xExtent", "type": "float"}], "name": "setXExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "yExtent", "type": "float"}], "name": "setYExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zExtent", "type": "float"}], "name": "setZExtent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setYZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setXZMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setXYMeshResolution", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qcuboidmesh.h", "outputRevision": 68}, {"classes": [{"className": "QCylinderGeometry", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QCylinderGeometry", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qcylindergeometry.h", "outputRevision": 68}, {"classes": [{"className": "QCylinderGeometryView", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QCylinderGeometryView", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qcylindergeometryview.h", "outputRevision": 68}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON>", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "lengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qcylindermesh.h", "outputRevision": 68}, {"classes": [{"className": "QDiffuseMapMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QDiffuseMapMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qdiffusemapmaterial.h", "outputRevision": 68}, {"classes": [{"className": "QDiffuseSpecularMapMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QDiffuseSpecularMapMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Qt3DRender::QAbstractTexture*"}], "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qdiffusespecularmapmaterial.h", "outputRevision": 68}, {"classes": [{"className": "QDiffuseSpecularMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "normal", "notify": "normalChanged", "read": "normal", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setNormal"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "alphaBlending", "notify": "alphaBlendingEnabledChanged", "read": "isAlphaBlendingEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAlphaBlendingEnabled"}], "qualifiedClassName": "Qt3DExtras::QDiffuseSpecularMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "normalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "textureScaleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "alphaBlendingEnabledChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setNormal", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "setTextureScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "setAlphaBlendingEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qdiffusespecularmaterial.h", "outputRevision": 68}, {"classes": [{"className": "QExtrudedTextGeometry", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "extrusionLength", "notify": "depthChanged", "read": "extrusionLength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QExtrudedTextGeometry", "signals": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "name": "textChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extrusionLength", "type": "float"}], "name": "depthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "name": "setText", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "setFont", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extrusionLength", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qextrudedtextgeometry.h", "outputRevision": 68}, {"classes": [{"className": "QExtrudedTextMesh", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "depth", "notify": "depthChanged", "read": "depth", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QExtrudedTextMesh", "signals": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "name": "textChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "depth", "type": "float"}], "name": "depthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "name": "setText", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "setFont", "returnType": "void"}, {"access": "public", "arguments": [{"name": "depth", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qextrudedtextmesh.h", "outputRevision": 68}, {"classes": [{"className": "QFirstPersonCameraController", "object": true, "qualifiedClassName": "Qt3DExtras::QFirstPersonCameraController", "superClasses": [{"access": "public", "name": "QAbstractCameraController"}]}], "inputFile": "qfirstpersoncameracontroller.h", "outputRevision": 68}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "surface", "notify": "surfaceChanged", "read": "surface", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setSurface"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "window", "notify": "surfaceChanged", "read": "surface", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setSurface"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "viewportRect", "notify": "viewportRectChanged", "read": "viewportRect", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false, "write": "setViewportRect"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "clearColor", "notify": "clearColorChanged", "read": "clearColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setClearColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "buffersToClear", "notify": "buffersToClearChanged", "read": "buffersToClear", "required": false, "revision": 65294, "scriptable": true, "stored": true, "type": "Qt3DRender::QClearBuffers::BufferType", "user": false, "write": "setBuffersToClear"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "camera", "notify": "cameraChanged", "read": "camera", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QEntity*", "user": false, "write": "setCamera"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "externalRenderTargetSize", "notify": "externalRenderTargetSizeChanged", "read": "externalRenderTargetSize", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setExternalRenderTargetSize"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "frustumCulling", "notify": "frustumCullingEnabledChanged", "read": "isFrustumCullingEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFrustumCullingEnabled"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "gamma", "notify": "gammaChanged", "read": "gamma", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "showDebugOverlay", "notify": "showDebugOverlayChanged", "read": "showDebugOverlay", "required": false, "revision": 65295, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShowDebugOverlay"}], "qualifiedClassName": "Qt3DExtras::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signals": [{"access": "public", "arguments": [{"name": "viewportRect", "type": "QRectF"}], "name": "viewportRectChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clearColor", "type": "QColor"}], "name": "clearColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "Qt3DRender::QClearBuffers::BufferType"}], "name": "buffersToClearChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "camera", "type": "Qt3DCore::QEntity*"}], "name": "cameraChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "surface", "type": "QObject*"}], "name": "surfaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "QSize"}], "name": "externalRenderTargetSizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "frustumCullingEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gamma", "type": "float"}], "name": "gammaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "showDebugOverlay", "type": "bool"}], "name": "showDebugOverlayChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "viewportRect", "type": "QRectF"}], "name": "setViewportRect", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clearColor", "type": "QColor"}], "name": "setClearColor", "returnType": "void"}, {"access": "public", "arguments": [{"type": "Qt3DRender::QClearBuffers::BufferType"}], "name": "setBuffersToClear", "returnType": "void"}, {"access": "public", "arguments": [{"name": "camera", "type": "Qt3DCore::QEntity*"}], "name": "setCamera", "returnType": "void"}, {"access": "public", "arguments": [{"name": "surface", "type": "QObject*"}], "name": "setSurface", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "QSize"}], "name": "setExternalRenderTargetSize", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "setFrustumCullingEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gamma", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "showDebugOverlay", "type": "bool"}], "name": "setShowDebugOverlay", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QTechniqueFilter"}]}], "inputFile": "qforwardrenderer.h", "outputRevision": 68}, {"classes": [{"className": "QGoochMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "cool", "notify": "coolChanged", "read": "cool", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setCool"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "warm", "notify": "warmChanged", "read": "warm", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setWarm"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "alpha", "notify": "alphaChanged", "read": "alpha", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "beta", "notify": "betaChanged", "read": "beta", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBeta"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}], "qualifiedClassName": "Qt3DExtras::QGoochMaterial", "signals": [{"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "cool", "type": "QColor"}], "name": "coolChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "warm", "type": "QColor"}], "name": "warmChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alpha", "type": "float"}], "name": "alphaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "beta", "type": "float"}], "name": "betaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "shininessChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "cool", "type": "QColor"}], "name": "setCool", "returnType": "void"}, {"access": "public", "arguments": [{"name": "warm", "type": "QColor"}], "name": "setWarm", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alpha", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "beta", "type": "float"}], "name": "setBeta", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "setShininess", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qgoochmaterial.h", "outputRevision": 68}, {"classes": [{"className": "QMetalRoughMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "baseColor", "notify": "baseColorChanged", "read": "baseColor", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setBaseColor"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "metalness", "notify": "metalnessChanged", "read": "metalness", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setMetalness"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "roughness", "notify": "roughnessChanged", "read": "roughness", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setRoughness"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "ambientOcclusion", "notify": "ambientOcclusionChanged", "read": "ambientOcclusion", "required": false, "revision": 65290, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setAmbientOcclusion"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "normal", "notify": "normalChanged", "read": "normal", "required": false, "revision": 65290, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setNormal"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "revision": 65290, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QMetalRoughMaterial", "signals": [{"access": "public", "arguments": [{"name": "baseColor", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "baseColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "metalness", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "metalnessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "roughness", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "roughnessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ambientOcclusion", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "ambientOcclusionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "normalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "baseColor", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setBaseColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "metalness", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setMetalness", "returnType": "void"}, {"access": "public", "arguments": [{"name": "roughness", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setRoughness", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ambientOcclusion", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setAmbientOcclusion", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "setNormal", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qmetalroughmaterial.h", "outputRevision": 68}, {"classes": [{"className": "QMorphPhongMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "interpolator", "notify": "interpolatorChanged", "read": "interpolator", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setInterpolator"}], "qualifiedClassName": "Qt3DExtras::QMorphPhongMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interpolator", "type": "float"}], "name": "interpolatorChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interpolator", "type": "float"}], "name": "setInterpolator", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qmorphphongmaterial.h", "outputRevision": 68}, {"classes": [{"className": "QNormalDiffuseMapAlphaMaterial", "object": true, "qualifiedClassName": "Qt3DExtras::QNormalDiffuseMapAlphaMaterial", "superClasses": [{"access": "public", "name": "QNormalDiffuseMapMaterial"}]}], "inputFile": "qnormaldiffusemapalphamaterial.h", "outputRevision": 68}, {"classes": [{"className": "QNormalDiffuseMapMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "normal", "notify": "normalChanged", "read": "normal", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setNormal"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QNormalDiffuseMapMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Qt3DRender::QAbstractTexture*"}], "name": "normalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setNormal", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qnormaldiffusemapmaterial.h", "outputRevision": 68}, {"classes": [{"className": "QNormalDiffuseSpecularMapMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "normal", "notify": "normalChanged", "read": "normal", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setNormal"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "textureScale", "notify": "textureScaleChanged", "read": "textureScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTextureScale"}], "qualifiedClassName": "Qt3DExtras::QNormalDiffuseSpecularMapMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Qt3DRender::QAbstractTexture*"}], "name": "normalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Qt3DRender::QAbstractTexture*"}], "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "textureScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normal", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setNormal", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureScale", "type": "float"}], "name": "setTextureScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qnormaldiffusespecularmapmaterial.h", "outputRevision": 68}, {"classes": [{"className": "QOrbitCameraController", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "zoomInLimit", "notify": "zoomInLimitChanged", "read": "zoomInLimit", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZoomInLimit"}], "qualifiedClassName": "Qt3DExtras::QOrbitCameraController", "signals": [{"access": "public", "name": "zoomInLimitChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractCameraController"}]}], "inputFile": "qorbitcameracontroller.h", "outputRevision": 68}, {"classes": [{"className": "QPerVertexColorMaterial", "object": true, "qualifiedClassName": "Qt3DExtras::QPerVertexColorMaterial", "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qpervertexcolormaterial.h", "outputRevision": 68}, {"classes": [{"className": "QPhongAlphaMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "alpha", "notify": "alphaChanged", "read": "alpha", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "sourceRgbArg", "notify": "sourceRgbArgChanged", "read": "sourceRgbArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquationArguments::Blending", "user": false, "write": "setSourceRgbArg"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "destinationRgbArg", "notify": "destinationRgbArgChanged", "read": "destinationRgbArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquationArguments::Blending", "user": false, "write": "setDestinationRgbArg"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "sourceAlphaArg", "notify": "sourceAlphaArgChanged", "read": "sourceAlphaArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquationArguments::Blending", "user": false, "write": "setSourceAlphaArg"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "destinationAlphaArg", "notify": "destinationAlphaArgChanged", "read": "destinationAlphaArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquationArguments::Blending", "user": false, "write": "setDestinationAlphaArg"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "blendFunctionArg", "notify": "blendFunctionArgChanged", "read": "blendFunctionArg", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QBlendEquation::BlendFunction", "user": false, "write": "setBlendFunctionArg"}], "qualifiedClassName": "Qt3DExtras::QPhongAlphaMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "shininessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alpha", "type": "float"}], "name": "alphaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceRgbArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "name": "sourceRgbArgChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "destinationRgbArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "name": "destinationRgbArgChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceAlphaArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "name": "sourceAlphaArgChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "destinationAlphaArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "name": "destinationAlphaArgChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "blendFunctionArg", "type": "Qt3DRender::QBlendEquation::BlendFunction"}], "name": "blendFunctionArgChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "setShininess", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alpha", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceRgbArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "name": "setSourceRgbArg", "returnType": "void"}, {"access": "public", "arguments": [{"name": "destinationRgbArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "name": "setDestinationRgbArg", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceAlphaArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "name": "setSourceAlphaArg", "returnType": "void"}, {"access": "public", "arguments": [{"name": "destinationAlphaArg", "type": "Qt3DRender::QBlendEquationArguments::Blending"}], "name": "setDestinationAlphaArg", "returnType": "void"}, {"access": "public", "arguments": [{"name": "blendFunctionArg", "type": "Qt3DRender::QBlendEquation::BlendFunction"}], "name": "setBlendFunctionArg", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qphongalphamaterial.h", "outputRevision": 68}, {"classes": [{"className": "QPhongMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ambient", "notify": "ambientChanged", "read": "ambient", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setAmbient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "diffuse", "notify": "diffuseChanged", "read": "diffuse", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDiffuse"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "specular", "notify": "specularChanged", "read": "specular", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSpecular"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shininess", "notify": "shininessChanged", "read": "shininess", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShininess"}], "qualifiedClassName": "Qt3DExtras::QPhongMaterial", "signals": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "ambientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "name": "diffuseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "specularChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "shininessChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "ambient", "type": "QColor"}], "name": "setAmbient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "diffuse", "type": "QColor"}], "name": "setDiffuse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "specular", "type": "QColor"}], "name": "setSpecular", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shininess", "type": "float"}], "name": "setShininess", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qphongmaterial.h", "outputRevision": 68}, {"classes": [{"className": "QPlaneGeometry", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "resolution", "notify": "resolutionChanged", "read": "resolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setResolution"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "mirrored", "notify": "mirroredChanged", "read": "mirrored", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "tangentAttribute", "read": "tangentAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QPlaneGeometry", "signals": [{"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "resolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "name": "heightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "name": "mirroredChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "name": "setHeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "name": "set<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qplanegeometry.h", "outputRevision": 68}, {"classes": [{"className": "QPlaneGeometryView", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "meshResolution", "notify": "meshResolutionChanged", "read": "meshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "mirrored", "notify": "mirroredChanged", "read": "mirrored", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::QPlaneGeometryView", "signals": [{"access": "public", "arguments": [{"name": "meshResolution", "type": "QSize"}], "name": "meshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "name": "heightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "name": "mirroredChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "width", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "name": "setHeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "name": "set<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qplanegeometryview.h", "outputRevision": 68}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "meshResolution", "notify": "meshResolutionChanged", "read": "meshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "mirrored", "notify": "mirroredChanged", "read": "mirrored", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "Qt3DExtras::Q<PERSON><PERSON>Mesh", "signals": [{"access": "public", "arguments": [{"name": "meshResolution", "type": "QSize"}], "name": "meshResolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "name": "heightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "name": "mirroredChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "width", "type": "float"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "name": "setHeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resolution", "type": "QSize"}], "name": "setMeshResolution", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mirrored", "type": "bool"}], "name": "set<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qplanemesh.h", "outputRevision": 68}, {"classes": [{"className": "QSkyboxEntity", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "baseName", "notify": "baseNameChanged", "read": "baseName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBaseName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "extension", "notify": "extensionChanged", "read": "extension", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setExtension"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "gammaCorrect", "notify": "gammaCorrectEnabledChanged", "read": "isGammaCorrectEnabled", "required": false, "revision": 65289, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGammaCorrectEnabled"}], "qualifiedClassName": "Qt3DExtras::QSkyboxEntity", "signals": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}], "name": "baseNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extension", "type": "QString"}], "name": "extensionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "gammaCorrectEnabledChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}], "name": "setBaseName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extension", "type": "QString"}], "name": "setExtension", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "setGammaCorrectEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QEntity"}]}], "inputFile": "qskyboxentity.h", "outputRevision": 68}, {"classes": [{"className": "QSphereGeometry", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "generateTangents", "notify": "generateTangentsChanged", "read": "generateTangents", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGenerateTangents"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "tangentAttribute", "read": "tangentAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QSphereGeometry", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "generateTangents", "type": "bool"}], "name": "generateTangentsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gen", "type": "bool"}], "name": "setGenerateTangents", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qspheregeometry.h", "outputRevision": 68}, {"classes": [{"className": "QSphereGeometryView", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "generateTangents", "notify": "generateTangentsChanged", "read": "generateTangents", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGenerateTangents"}], "qualifiedClassName": "Qt3DExtras::QSphereGeometryView", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "generateTangents", "type": "bool"}], "name": "generateTangentsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gen", "type": "bool"}], "name": "setGenerateTangents", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qspheregeometryview.h", "outputRevision": 68}, {"classes": [{"className": "QSphereMesh", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "generateTangents", "notify": "generateTangentsChanged", "read": "generateTangents", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGenerateTangents"}], "qualifiedClassName": "Qt3DExtras::QSphereMesh", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "generateTangents", "type": "bool"}], "name": "generateTangentsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gen", "type": "bool"}], "name": "setGenerateTangents", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qspheremesh.h", "outputRevision": 68}, {"classes": [{"className": "QSpriteGrid", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rows", "notify": "rowsChanged", "read": "rows", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRows"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "columns", "notify": "columnsChanged", "read": "columns", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setColumns"}], "qualifiedClassName": "Qt3DExtras::QSpriteGrid", "signals": [{"access": "public", "arguments": [{"name": "rows", "type": "int"}], "name": "rowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "columns", "type": "int"}], "name": "columnsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rows", "type": "int"}], "name": "setRows", "returnType": "void"}, {"access": "public", "arguments": [{"name": "columns", "type": "int"}], "name": "setColumns", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSpriteSheet"}]}], "inputFile": "qspritegrid.h", "outputRevision": 68}, {"classes": [{"className": "QSpriteSheet", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sprites", "notify": "spritesChanged", "read": "sprites", "required": false, "scriptable": true, "stored": true, "type": "QList<QSpriteSheetItem*>", "user": false, "write": "setSprites"}], "qualifiedClassName": "Qt3DExtras::QSpriteSheet", "signals": [{"access": "public", "arguments": [{"name": "sprites", "type": "QList<QSpriteSheetItem*>"}], "name": "spritesChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "sprites", "type": "QList<QSpriteSheetItem*>"}], "name": "setSprites", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSpriteSheet"}]}], "inputFile": "qspritesheet.h", "outputRevision": 68}, {"classes": [{"className": "QSpriteSheetItem", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHeight"}], "qualifiedClassName": "Qt3DExtras::QSpriteSheetItem", "signals": [{"access": "public", "arguments": [{"name": "x", "type": "int"}], "name": "xChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "y", "type": "int"}], "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "int"}], "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "int"}], "name": "heightChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "x", "type": "int"}], "name": "setX", "returnType": "void"}, {"access": "public", "arguments": [{"name": "y", "type": "int"}], "name": "setY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "int"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "int"}], "name": "setHeight", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qspritesheetitem.h", "outputRevision": 68}, {"classes": [{"className": "Qt3DWindow", "object": true, "qualifiedClassName": "Qt3DExtras::Qt3DWindow", "superClasses": [{"access": "public", "name": "QWindow"}]}], "inputFile": "qt3dwindow.h", "outputRevision": 68}, {"classes": [{"className": "QText2DEntity", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHeight"}], "qualifiedClassName": "Qt3DExtras::QText2DEntity", "signals": [{"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "text", "type": "QString"}], "name": "textChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "float"}], "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "height", "type": "float"}], "name": "heightChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QEntity"}]}], "inputFile": "qtext2dentity.h", "outputRevision": 68}, {"classes": [{"className": "QText2DMaterial", "object": true, "qualifiedClassName": "Qt3DExtras::QText2DMaterial", "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qtext2dmaterial_p.h", "outputRevision": 68}, {"classes": [{"className": "QTextureAtlas", "object": true, "qualifiedClassName": "Qt3DExtras::QTextureAtlas", "superClasses": [{"access": "public", "name": "Qt3DRender::QAbstractTexture"}]}], "inputFile": "qtextureatlas_p.h", "outputRevision": 68}, {"classes": [{"className": "QTextureMaterial", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "texture", "notify": "textureChanged", "read": "texture", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QAbstractTexture*", "user": false, "write": "setTexture"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "textureOffset", "notify": "textureOffsetChanged", "read": "textureOffset", "required": false, "scriptable": true, "stored": true, "type": "QVector2D", "user": false, "write": "setTextureOffset"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "textureTransform", "notify": "textureTransformChanged", "read": "textureTransform", "required": false, "revision": 65290, "scriptable": true, "stored": true, "type": "QMatrix3x3", "user": false, "write": "setTextureTransform"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "alphaBlending", "notify": "alphaBlendingEnabledChanged", "read": "isAlphaBlendingEnabled", "required": false, "revision": 65291, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAlphaBlendingEnabled"}], "qualifiedClassName": "Qt3DExtras::QTextureMaterial", "signals": [{"access": "public", "arguments": [{"name": "texture", "type": "Qt3DRender::QAbstractTexture*"}], "name": "textureChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureOffset", "type": "QVector2D"}], "name": "textureOffsetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureTransform", "type": "QMatrix3x3"}], "name": "textureTransformChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "alphaBlendingEnabledChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "texture", "type": "Qt3DRender::QAbstractTexture*"}], "name": "setTexture", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureOffset", "type": "QVector2D"}], "name": "setTextureOffset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "matrix", "type": "QMatrix3x3"}], "name": "setTextureTransform", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "setAlphaBlendingEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QMaterial"}]}], "inputFile": "qtexturematerial.h", "outputRevision": 68}, {"classes": [{"className": "QTorusGeometry", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "minorRadius", "notify": "minorRadius<PERSON><PERSON><PERSON>", "read": "minorRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinorRadius"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "positionAttribute", "read": "positionAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "normalAttribute", "read": "normalAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "texCoordAttribute", "read": "texCoordAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "indexAttribute", "read": "indexAttribute", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAttribute*", "user": false}], "qualifiedClassName": "Qt3DExtras::QTorusGeometry", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "name": "minorRadius<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "name": "setMinorRadius", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometry"}]}], "inputFile": "qtorusgeometry.h", "outputRevision": 68}, {"classes": [{"className": "QTorusGeometryView", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "minorRadius", "notify": "minorRadius<PERSON><PERSON><PERSON>", "read": "minorRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinorRadius"}], "qualifiedClassName": "Qt3DExtras::QTorusGeometryView", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "name": "minorRadius<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "name": "setMinorRadius", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QGeometryView"}]}], "inputFile": "qtorusgeometryview.h", "outputRevision": 68}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "slices", "notify": "slicesChanged", "read": "slices", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSlices"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "minorRadius", "notify": "minorRadius<PERSON><PERSON><PERSON>", "read": "minorRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinorRadius"}], "qualifiedClassName": "Qt3DExtras::QTorusMesh", "signals": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "ringsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "slicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "name": "minorRadius<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "rings", "type": "int"}], "name": "setRings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "int"}], "name": "setSlices", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minorRadius", "type": "float"}], "name": "setMinorRadius", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "qtorusmesh.h", "outputRevision": 68}]