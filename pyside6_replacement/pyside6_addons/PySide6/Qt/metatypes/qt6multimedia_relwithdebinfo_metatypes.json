[{"classes": [{"className": "QPlatformVideoSource", "object": true, "qualifiedClassName": "QPlatformVideoSource", "signals": [{"access": "public", "arguments": [{"type": "QVideoFrame"}], "name": "newVideoFrame", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "name": "activeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformvideosource_p.h", "outputRevision": 68}, {"classes": [{"className": "QAudioDecoder", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "ResourceError", "FormatError", "AccessDeniedError", "NotSupportedError"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "isDecoding", "notify": "isDecodingChanged", "read": "isDecoding", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "error", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "bufferAvailable", "notify": "bufferAvailableChanged", "read": "bufferAvailable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QAudioDecoder", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "name": "bufferAvailableChanged", "returnType": "void"}, {"access": "public", "name": "bufferReady", "returnType": "void"}, {"access": "public", "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "name": "isDecodingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QAudioFormat"}], "name": "formatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QAudioDecoder::<PERSON><PERSON><PERSON>"}], "name": "error", "returnType": "void"}, {"access": "public", "name": "sourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "qint64"}], "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "qint64"}], "name": "durationChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiodecoder.h", "outputRevision": 68}, {"classes": [{"className": "QAudioDevice", "enums": [{"isClass": false, "isFlag": false, "name": "Mode", "values": ["<PERSON><PERSON>", "Input", "Output"]}], "gadget": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "id", "read": "id", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "description", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "isDefault", "read": "isDefault", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "mode", "read": "mode", "required": false, "scriptable": true, "stored": true, "type": "Mode", "user": false}], "qualifiedClassName": "QAudioDevice"}], "inputFile": "qaudiodevice.h", "outputRevision": 68}, {"classes": [{"className": "QAudioInput", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "device", "notify": "deviceChanged", "read": "device", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false, "write": "setDevice"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "muted", "notify": "mutedChanged", "read": "isMuted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMuted"}], "qualifiedClassName": "QAudioInput", "signals": [{"access": "public", "name": "deviceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "volume", "type": "float"}], "name": "volumeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "muted", "type": "bool"}], "name": "mutedChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "device", "type": "QAudioDevice"}], "name": "setDevice", "returnType": "void"}, {"access": "public", "arguments": [{"name": "volume", "type": "float"}], "name": "setVolume", "returnType": "void"}, {"access": "public", "arguments": [{"name": "muted", "type": "bool"}], "name": "setMuted", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudioinput.h", "outputRevision": 68}, {"classes": [{"className": "QAudioOutput", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "device", "notify": "deviceChanged", "read": "device", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false, "write": "setDevice"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "muted", "notify": "mutedChanged", "read": "isMuted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMuted"}], "qualifiedClassName": "QAudioOutput", "signals": [{"access": "public", "name": "deviceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "volume", "type": "float"}], "name": "volumeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "muted", "type": "bool"}], "name": "mutedChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "device", "type": "QAudioDevice"}], "name": "setDevice", "returnType": "void"}, {"access": "public", "arguments": [{"name": "volume", "type": "float"}], "name": "setVolume", "returnType": "void"}, {"access": "public", "arguments": [{"name": "muted", "type": "bool"}], "name": "setMuted", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiooutput.h", "outputRevision": 68}, {"classes": [{"className": "QAudioSink", "object": true, "qualifiedClassName": "QAudioSink", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "QAudio::State"}], "name": "stateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiosink.h", "outputRevision": 68}, {"classes": [{"className": "QAudioSource", "object": true, "qualifiedClassName": "QAudioSource", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "QAudio::State"}], "name": "stateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiosource.h", "outputRevision": 68}, {"classes": [{"className": "QAudioStateChangeNotifier", "object": true, "qualifiedClassName": "QAudioStateChangeNotifier", "signals": [{"access": "public", "arguments": [{"name": "error", "type": "QAudio::<PERSON><PERSON><PERSON>"}], "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QAudio::State"}], "name": "stateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QPlatformAudioSink", "object": true, "qualifiedClassName": "QPlatformAudioSink", "superClasses": [{"access": "public", "name": "QAudioStateChangeNotifier"}]}, {"className": "QPlatformAudioSource", "object": true, "qualifiedClassName": "QPlatformAudioSource", "superClasses": [{"access": "public", "name": "QAudioStateChangeNotifier"}]}], "inputFile": "qaudiosystem_p.h", "outputRevision": 68}, {"classes": [{"className": "QCamera", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "CameraError"]}, {"isClass": false, "isFlag": false, "name": "FocusMode", "values": ["FocusModeAuto", "FocusModeAutoNear", "FocusModeAutoFar", "FocusModeHyperfocal", "FocusModeInfinity", "FocusModeManual"]}, {"isClass": false, "isFlag": false, "name": "FlashMode", "values": ["<PERSON><PERSON>ff", "FlashOn", "FlashAuto"]}, {"isClass": false, "isFlag": false, "name": "TorchMode", "values": ["<PERSON><PERSON><PERSON><PERSON>", "TorchOn", "TorchAuto"]}, {"isClass": false, "isFlag": false, "name": "ExposureMode", "values": ["ExposureAuto", "ExposureManual", "ExposurePortrait", "ExposureNight", "ExposureSports", "ExposureSnow", "ExposureBeach", "ExposureAction", "ExposureLandscape", "ExposureNightPortrait", "ExposureTheatre", "ExposureSunset", "ExposureSteadyPhoto", "ExposureFireworks", "ExposureParty", "ExposureCandlelight", "ExposureBarcode"]}, {"isClass": false, "isFlag": false, "name": "WhiteBalanceMode", "values": ["WhiteBalanceAuto", "WhiteBalanceManual", "WhiteBalanceSunlight", "WhiteBalanceCloudy", "WhiteBalanceShade", "WhiteBalanceTungsten", "WhiteBalanceFluorescent", "WhiteBalanceFlash", "WhiteBalanceSunset"]}], "methods": [{"access": "public", "arguments": [{"name": "mode", "type": "FocusMode"}], "name": "isFocusModeSupported", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "mode", "type": "FlashMode"}], "name": "isFlashModeSupported", "returnType": "bool"}, {"access": "public", "name": "isFlashReady", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "mode", "type": "TorchMode"}], "name": "isTorchModeSupported", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "mode", "type": "ExposureMode"}], "name": "isExposureModeSupported", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "mode", "type": "WhiteBalanceMode"}], "name": "isWhiteBalanceModeSupported", "returnType": "bool"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "cameraDevice", "notify": "cameraDeviceChanged", "read": "cameraDevice", "required": false, "scriptable": true, "stored": true, "type": "QCameraDevice", "user": false, "write": "setCameraDevice"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "cameraFormat", "notify": "cameraFormatChanged", "read": "cameraFormat", "required": false, "scriptable": true, "stored": true, "type": "QCameraFormat", "user": false, "write": "setCameraFormat"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "focusMode", "read": "focusMode", "required": false, "scriptable": true, "stored": true, "type": "FocusMode", "user": false, "write": "setFocusMode"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "focusPoint", "notify": "focusPointChanged", "read": "focusPoint", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "customFocusPoint", "notify": "customFocusPointChanged", "read": "customFocusPoint", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setCustomFocusPoint"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "focusDistance", "notify": "focusDistanceChanged", "read": "focusDistance", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setFocusDistance"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "minimumZoomFactor", "notify": "minimumZoomFactorChanged", "read": "minimumZoomFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "maximumZoomFactor", "notify": "maximumZoomFactorChanged", "read": "maximumZoomFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "zoomFactor", "notify": "zoomFactorChanged", "read": "zoomFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZoomFactor"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "exposureTime", "notify": "exposureTimeChanged", "read": "exposureTime", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "manualExposureTime", "notify": "manualExposureTimeChanged", "read": "manualExposureTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setManualExposureTime"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "isoSensitivity", "notify": "isoSensitivityChanged", "read": "isoSensitivity", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "manualIsoSensitivity", "notify": "manualIsoSensitivityChanged", "read": "manualIsoSensitivity", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setManualIsoSensitivity"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "exposureCompensation", "notify": "exposureCompensationChanged", "read": "exposureCompensation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setExposureCompensation"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "exposureMode", "notify": "exposureModeChanged", "read": "exposureMode", "required": false, "scriptable": true, "stored": true, "type": "QCamera::ExposureMode", "user": false, "write": "setExposureMode"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "flashReady", "notify": "flashReady", "read": "isFlashReady", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "flashMode", "notify": "flashModeChanged", "read": "flashMode", "required": false, "scriptable": true, "stored": true, "type": "QCamera::FlashMode", "user": false, "write": "setFlashMode"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "torchMode", "notify": "torchModeChanged", "read": "torchMode", "required": false, "scriptable": true, "stored": true, "type": "QCamera::TorchMode", "user": false, "write": "setTorchMode"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "whiteBalanceMode", "notify": "whiteBalanceModeChanged", "read": "whiteBalanceMode", "required": false, "scriptable": true, "stored": true, "type": "WhiteBalanceMode", "user": false, "write": "setWhiteBalanceMode"}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "colorTemperature", "notify": "colorTemperatureChanged", "read": "colorTemperature", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setColorTemperature"}, {"constant": false, "designable": true, "final": false, "index": 23, "name": "supportedFeatures", "notify": "supportedFeaturesChanged", "read": "supportedFeatures", "required": false, "scriptable": true, "stored": true, "type": "Features", "user": false}], "qualifiedClassName": "QCamera", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "name": "activeChanged", "returnType": "void"}, {"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QCamera::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "name": "cameraDeviceChanged", "returnType": "void"}, {"access": "public", "name": "cameraFormatChanged", "returnType": "void"}, {"access": "public", "name": "supportedFeaturesChanged", "returnType": "void"}, {"access": "public", "name": "focusModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "name": "zoomFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "name": "minimumZoomFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "name": "maximumZoomFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "name": "focusDistanceChanged", "returnType": "void"}, {"access": "public", "name": "focusPointChanged", "returnType": "void"}, {"access": "public", "name": "customFocusPointChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "name": "flashReady", "returnType": "void"}, {"access": "public", "name": "flashModeChanged", "returnType": "void"}, {"access": "public", "name": "torchModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "speed", "type": "float"}], "name": "exposureTimeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "speed", "type": "float"}], "name": "manualExposureTimeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "name": "isoSensitivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "name": "manualIsoSensitivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "name": "exposureCompensationChanged", "returnType": "void"}, {"access": "public", "name": "exposureModeChanged", "returnType": "void"}, {"access": "public", "name": "whiteBalanceModeChanged", "returnType": "void"}, {"access": "public", "name": "colorTemperatureChanged", "returnType": "void"}, {"access": "public", "name": "brightnessChanged", "returnType": "void"}, {"access": "public", "name": "contrastChanged", "returnType": "void"}, {"access": "public", "name": "saturationChanged", "returnType": "void"}, {"access": "public", "name": "h<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "active", "type": "bool"}], "name": "setActive", "returnType": "void"}, {"access": "public", "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zoom", "type": "float"}, {"name": "rate", "type": "float"}], "name": "zoomTo", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "FlashMode"}], "name": "setFlashMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "TorchMode"}], "name": "setTorchMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "ExposureMode"}], "name": "setExposureMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ev", "type": "float"}], "name": "setExposureCompensation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "iso", "type": "int"}], "name": "setManualIsoSensitivity", "returnType": "void"}, {"access": "public", "name": "setAutoIsoSensitivity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "seconds", "type": "float"}], "name": "setManualExposureTime", "returnType": "void"}, {"access": "public", "name": "setAutoExposureTime", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "WhiteBalanceMode"}], "name": "setWhiteBalanceMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "colorTemperature", "type": "int"}], "name": "setColorTemperature", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}, {"type": "QString"}], "name": "_q_error", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcamera.h", "outputRevision": 68}, {"classes": [{"className": "QCameraFormat", "gadget": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "resolution", "read": "resolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "pixelFormat", "read": "pixelFormat", "required": false, "scriptable": true, "stored": true, "type": "QVideoFrameFormat::PixelFormat", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "minFrameRate", "read": "minFrameRate", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "maxFrameRate", "read": "maxFrameRate", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "QCameraFormat"}, {"className": "QCameraDevice", "enums": [{"isClass": false, "isFlag": false, "name": "Position", "values": ["UnspecifiedPosition", "BackFace", "FrontFace"]}], "gadget": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "id", "read": "id", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "description", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "isDefault", "read": "isDefault", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "position", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "Position", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "videoFormats", "read": "videoFormats", "required": false, "scriptable": true, "stored": true, "type": "QList<QCameraFormat>", "user": false}], "qualifiedClassName": "QCameraDevice"}], "inputFile": "qcameradevice.h", "outputRevision": 68}, {"classes": [{"className": "QDarwinAudioSinkBuffer", "object": true, "qualifiedClassName": "QDarwinAudioSinkBuffer", "signals": [{"access": "public", "name": "readyRead", "returnType": "void"}], "slots": [{"access": "private", "name": "fillBuffer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QDarwinAudioSink", "object": true, "qualifiedClassName": "QDarwinAudioSink", "slots": [{"access": "private", "name": "inputReady", "returnType": "void"}, {"access": "private", "name": "updateAudioDevice", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformAudioSink"}]}], "inputFile": "qdarwinaudiosink_p.h", "outputRevision": 68}, {"classes": [{"className": "QDarwinAudioSourceBuffer", "object": true, "qualifiedClassName": "QDarwinAudioSourceBuffer", "signals": [{"access": "public", "name": "readyRead", "returnType": "void"}], "slots": [{"access": "private", "name": "flushBuffer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QDarwinAudioSourceDevice", "object": true, "qualifiedClassName": "QDarwinAudioSourceDevice", "superClasses": [{"access": "public", "name": "QIODevice"}]}, {"className": "QDarwinAudioSource", "object": true, "qualifiedClassName": "QDarwinAudioSource", "slots": [{"access": "private", "name": "deviceStoppped", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformAudioSource"}]}], "inputFile": "qdarwinaudiosource_p.h", "outputRevision": 68}, {"classes": [{"className": "QImageCapture", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "NotReadyError", "ResourceError", "OutOfSpaceError", "NotSupportedFeatureError", "FormatError"]}, {"isClass": false, "isFlag": false, "name": "Quality", "values": ["VeryLowQuality", "LowQuality", "NormalQuality", "HighQuality", "VeryHighQuality"]}, {"isClass": false, "isFlag": false, "name": "FileFormat", "values": ["UnspecifiedFormat", "JPEG", "PNG", "WebP", "Tiff", "LastFileFormat"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "readyForCapture", "notify": "readyForCaptureChanged", "read": "isReadyForCapture", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "metaData", "notify": "metaDataChanged", "read": "metaData", "required": false, "scriptable": true, "stored": true, "type": "QMediaMetaData", "user": false, "write": "setMetaData"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "fileFormat", "notify": "fileFormatChanged", "read": "fileFormat", "required": false, "scriptable": true, "stored": true, "type": "FileFormat", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "quality", "notify": "qualityChanged", "read": "quality", "required": false, "scriptable": true, "stored": true, "type": "Quality", "user": false}], "qualifiedClassName": "QImageCapture", "signals": [{"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "error", "type": "QImageCapture::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ready", "type": "bool"}], "name": "readyForCaptureChanged", "returnType": "void"}, {"access": "public", "name": "metaDataChanged", "returnType": "void"}, {"access": "public", "name": "fileFormatChanged", "returnType": "void"}, {"access": "public", "name": "qualityChanged", "returnType": "void"}, {"access": "public", "name": "resolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}], "name": "imageExposed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "preview", "type": "QImage"}], "name": "imageCaptured", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "metaData", "type": "QMediaMetaData"}], "name": "imageMetadataAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "frame", "type": "QVideoFrame"}], "name": "imageAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "fileName", "type": "QString"}], "name": "imageSaved", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "location", "type": "QString"}], "name": "captureToFile", "returnType": "int"}, {"access": "public", "isCloned": true, "name": "captureToFile", "returnType": "int"}, {"access": "public", "name": "capture", "returnType": "int"}, {"access": "private", "arguments": [{"type": "int"}, {"type": "int"}, {"type": "QString"}], "name": "_q_error", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qimagecapture.h", "outputRevision": 68}, {"classes": [{"className": "QMediaCaptureSession", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "audioInput", "notify": "audioInputChanged", "read": "audioInput", "required": false, "scriptable": true, "stored": true, "type": "QAudioInput*", "user": false, "write": "setAudioInput"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "audioOutput", "notify": "audioOutputChanged", "read": "audioOutput", "required": false, "scriptable": true, "stored": true, "type": "QAudioOutput*", "user": false, "write": "setAudioOutput"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "camera", "notify": "cameraChanged", "read": "camera", "required": false, "scriptable": true, "stored": true, "type": "QCamera*", "user": false, "write": "setCamera"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "screenCapture", "notify": "screenCaptureChanged", "read": "screenCapture", "required": false, "scriptable": true, "stored": true, "type": "QScreenCapture*", "user": false, "write": "setScreenCapture"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "imageCapture", "notify": "imageCaptureChanged", "read": "imageCapture", "required": false, "scriptable": true, "stored": true, "type": "QImageCapture*", "user": false, "write": "setImageCapture"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "recorder", "notify": "<PERSON><PERSON><PERSON><PERSON>", "read": "recorder", "required": false, "scriptable": true, "stored": true, "type": "QMediaRecorder*", "user": false, "write": "setRecorder"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "videoOutput", "notify": "videoOutputChanged", "read": "videoOutput", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setVideoOutput"}], "qualifiedClassName": "QMediaCaptureSession", "signals": [{"access": "public", "name": "audioInputChanged", "returnType": "void"}, {"access": "public", "name": "cameraChanged", "returnType": "void"}, {"access": "public", "name": "screenCaptureChanged", "returnType": "void"}, {"access": "public", "name": "imageCaptureChanged", "returnType": "void"}, {"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "videoOutputChanged", "returnType": "void"}, {"access": "public", "name": "audioOutputChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmediacapturesession.h", "outputRevision": 68}, {"classes": [{"className": "QMediaDevices", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "audioInputs", "notify": "audioInputsChanged", "read": "audioInputs", "required": false, "scriptable": true, "stored": true, "type": "QList<QAudioDevice>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "audioOutputs", "notify": "audioOutputsChanged", "read": "audioOutputs", "required": false, "scriptable": true, "stored": true, "type": "QList<QAudioDevice>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "videoInputs", "notify": "videoInputsChanged", "read": "videoInputs", "required": false, "scriptable": true, "stored": true, "type": "QList<QCameraDevice>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "defaultAudioInput", "notify": "audioInputsChanged", "read": "defaultAudioInput", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "defaultAudioOutput", "notify": "audioOutputsChanged", "read": "defaultAudioOutput", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "defaultVideoInput", "notify": "videoInputsChanged", "read": "defaultVideoInput", "required": false, "scriptable": true, "stored": true, "type": "QCameraDevice", "user": false}], "qualifiedClassName": "QMediaDevices", "signals": [{"access": "public", "name": "audioInputsChanged", "returnType": "void"}, {"access": "public", "name": "audioOutputsChanged", "returnType": "void"}, {"access": "public", "name": "videoInputsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmediadevices.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "RegisterEnumClassesUnscoped", "value": "false"}], "className": "QMediaFormat", "enums": [{"isClass": false, "isFlag": false, "name": "FileFormat", "values": ["UnspecifiedFormat", "WMV", "AVI", "<PERSON><PERSON><PERSON>", "MPEG4", "<PERSON><PERSON>", "QuickTime", "WebM", "Mpeg4Audio", "AAC", "WMA", "MP3", "FLAC", "Wave", "LastFileFormat"]}, {"isClass": true, "isFlag": false, "name": "AudioCodec", "values": ["Unspecified", "MP3", "AAC", "AC3", "EAC3", "FLAC", "DolbyTrueHD", "Opus", "Vorbis", "Wave", "WMA", "ALAC", "LastAudioCodec"]}, {"isClass": true, "isFlag": false, "name": "VideoCodec", "values": ["Unspecified", "MPEG1", "MPEG2", "MPEG4", "H264", "H265", "VP8", "VP9", "AV1", "Theora", "WMV", "MotionJPEG", "LastVideoCodec"]}, {"isClass": false, "isFlag": false, "name": "ConversionMode", "values": ["Encode", "Decode"]}], "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "mode", "type": "ConversionMode"}], "name": "isSupported", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "m", "type": "ConversionMode"}], "name": "supportedFileFormats", "returnType": "QList<FileFormat>"}, {"access": "public", "arguments": [{"name": "m", "type": "ConversionMode"}], "name": "supportedVideoCodecs", "returnType": "QList<VideoCodec>"}, {"access": "public", "arguments": [{"name": "m", "type": "ConversionMode"}], "name": "supportedAudioCodecs", "returnType": "QList<AudioCodec>"}, {"access": "public", "arguments": [{"name": "fileFormat", "type": "FileFormat"}], "name": "fileFormatName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "codec", "type": "AudioCodec"}], "name": "audioCodecName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "codec", "type": "VideoCodec"}], "name": "videoCodecName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "fileFormat", "type": "QMediaFormat::FileFormat"}], "name": "fileFormatDescription", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "codec", "type": "QMediaFormat::AudioCodec"}], "name": "audioCodecDescription", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "codec", "type": "QMediaFormat::VideoCodec"}], "name": "videoCodecDescription", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fileFormat", "read": "fileFormat", "required": false, "scriptable": true, "stored": true, "type": "FileFormat", "user": false, "write": "setFileFormat"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "audioCodec", "read": "audioCodec", "required": false, "scriptable": true, "stored": true, "type": "AudioCodec", "user": false, "write": "setAudioCodec"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "videoCodec", "read": "videoCodec", "required": false, "scriptable": true, "stored": true, "type": "VideoCodec", "user": false, "write": "setVideoCodec"}], "qualifiedClassName": "QMediaFormat"}], "inputFile": "qmediaformat.h", "outputRevision": 68}, {"classes": [{"className": "QMediaMetaData", "enums": [{"isClass": false, "isFlag": false, "name": "Key", "values": ["Title", "Author", "Comment", "Description", "Genre", "Date", "Language", "Publisher", "Copyright", "Url", "Duration", "MediaType", "FileFormat", "AudioBitRate", "AudioCodec", "VideoBitRate", "VideoCodec", "VideoFrameRate", "AlbumTitle", "AlbumArtist", "ContributingArtist", "TrackNumber", "Composer", "LeadPerformer", "ThumbnailImage", "CoverArtImage", "Orientation", "Resolution"]}], "gadget": true, "methods": [{"access": "public", "arguments": [{"name": "k", "type": "Key"}], "name": "value", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "k", "type": "Key"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "name": "insert", "returnType": "void"}, {"access": "public", "arguments": [{"name": "k", "type": "Key"}], "name": "remove", "returnType": "void"}, {"access": "public", "name": "keys", "returnType": "QList<Key>"}, {"access": "public", "name": "clear", "returnType": "void"}, {"access": "public", "name": "isEmpty", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "k", "type": "Key"}], "name": "stringValue", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "k", "type": "Key"}], "name": "metaDataKeyToString", "returnType": "QString"}], "qualifiedClassName": "QMediaMetaData"}], "inputFile": "qmediametadata.h", "outputRevision": 68}, {"classes": [{"className": "QMediaPlayer", "enums": [{"isClass": false, "isFlag": false, "name": "PlaybackState", "values": ["StoppedState", "PlayingState", "PausedState"]}, {"isClass": false, "isFlag": false, "name": "MediaStatus", "values": ["NoMedia", "LoadingMedia", "LoadedMedia", "StalledMedia", "BufferingMedia", "BufferedMedia", "EndOfMedia", "InvalidMedia"]}, {"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "ResourceError", "FormatError", "NetworkError", "AccessDeniedError"]}, {"isClass": false, "isFlag": false, "name": "Loops", "values": ["Infinite", "Once"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "qint64", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "qint64", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "bufferProgress", "notify": "bufferProgressChanged", "read": "bufferProgress", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "hasAudio", "notify": "hasAudioChanged", "read": "hasAudio", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "hasVideo", "notify": "hasVideoChanged", "read": "hasVideo", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "seekable", "notify": "seekableChanged", "read": "isSeekable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "playing", "notify": "playingChanged", "read": "isPlaying", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "playbackRate", "notify": "playbackRateChanged", "read": "playbackRate", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPlaybackRate"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "loops", "notify": "loopsChanged", "read": "loops", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoops"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "playbackState", "notify": "playbackStateChanged", "read": "playbackState", "required": false, "scriptable": true, "stored": true, "type": "PlaybackState", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "mediaStatus", "notify": "mediaStatusChanged", "read": "mediaStatus", "required": false, "scriptable": true, "stored": true, "type": "MediaStatus", "user": false}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "metaData", "notify": "metaDataChanged", "read": "metaData", "required": false, "scriptable": true, "stored": true, "type": "QMediaMetaData", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "videoOutput", "notify": "videoOutputChanged", "read": "videoOutput", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setVideoOutput"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "audioOutput", "notify": "audioOutputChanged", "read": "audioOutput", "required": false, "scriptable": true, "stored": true, "type": "QAudioOutput*", "user": false, "write": "setAudioOutput"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "audioTracks", "notify": "tracksChanged", "read": "audioTracks", "required": false, "scriptable": true, "stored": true, "type": "QList<QMediaMetaData>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "videoTracks", "notify": "tracksChanged", "read": "videoTracks", "required": false, "scriptable": true, "stored": true, "type": "QList<QMediaMetaData>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "subtitleTracks", "notify": "tracksChanged", "read": "subtitleTracks", "required": false, "scriptable": true, "stored": true, "type": "QList<QMediaMetaData>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "activeAudioTrack", "notify": "activeTracksChanged", "read": "activeAudioTrack", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setActiveAudioTrack"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "activeVideoTrack", "notify": "activeTracksChanged", "read": "activeVideoTrack", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setActiveVideoTrack"}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "activeSubtitleTrack", "notify": "activeTracksChanged", "read": "activeSubtitleTrack", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setActiveSubtitleTrack"}], "qualifiedClassName": "QMediaPlayer", "signals": [{"access": "public", "arguments": [{"name": "media", "type": "QUrl"}], "name": "sourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newState", "type": "QMediaPlayer::PlaybackState"}], "name": "playbackStateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "QMediaPlayer::MediaStatus"}], "name": "mediaStatusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "qint64"}], "name": "durationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "qint64"}], "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "available", "type": "bool"}], "name": "hasAudioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "videoAvailable", "type": "bool"}], "name": "hasVideoChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "progress", "type": "float"}], "name": "bufferProgressChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "seekable", "type": "bool"}], "name": "seekableChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "playing", "type": "bool"}], "name": "playingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rate", "type": "qreal"}], "name": "playbackRateChanged", "returnType": "void"}, {"access": "public", "name": "loopsChanged", "returnType": "void"}, {"access": "public", "name": "metaDataChanged", "returnType": "void"}, {"access": "public", "name": "videoOutputChanged", "returnType": "void"}, {"access": "public", "name": "audioOutputChanged", "returnType": "void"}, {"access": "public", "name": "tracksChanged", "returnType": "void"}, {"access": "public", "name": "activeTracksChanged", "returnType": "void"}, {"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QMediaPlayer::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "name": "play", "returnType": "void"}, {"access": "public", "name": "pause", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "qint64"}], "name": "setPosition", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rate", "type": "qreal"}], "name": "setPlaybackRate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "source", "type": "QUrl"}], "name": "setSource", "returnType": "void"}, {"access": "public", "arguments": [{"name": "device", "type": "QIODevice*"}, {"name": "sourceUrl", "type": "QUrl"}], "name": "setSourceDevice", "returnType": "void"}, {"access": "public", "arguments": [{"name": "device", "type": "QIODevice*"}], "isCloned": true, "name": "setSourceDevice", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmediaplayer.h", "outputRevision": 68}, {"classes": [{"className": "QMediaRecorder", "enums": [{"isClass": false, "isFlag": false, "name": "Quality", "values": ["VeryLowQuality", "LowQuality", "NormalQuality", "HighQuality", "VeryHighQuality"]}, {"isClass": false, "isFlag": false, "name": "EncodingMode", "values": ["ConstantQualityEncoding", "ConstantBitRateEncoding", "AverageBitRateEncoding", "TwoPassEncoding"]}, {"isClass": false, "isFlag": false, "name": "RecorderState", "values": ["StoppedState", "RecordingState", "PausedState"]}, {"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "ResourceError", "FormatError", "OutOfSpaceError", "LocationNotWritable"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "recorderState", "notify": "recorderStateChanged", "read": "recorderState", "required": false, "scriptable": true, "stored": true, "type": "QMediaRecorder::RecorderState", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "qint64", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "outputLocation", "read": "outputLocation", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setOutputLocation"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "actualLocation", "notify": "actualLocationChanged", "read": "actualLocation", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "metaData", "notify": "metaDataChanged", "read": "metaData", "required": false, "scriptable": true, "stored": true, "type": "QMediaMetaData", "user": false, "write": "setMetaData"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "QMediaRecorder::<PERSON><PERSON><PERSON>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "mediaFormat", "notify": "mediaFormatChanged", "read": "mediaFormat", "required": false, "scriptable": true, "stored": true, "type": "QMediaFormat", "user": false, "write": "setMediaFormat"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "quality", "read": "quality", "required": false, "scriptable": true, "stored": true, "type": "Quality", "user": false, "write": "setQuality"}], "qualifiedClassName": "QMediaRecorder", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "RecorderState"}], "name": "recorderStateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "qint64"}], "name": "durationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "location", "type": "QUrl"}], "name": "actualLocationChanged", "returnType": "void"}, {"access": "public", "name": "encoderSettingsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "Error"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}, {"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "name": "metaDataChanged", "returnType": "void"}, {"access": "public", "name": "mediaFormatChanged", "returnType": "void"}, {"access": "public", "name": "encodingModeChanged", "returnType": "void"}, {"access": "public", "name": "qualityChanged", "returnType": "void"}, {"access": "public", "name": "videoResolutionChanged", "returnType": "void"}, {"access": "public", "name": "videoFrameRateChanged", "returnType": "void"}, {"access": "public", "name": "videoBitRateChanged", "returnType": "void"}, {"access": "public", "name": "audioBitRateChanged", "returnType": "void"}, {"access": "public", "name": "audioChannelCountChanged", "returnType": "void"}, {"access": "public", "name": "audioSampleRateChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "record", "returnType": "void"}, {"access": "public", "name": "pause", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmediarecorder.h", "outputRevision": 68}, {"classes": [{"className": "QPlatformAudioDecoder", "object": true, "qualifiedClassName": "QPlatformAudioDecoder", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformaudiodecoder_p.h", "outputRevision": 68}, {"classes": [{"className": "QPlatformCamera", "object": true, "qualifiedClassName": "QPlatformCamera", "signals": [{"access": "public", "arguments": [{"name": "error", "type": "int"}, {"name": "errorString", "type": "QString"}], "name": "error", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformVideoSource"}]}], "inputFile": "qplatformcamera_p.h", "outputRevision": 68}, {"classes": [{"className": "QPlatformImageCapture", "object": true, "qualifiedClassName": "QPlatformImageCapture", "signals": [{"access": "public", "arguments": [{"name": "ready", "type": "bool"}], "name": "readyForCaptureChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "requestId", "type": "int"}], "name": "imageExposed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "requestId", "type": "int"}, {"name": "preview", "type": "QImage"}], "name": "imageCaptured", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"type": "QMediaMetaData"}], "name": "imageMetadataAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "requestId", "type": "int"}, {"name": "buffer", "type": "QVideoFrame"}], "name": "imageAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "requestId", "type": "int"}, {"name": "fileName", "type": "QString"}], "name": "imageSaved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "error", "type": "int"}, {"name": "errorString", "type": "QString"}], "name": "error", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformimagecapture_p.h", "outputRevision": 68}, {"classes": [{"className": "QPlatformMediaCaptureSession", "object": true, "qualifiedClassName": "QPlatformMediaCaptureSession", "signals": [{"access": "public", "name": "cameraChanged", "returnType": "void"}, {"access": "public", "name": "screenCaptureChanged", "returnType": "void"}, {"access": "public", "name": "imageCaptureChanged", "returnType": "void"}, {"access": "public", "name": "encoderChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformmediacapture_p.h", "outputRevision": 68}, {"classes": [{"className": "QPlatformMediaDevices", "object": true, "qualifiedClassName": "QPlatformMediaDevices", "signals": [{"access": "public", "name": "audioInputsChanged", "returnType": "void"}, {"access": "public", "name": "audioOutputsChanged", "returnType": "void"}, {"access": "public", "name": "videoInputsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformmediadevices_p.h", "outputRevision": 68}, {"classes": [{"className": "QPlatformMediaPlugin", "object": true, "qualifiedClassName": "QPlatformMediaPlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformmediaplugin_p.h", "outputRevision": 68}, {"classes": [{"className": "QPlatformSurfaceCapture", "object": true, "qualifiedClassName": "QPlatformSurfaceCapture", "signals": [{"access": "public", "arguments": [{"type": "ScreenSource"}], "name": "sourceChanged", "returnType": "void"}, {"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "Error"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "error", "type": "Error"}, {"name": "errorString", "type": "QString"}], "name": "updateError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformVideoSource"}]}], "inputFile": "qplatformsurfacecapture_p.h", "outputRevision": 68}, {"classes": [{"className": "QPlatformVideoDevices", "object": true, "qualifiedClassName": "QPlatformVideoDevices", "signals": [{"access": "public", "name": "videoInputsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformvideodevices_p.h", "outputRevision": 68}, {"classes": [{"className": "QPlatformVideoSink", "object": true, "qualifiedClassName": "QPlatformVideoSink", "signals": [{"access": "public", "arguments": [{"name": "rhi", "type": "QRhi*"}], "name": "rhi<PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformvideosink_p.h", "outputRevision": 68}, {"classes": [{"className": "QSample", "object": true, "qualifiedClassName": "QSample", "signals": [{"access": "public", "name": "error", "returnType": "void"}, {"access": "public", "name": "ready", "returnType": "void"}], "slots": [{"access": "private", "name": "load", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QNetworkReply::NetworkError"}], "name": "loadingError", "returnType": "void"}, {"access": "private", "name": "decoderError", "returnType": "void"}, {"access": "private", "name": "readSample", "returnType": "void"}, {"access": "private", "name": "decoderReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QSampleCache", "object": true, "qualifiedClassName": "QSampleCache", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsamplecache_p.h", "outputRevision": 68}, {"classes": [{"className": "QScreenCapture", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "InternalError", "CapturingNotSupported", "CaptureFailed", "NotFound"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "screen", "notify": "screenChanged", "read": "screen", "required": false, "scriptable": true, "stored": true, "type": "QScreen*", "user": false, "write": "setScreen"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QScreenCapture", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "name": "activeChanged", "returnType": "void"}, {"access": "public", "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QScreen*"}], "name": "screenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QScreenCapture::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "active", "type": "bool"}], "name": "setActive", "returnType": "void"}, {"access": "public", "name": "start", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qscreencapture.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultMethod", "value": "play()"}], "className": "QSoundEffect", "enums": [{"isClass": false, "isFlag": false, "name": "Loop", "values": ["Infinite"]}, {"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Loading", "Ready", "Error"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "loops", "notify": "loopCountChanged", "read": "loopCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoopCount"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "loopsRemaining", "notify": "loopsRemainingChanged", "read": "loopsRemaining", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "muted", "notify": "mutedChanged", "read": "isMuted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMuted"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "playing", "notify": "playingChanged", "read": "isPlaying", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "audioDevice", "notify": "audioDeviceChanged", "read": "audioDevice", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false, "write": "setAudioDevice"}], "qualifiedClassName": "QSoundEffect", "signals": [{"access": "public", "name": "sourceChanged", "returnType": "void"}, {"access": "public", "name": "loopCountChanged", "returnType": "void"}, {"access": "public", "name": "loopsRemainingChanged", "returnType": "void"}, {"access": "public", "name": "volumeChanged", "returnType": "void"}, {"access": "public", "name": "mutedChanged", "returnType": "void"}, {"access": "public", "name": "loadedChanged", "returnType": "void"}, {"access": "public", "name": "playingChanged", "returnType": "void"}, {"access": "public", "name": "statusChanged", "returnType": "void"}, {"access": "public", "name": "audioDeviceChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "play", "returnType": "void"}, {"access": "public", "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsoundeffect.h", "outputRevision": 68}, {"classes": [{"className": "QVideoOutputOrientationHandler", "object": true, "qualifiedClassName": "QVideoOutputOrientationHandler", "signals": [{"access": "public", "arguments": [{"name": "angle", "type": "int"}], "name": "orientationChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "orientation", "type": "Qt::ScreenOrientation"}], "name": "screenOrientationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qvideooutputorientationhandler_p.h", "outputRevision": 68}, {"classes": [{"className": "QVideoSink", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "subtitleText", "notify": "subtitleTextChanged", "read": "subtitleText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSubtitleText"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "videoSize", "notify": "videoSizeChanged", "read": "videoSize", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false}], "qualifiedClassName": "QVideoSink", "signals": [{"access": "public", "arguments": [{"name": "frame", "type": "QVideoFrame"}], "name": "videoFrameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "subtitleText", "type": "QString"}], "name": "subtitleTextChanged", "returnType": "void"}, {"access": "public", "name": "videoSizeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qvideosink.h", "outputRevision": 68}, {"classes": [{"className": "QVideoWindow", "methods": [{"access": "public", "name": "videoSink", "returnType": "QVideoSink*"}], "object": true, "qualifiedClassName": "QVideoWindow", "signals": [{"access": "public", "arguments": [{"name": "mode", "type": "Qt::AspectRatioMode"}], "name": "aspectRatioModeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "mode", "type": "Qt::AspectRatioMode"}], "name": "setAspectRatioMode", "returnType": "void"}, {"access": "private", "arguments": [{"name": "frame", "type": "QVideoFrame"}], "name": "setVideoFrame", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWindow"}]}], "inputFile": "qvideowindow_p.h", "outputRevision": 68}, {"classes": [{"className": "QWaveDecoder", "object": true, "qualifiedClassName": "QWaveDecoder", "signals": [{"access": "public", "name": "formatKnown", "returnType": "void"}, {"access": "public", "name": "parsingError", "returnType": "void"}], "slots": [{"access": "private", "name": "handleData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qwavedecoder.h", "outputRevision": 68}]