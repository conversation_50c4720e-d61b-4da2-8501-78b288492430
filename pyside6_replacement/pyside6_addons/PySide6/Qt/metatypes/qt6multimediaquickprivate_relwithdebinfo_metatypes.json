[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "ImageCapture"}], "className": "QQuickImageCapture", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "preview", "notify": "previewChanged", "read": "preview", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QQuickImageCapture", "signals": [{"access": "public", "name": "previewChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "location", "type": "QUrl"}], "name": "saveToFile", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}, {"type": "QImage"}], "name": "_q_imageCaptured", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QImageCapture"}]}], "inputFile": "qquickimagecapture_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MediaPlayer"}], "className": "QQuickMediaPlayer", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "qmlSource", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "qmlSetSource"}], "qualifiedClassName": "QQuickMediaPlayer", "signals": [{"access": "public", "arguments": [{"name": "source", "type": "QUrl"}], "name": "sourceChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QMediaPlayer"}]}], "inputFile": "qquickmediaplayer_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ScreenCapture"}], "className": "QQuickScreenCatpure", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "screen", "notify": "screenChanged", "read": "qmlScreen", "required": false, "scriptable": true, "stored": true, "type": "QQuickScreenInfo*", "user": false, "write": "qmlSetScreen"}], "qualifiedClassName": "QQuickScreenCatpure", "signals": [{"access": "public", "arguments": [{"type": "QQuickScreenInfo*"}], "name": "screenChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QScreenCapture"}]}], "inputFile": "qquickscreencapture_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "SoundEffect"}], "className": "QQuickSoundEffect", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "qmlSource", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "qmlSetSource"}], "qualifiedClassName": "QQuickSoundEffect", "signals": [{"access": "public", "arguments": [{"name": "source", "type": "QUrl"}], "name": "sourceChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSoundEffect"}]}], "inputFile": "qquicksoundeffect_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "VideoOutput"}], "className": "QQuickVideoOutput", "enums": [{"isClass": false, "isFlag": false, "name": "FillMode", "values": ["<PERSON><PERSON><PERSON>", "PreserveAspectFit", "PreserveAspectCrop"]}], "methods": [{"access": "public", "name": "videoSink", "returnType": "QVideoSink*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fillMode", "notify": "fillModeChanged", "read": "fillMode", "required": false, "scriptable": true, "stored": true, "type": "FillMode", "user": false, "write": "setFillMode"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "orientation", "notify": "orientationChanged", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setOrientation"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "sourceRect", "notify": "sourceRectChanged", "read": "sourceRect", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "contentRect", "notify": "contentRectChanged", "read": "contentRect", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "videoSink", "read": "videoSink", "required": false, "scriptable": true, "stored": true, "type": "QVideoSink*", "user": false}], "qualifiedClassName": "QQuickVideoOutput", "signals": [{"access": "public", "name": "sourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QQuickVideoOutput::FillMode"}], "name": "fillModeChanged", "returnType": "void"}, {"access": "public", "name": "orientationChanged", "returnType": "void"}, {"access": "public", "name": "sourceRectChanged", "returnType": "void"}, {"access": "public", "name": "contentRectChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QSize"}], "name": "frameUpdated", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"type": "QSize"}], "name": "_q_new<PERSON>rame", "returnType": "void"}, {"access": "private", "name": "_q_updateGeometry", "returnType": "void"}, {"access": "private", "name": "_q_invalidateSceneGraph", "returnType": "void"}, {"access": "private", "name": "_q_sceneGraphInitialized", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qquickvideooutput_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Foreign", "value": "QMediaCaptureSession"}, {"name": "QML.Element", "value": "CaptureSession"}], "className": "QMediaCaptureSessionForeign", "gadget": true, "qualifiedClassName": "QMediaCaptureSessionForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QCamera"}, {"name": "QML.Element", "value": "Camera"}], "className": "QCameraForeign", "gadget": true, "qualifiedClassName": "QCameraForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QImageCapture"}], "className": "QImageCaptureForeign", "gadget": true, "qualifiedClassName": "QImageCaptureForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QScreenCapture"}], "className": "QScreenCaptureForeign", "gadget": true, "qualifiedClassName": "QScreenCaptureForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QMediaRecorder"}, {"name": "QML.Element", "value": "MediaRecorder"}], "className": "QMediaRecorderForeign", "gadget": true, "qualifiedClassName": "QMediaRecorderForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QMediaMetaData"}, {"name": "QML.Element", "value": "mediaMetaData"}], "className": "QMediaMetaDataForeign", "gadget": true, "qualifiedClassName": "QMediaMetaDataForeign"}, {"className": "QMediaMetaDataDerived", "gadget": true, "qualifiedClassName": "QMediaMetaDataDerived", "superClasses": [{"access": "public", "name": "QMediaMetaData"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QMediaDevices"}, {"name": "QML.Element", "value": "MediaDevices"}], "className": "QMediaDevicesForeign", "gadget": true, "qualifiedClassName": "QMediaDevicesForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QAudioInput"}, {"name": "QML.Element", "value": "AudioInput"}], "className": "QAudioInputForeign", "gadget": true, "qualifiedClassName": "QAudioInputForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QAudioOutput"}, {"name": "QML.Element", "value": "AudioOutput"}], "className": "QAudioOutputForeign", "gadget": true, "qualifiedClassName": "QAudioOutputForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QAudioDevice"}, {"name": "QML.Element", "value": "audioDevice"}], "className": "QAudioDeviceForeign", "gadget": true, "qualifiedClassName": "QAudioDeviceForeign"}, {"className": "QAudioDeviceDerived", "gadget": true, "qualifiedClassName": "QAudioDeviceDerived", "superClasses": [{"access": "public", "name": "QAudioDevice"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QCameraDevice"}, {"name": "QML.Element", "value": "cameraDevice"}], "className": "QCameraDeviceForeign", "gadget": true, "qualifiedClassName": "QCameraDeviceForeign"}, {"className": "QCameraDeviceDerived", "gadget": true, "qualifiedClassName": "QCameraDeviceDerived", "superClasses": [{"access": "public", "name": "QCameraDevice"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QMediaFormat"}, {"name": "QML.Element", "value": "mediaFormat"}], "className": "QMediaFormatForeign", "gadget": true, "qualifiedClassName": "QMediaFormatForeign"}, {"className": "QMediaFormatDerived", "gadget": true, "qualifiedClassName": "QMediaFormatDerived", "superClasses": [{"access": "public", "name": "QMediaFormat"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QCameraFormat"}, {"name": "QML.Element", "value": "cameraFormat"}], "className": "QCameraFormatForeign", "gadget": true, "qualifiedClassName": "QCameraFormatForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QVideoSink"}, {"name": "QML.Element", "value": "VideoSink"}], "className": "QVideoSinkForeign", "gadget": true, "qualifiedClassName": "QVideoSinkForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QMediaMetaDataDerived"}, {"name": "QML.Element", "value": "MediaMetaData"}], "className": "QMediaMetaDataNamespaceForeign", "namespace": true, "qualifiedClassName": "QMediaMetaDataNamespaceForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QAudioDeviceDerived"}, {"name": "QML.Element", "value": "AudioDevice"}], "className": "QAudioDeviceNamespaceForeign", "namespace": true, "qualifiedClassName": "QAudioDeviceNamespaceForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QCameraDeviceDerived"}, {"name": "QML.Element", "value": "CameraDevice"}], "className": "QCameraDeviceNamespaceForeign", "namespace": true, "qualifiedClassName": "QCameraDeviceNamespaceForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QMediaFormatDerived"}, {"name": "QML.Element", "value": "MediaFormat"}], "className": "QMediaFormatNamespaceForeign", "namespace": true, "qualifiedClassName": "QMediaFormatNamespaceForeign"}], "inputFile": "qtmultimediaquicktypes_p.h", "outputRevision": 68}]