# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtLocation, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtLocation`

import PySide6.QtLocation
import PySide6.QtCore
import PySide6.QtPositioning

import enum
from typing import Any, ClassVar, Dict, List, Optional, Sequence, Tuple, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QGeoCodeReply(PySide6.QtCore.QObject):

    aborted                  : ClassVar[Signal] = ... # aborted()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QGeoCodeReply::Error,QString)
    finished                 : ClassVar[Signal] = ... # finished()

    class Error(enum.Enum):

        NoError                  : QGeoCodeReply.Error = ... # 0x0
        EngineNotSetError        : QGeoCodeReply.Error = ... # 0x1
        CommunicationError       : QGeoCodeReply.Error = ... # 0x2
        ParseError               : QGeoCodeReply.Error = ... # 0x3
        UnsupportedOptionError   : QGeoCodeReply.Error = ... # 0x4
        CombinationError         : QGeoCodeReply.Error = ... # 0x5
        UnknownError             : QGeoCodeReply.Error = ... # 0x6


    @overload
    def __init__(self, error: PySide6.QtLocation.QGeoCodeReply.Error, errorString: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def abort(self) -> None: ...
    def addLocation(self, location: PySide6.QtPositioning.QGeoLocation) -> None: ...
    def error(self) -> PySide6.QtLocation.QGeoCodeReply.Error: ...
    def errorString(self) -> str: ...
    def isFinished(self) -> bool: ...
    def limit(self) -> int: ...
    def locations(self) -> List[PySide6.QtPositioning.QGeoLocation]: ...
    def offset(self) -> int: ...
    def setError(self, error: PySide6.QtLocation.QGeoCodeReply.Error, errorString: str) -> None: ...
    def setFinished(self, finished: bool) -> None: ...
    def setLimit(self, limit: int) -> None: ...
    def setLocations(self, locations: Sequence[PySide6.QtPositioning.QGeoLocation]) -> None: ...
    def setOffset(self, offset: int) -> None: ...
    def setViewport(self, viewport: PySide6.QtPositioning.QGeoShape) -> None: ...
    def viewport(self) -> PySide6.QtPositioning.QGeoShape: ...


class QGeoCodingManager(PySide6.QtCore.QObject):

    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QGeoCodeReply*,QGeoCodeReply::Error,QString)
    finished                 : ClassVar[Signal] = ... # finished(QGeoCodeReply*)
    @overload
    def geocode(self, address: PySide6.QtPositioning.QGeoAddress, bounds: PySide6.QtPositioning.QGeoShape = ...) -> PySide6.QtLocation.QGeoCodeReply: ...
    @overload
    def geocode(self, searchString: str, limit: int = ..., offset: int = ..., bounds: PySide6.QtPositioning.QGeoShape = ...) -> PySide6.QtLocation.QGeoCodeReply: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def managerName(self) -> str: ...
    def managerVersion(self) -> int: ...
    def reverseGeocode(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, bounds: PySide6.QtPositioning.QGeoShape = ...) -> PySide6.QtLocation.QGeoCodeReply: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...


class QGeoCodingManagerEngine(PySide6.QtCore.QObject):

    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QGeoCodeReply*,QGeoCodeReply::Error,QString)
    finished                 : ClassVar[Signal] = ... # finished(QGeoCodeReply*)

    def __init__(self, parameters: Dict[str, Any], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    @overload
    def geocode(self, address: PySide6.QtPositioning.QGeoAddress, bounds: PySide6.QtPositioning.QGeoShape) -> PySide6.QtLocation.QGeoCodeReply: ...
    @overload
    def geocode(self, address: str, limit: int, offset: int, bounds: PySide6.QtPositioning.QGeoShape) -> PySide6.QtLocation.QGeoCodeReply: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def managerName(self) -> str: ...
    def managerVersion(self) -> int: ...
    def reverseGeocode(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, bounds: PySide6.QtPositioning.QGeoShape) -> PySide6.QtLocation.QGeoCodeReply: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...


class QGeoManeuver(Shiboken.Object):

    class InstructionDirection(enum.Enum):

        NoDirection              : QGeoManeuver.InstructionDirection = ... # 0x0
        DirectionForward         : QGeoManeuver.InstructionDirection = ... # 0x1
        DirectionBearRight       : QGeoManeuver.InstructionDirection = ... # 0x2
        DirectionLightRight      : QGeoManeuver.InstructionDirection = ... # 0x3
        DirectionRight           : QGeoManeuver.InstructionDirection = ... # 0x4
        DirectionHardRight       : QGeoManeuver.InstructionDirection = ... # 0x5
        DirectionUTurnRight      : QGeoManeuver.InstructionDirection = ... # 0x6
        DirectionUTurnLeft       : QGeoManeuver.InstructionDirection = ... # 0x7
        DirectionHardLeft        : QGeoManeuver.InstructionDirection = ... # 0x8
        DirectionLeft            : QGeoManeuver.InstructionDirection = ... # 0x9
        DirectionLightLeft       : QGeoManeuver.InstructionDirection = ... # 0xa
        DirectionBearLeft        : QGeoManeuver.InstructionDirection = ... # 0xb


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QGeoManeuver) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def direction(self) -> PySide6.QtLocation.QGeoManeuver.InstructionDirection: ...
    def distanceToNextInstruction(self) -> float: ...
    def extendedAttributes(self) -> Dict[str, Any]: ...
    def instructionText(self) -> str: ...
    def isValid(self) -> bool: ...
    def position(self) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def setDirection(self, direction: PySide6.QtLocation.QGeoManeuver.InstructionDirection) -> None: ...
    def setDistanceToNextInstruction(self, distance: float) -> None: ...
    def setExtendedAttributes(self, extendedAttributes: Dict[str, Any]) -> None: ...
    def setInstructionText(self, instructionText: str) -> None: ...
    def setPosition(self, position: PySide6.QtPositioning.QGeoCoordinate) -> None: ...
    def setTimeToNextInstruction(self, secs: int) -> None: ...
    def setWaypoint(self, coordinate: PySide6.QtPositioning.QGeoCoordinate) -> None: ...
    def swap(self, other: PySide6.QtLocation.QGeoManeuver) -> None: ...
    def timeToNextInstruction(self) -> int: ...
    def waypoint(self) -> PySide6.QtPositioning.QGeoCoordinate: ...


class QGeoRoute(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QGeoRoute) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def bounds(self) -> PySide6.QtPositioning.QGeoRectangle: ...
    def distance(self) -> float: ...
    def extendedAttributes(self) -> Dict[str, Any]: ...
    def firstRouteSegment(self) -> PySide6.QtLocation.QGeoRouteSegment: ...
    def legIndex(self) -> int: ...
    def overallRoute(self) -> PySide6.QtLocation.QGeoRoute: ...
    def path(self) -> List[PySide6.QtPositioning.QGeoCoordinate]: ...
    def request(self) -> PySide6.QtLocation.QGeoRouteRequest: ...
    def routeId(self) -> str: ...
    def routeLegs(self) -> List[PySide6.QtLocation.QGeoRoute]: ...
    def segments(self) -> List[PySide6.QtLocation.QGeoRouteSegment]: ...
    def segmentsCount(self) -> int: ...
    def setBounds(self, bounds: Union[PySide6.QtPositioning.QGeoRectangle, PySide6.QtPositioning.QGeoShape, Sequence[PySide6.QtPositioning.QGeoCoordinate]]) -> None: ...
    def setDistance(self, distance: float) -> None: ...
    def setExtendedAttributes(self, extendedAttributes: Dict[str, Any]) -> None: ...
    def setFirstRouteSegment(self, routeSegment: PySide6.QtLocation.QGeoRouteSegment) -> None: ...
    def setLegIndex(self, idx: int) -> None: ...
    def setOverallRoute(self, route: PySide6.QtLocation.QGeoRoute) -> None: ...
    def setPath(self, path: Sequence[PySide6.QtPositioning.QGeoCoordinate]) -> None: ...
    def setRequest(self, request: PySide6.QtLocation.QGeoRouteRequest) -> None: ...
    def setRouteId(self, id: str) -> None: ...
    def setRouteLegs(self, legs: Sequence[PySide6.QtLocation.QGeoRoute]) -> None: ...
    def setTravelMode(self, mode: PySide6.QtLocation.QGeoRouteRequest.TravelMode) -> None: ...
    def setTravelTime(self, secs: int) -> None: ...
    def swap(self, other: PySide6.QtLocation.QGeoRoute) -> None: ...
    def travelMode(self) -> PySide6.QtLocation.QGeoRouteRequest.TravelMode: ...
    def travelTime(self) -> int: ...


class QGeoRouteReply(PySide6.QtCore.QObject):

    aborted                  : ClassVar[Signal] = ... # aborted()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QGeoRouteReply::Error,QString)
    finished                 : ClassVar[Signal] = ... # finished()

    class Error(enum.Enum):

        NoError                  : QGeoRouteReply.Error = ... # 0x0
        EngineNotSetError        : QGeoRouteReply.Error = ... # 0x1
        CommunicationError       : QGeoRouteReply.Error = ... # 0x2
        ParseError               : QGeoRouteReply.Error = ... # 0x3
        UnsupportedOptionError   : QGeoRouteReply.Error = ... # 0x4
        UnknownError             : QGeoRouteReply.Error = ... # 0x5


    @overload
    def __init__(self, error: PySide6.QtLocation.QGeoRouteReply.Error, errorString: str, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
    @overload
    def __init__(self, request: PySide6.QtLocation.QGeoRouteRequest, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def abort(self) -> None: ...
    def addRoutes(self, routes: Sequence[PySide6.QtLocation.QGeoRoute]) -> None: ...
    def error(self) -> PySide6.QtLocation.QGeoRouteReply.Error: ...
    def errorString(self) -> str: ...
    def isFinished(self) -> bool: ...
    def request(self) -> PySide6.QtLocation.QGeoRouteRequest: ...
    def routes(self) -> List[PySide6.QtLocation.QGeoRoute]: ...
    def setError(self, error: PySide6.QtLocation.QGeoRouteReply.Error, errorString: str) -> None: ...
    def setFinished(self, finished: bool) -> None: ...
    def setRoutes(self, routes: Sequence[PySide6.QtLocation.QGeoRoute]) -> None: ...


class QGeoRouteRequest(Shiboken.Object):

    class FeatureType(enum.Flag):

        NoFeature                : QGeoRouteRequest.FeatureType = ... # 0x0
        TollFeature              : QGeoRouteRequest.FeatureType = ... # 0x1
        HighwayFeature           : QGeoRouteRequest.FeatureType = ... # 0x2
        PublicTransitFeature     : QGeoRouteRequest.FeatureType = ... # 0x4
        FerryFeature             : QGeoRouteRequest.FeatureType = ... # 0x8
        TunnelFeature            : QGeoRouteRequest.FeatureType = ... # 0x10
        DirtRoadFeature          : QGeoRouteRequest.FeatureType = ... # 0x20
        ParksFeature             : QGeoRouteRequest.FeatureType = ... # 0x40
        MotorPoolLaneFeature     : QGeoRouteRequest.FeatureType = ... # 0x80
        TrafficFeature           : QGeoRouteRequest.FeatureType = ... # 0x100


    class FeatureWeight(enum.Flag):

        NeutralFeatureWeight     : QGeoRouteRequest.FeatureWeight = ... # 0x0
        PreferFeatureWeight      : QGeoRouteRequest.FeatureWeight = ... # 0x1
        RequireFeatureWeight     : QGeoRouteRequest.FeatureWeight = ... # 0x2
        AvoidFeatureWeight       : QGeoRouteRequest.FeatureWeight = ... # 0x4
        DisallowFeatureWeight    : QGeoRouteRequest.FeatureWeight = ... # 0x8


    class ManeuverDetail(enum.Flag):

        NoManeuvers              : QGeoRouteRequest.ManeuverDetail = ... # 0x0
        BasicManeuvers           : QGeoRouteRequest.ManeuverDetail = ... # 0x1


    class RouteOptimization(enum.Flag):

        ShortestRoute            : QGeoRouteRequest.RouteOptimization = ... # 0x1
        FastestRoute             : QGeoRouteRequest.RouteOptimization = ... # 0x2
        MostEconomicRoute        : QGeoRouteRequest.RouteOptimization = ... # 0x4
        MostScenicRoute          : QGeoRouteRequest.RouteOptimization = ... # 0x8


    class SegmentDetail(enum.Flag):

        NoSegmentData            : QGeoRouteRequest.SegmentDetail = ... # 0x0
        BasicSegmentData         : QGeoRouteRequest.SegmentDetail = ... # 0x1


    class TravelMode(enum.Flag):

        CarTravel                : QGeoRouteRequest.TravelMode = ... # 0x1
        PedestrianTravel         : QGeoRouteRequest.TravelMode = ... # 0x2
        BicycleTravel            : QGeoRouteRequest.TravelMode = ... # 0x4
        PublicTransitTravel      : QGeoRouteRequest.TravelMode = ... # 0x8
        TruckTravel              : QGeoRouteRequest.TravelMode = ... # 0x10


    @overload
    def __init__(self, origin: PySide6.QtPositioning.QGeoCoordinate, destination: PySide6.QtPositioning.QGeoCoordinate) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QGeoRouteRequest) -> None: ...
    @overload
    def __init__(self, waypoints: Sequence[PySide6.QtPositioning.QGeoCoordinate] = ...) -> None: ...

    def departureTime(self) -> PySide6.QtCore.QDateTime: ...
    def excludeAreas(self) -> List[PySide6.QtPositioning.QGeoRectangle]: ...
    def featureTypes(self) -> List[PySide6.QtLocation.QGeoRouteRequest.FeatureType]: ...
    def featureWeight(self, featureType: PySide6.QtLocation.QGeoRouteRequest.FeatureType) -> PySide6.QtLocation.QGeoRouteRequest.FeatureWeight: ...
    def maneuverDetail(self) -> PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail: ...
    def numberAlternativeRoutes(self) -> int: ...
    def routeOptimization(self) -> PySide6.QtLocation.QGeoRouteRequest.RouteOptimization: ...
    def segmentDetail(self) -> PySide6.QtLocation.QGeoRouteRequest.SegmentDetail: ...
    def setDepartureTime(self, departureTime: PySide6.QtCore.QDateTime) -> None: ...
    def setExcludeAreas(self, areas: Sequence[PySide6.QtPositioning.QGeoRectangle]) -> None: ...
    def setFeatureWeight(self, featureType: PySide6.QtLocation.QGeoRouteRequest.FeatureType, featureWeight: PySide6.QtLocation.QGeoRouteRequest.FeatureWeight) -> None: ...
    def setManeuverDetail(self, maneuverDetail: PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail) -> None: ...
    def setNumberAlternativeRoutes(self, alternatives: int) -> None: ...
    def setRouteOptimization(self, optimization: PySide6.QtLocation.QGeoRouteRequest.RouteOptimization) -> None: ...
    def setSegmentDetail(self, segmentDetail: PySide6.QtLocation.QGeoRouteRequest.SegmentDetail) -> None: ...
    def setTravelModes(self, travelModes: PySide6.QtLocation.QGeoRouteRequest.TravelMode) -> None: ...
    def setWaypoints(self, waypoints: Sequence[PySide6.QtPositioning.QGeoCoordinate]) -> None: ...
    def swap(self, other: PySide6.QtLocation.QGeoRouteRequest) -> None: ...
    def travelModes(self) -> PySide6.QtLocation.QGeoRouteRequest.TravelMode: ...
    def waypoints(self) -> List[PySide6.QtPositioning.QGeoCoordinate]: ...


class QGeoRouteSegment(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QGeoRouteSegment) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def distance(self) -> float: ...
    def isLegLastSegment(self) -> bool: ...
    def isValid(self) -> bool: ...
    def maneuver(self) -> PySide6.QtLocation.QGeoManeuver: ...
    def nextRouteSegment(self) -> PySide6.QtLocation.QGeoRouteSegment: ...
    def path(self) -> List[PySide6.QtPositioning.QGeoCoordinate]: ...
    def setDistance(self, distance: float) -> None: ...
    def setManeuver(self, maneuver: PySide6.QtLocation.QGeoManeuver) -> None: ...
    def setNextRouteSegment(self, routeSegment: PySide6.QtLocation.QGeoRouteSegment) -> None: ...
    def setPath(self, path: Sequence[PySide6.QtPositioning.QGeoCoordinate]) -> None: ...
    def setTravelTime(self, secs: int) -> None: ...
    def swap(self, other: PySide6.QtLocation.QGeoRouteSegment) -> None: ...
    def travelTime(self) -> int: ...


class QGeoRoutingManager(PySide6.QtCore.QObject):

    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QGeoRouteReply*,QGeoRouteReply::Error,QString)
    finished                 : ClassVar[Signal] = ... # finished(QGeoRouteReply*)
    def calculateRoute(self, request: PySide6.QtLocation.QGeoRouteRequest) -> PySide6.QtLocation.QGeoRouteReply: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def managerName(self) -> str: ...
    def managerVersion(self) -> int: ...
    def measurementSystem(self) -> PySide6.QtCore.QLocale.MeasurementSystem: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...
    def setMeasurementSystem(self, system: PySide6.QtCore.QLocale.MeasurementSystem) -> None: ...
    def supportedFeatureTypes(self) -> PySide6.QtLocation.QGeoRouteRequest.FeatureType: ...
    def supportedFeatureWeights(self) -> PySide6.QtLocation.QGeoRouteRequest.FeatureWeight: ...
    def supportedManeuverDetails(self) -> PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail: ...
    def supportedRouteOptimizations(self) -> PySide6.QtLocation.QGeoRouteRequest.RouteOptimization: ...
    def supportedSegmentDetails(self) -> PySide6.QtLocation.QGeoRouteRequest.SegmentDetail: ...
    def supportedTravelModes(self) -> PySide6.QtLocation.QGeoRouteRequest.TravelMode: ...
    def updateRoute(self, route: PySide6.QtLocation.QGeoRoute, position: PySide6.QtPositioning.QGeoCoordinate) -> PySide6.QtLocation.QGeoRouteReply: ...


class QGeoRoutingManagerEngine(PySide6.QtCore.QObject):

    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QGeoRouteReply*,QGeoRouteReply::Error,QString)
    finished                 : ClassVar[Signal] = ... # finished(QGeoRouteReply*)

    def __init__(self, parameters: Dict[str, Any], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def calculateRoute(self, request: PySide6.QtLocation.QGeoRouteRequest) -> PySide6.QtLocation.QGeoRouteReply: ...
    def locale(self) -> PySide6.QtCore.QLocale: ...
    def managerName(self) -> str: ...
    def managerVersion(self) -> int: ...
    def measurementSystem(self) -> PySide6.QtCore.QLocale.MeasurementSystem: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...
    def setMeasurementSystem(self, system: PySide6.QtCore.QLocale.MeasurementSystem) -> None: ...
    def setSupportedFeatureTypes(self, featureTypes: PySide6.QtLocation.QGeoRouteRequest.FeatureType) -> None: ...
    def setSupportedFeatureWeights(self, featureWeights: PySide6.QtLocation.QGeoRouteRequest.FeatureWeight) -> None: ...
    def setSupportedManeuverDetails(self, maneuverDetails: PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail) -> None: ...
    def setSupportedRouteOptimizations(self, optimizations: PySide6.QtLocation.QGeoRouteRequest.RouteOptimization) -> None: ...
    def setSupportedSegmentDetails(self, segmentDetails: PySide6.QtLocation.QGeoRouteRequest.SegmentDetail) -> None: ...
    def setSupportedTravelModes(self, travelModes: PySide6.QtLocation.QGeoRouteRequest.TravelMode) -> None: ...
    def supportedFeatureTypes(self) -> PySide6.QtLocation.QGeoRouteRequest.FeatureType: ...
    def supportedFeatureWeights(self) -> PySide6.QtLocation.QGeoRouteRequest.FeatureWeight: ...
    def supportedManeuverDetails(self) -> PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail: ...
    def supportedRouteOptimizations(self) -> PySide6.QtLocation.QGeoRouteRequest.RouteOptimization: ...
    def supportedSegmentDetails(self) -> PySide6.QtLocation.QGeoRouteRequest.SegmentDetail: ...
    def supportedTravelModes(self) -> PySide6.QtLocation.QGeoRouteRequest.TravelMode: ...
    def updateRoute(self, route: PySide6.QtLocation.QGeoRoute, position: PySide6.QtPositioning.QGeoCoordinate) -> PySide6.QtLocation.QGeoRouteReply: ...


class QGeoServiceProvider(PySide6.QtCore.QObject):

    class Error(enum.Enum):

        NoError                  : QGeoServiceProvider.Error = ... # 0x0
        NotSupportedError        : QGeoServiceProvider.Error = ... # 0x1
        UnknownParameterError    : QGeoServiceProvider.Error = ... # 0x2
        MissingRequiredParameterError: QGeoServiceProvider.Error = ... # 0x3
        ConnectionError          : QGeoServiceProvider.Error = ... # 0x4
        LoaderError              : QGeoServiceProvider.Error = ... # 0x5


    class GeocodingFeature(enum.Flag):

        AnyGeocodingFeatures     : QGeoServiceProvider.GeocodingFeature = ... # -0x1
        NoGeocodingFeatures      : QGeoServiceProvider.GeocodingFeature = ... # 0x0
        OnlineGeocodingFeature   : QGeoServiceProvider.GeocodingFeature = ... # 0x1
        OfflineGeocodingFeature  : QGeoServiceProvider.GeocodingFeature = ... # 0x2
        ReverseGeocodingFeature  : QGeoServiceProvider.GeocodingFeature = ... # 0x4
        LocalizedGeocodingFeature: QGeoServiceProvider.GeocodingFeature = ... # 0x8


    class MappingFeature(enum.Flag):

        AnyMappingFeatures       : QGeoServiceProvider.MappingFeature = ... # -0x1
        NoMappingFeatures        : QGeoServiceProvider.MappingFeature = ... # 0x0
        OnlineMappingFeature     : QGeoServiceProvider.MappingFeature = ... # 0x1
        OfflineMappingFeature    : QGeoServiceProvider.MappingFeature = ... # 0x2
        LocalizedMappingFeature  : QGeoServiceProvider.MappingFeature = ... # 0x4


    class NavigationFeature(enum.Flag):

        AnyNavigationFeatures    : QGeoServiceProvider.NavigationFeature = ... # -0x1
        NoNavigationFeatures     : QGeoServiceProvider.NavigationFeature = ... # 0x0
        OnlineNavigationFeature  : QGeoServiceProvider.NavigationFeature = ... # 0x1
        OfflineNavigationFeature : QGeoServiceProvider.NavigationFeature = ... # 0x2


    class PlacesFeature(enum.Flag):

        AnyPlacesFeatures        : QGeoServiceProvider.PlacesFeature = ... # -0x1
        NoPlacesFeatures         : QGeoServiceProvider.PlacesFeature = ... # 0x0
        OnlinePlacesFeature      : QGeoServiceProvider.PlacesFeature = ... # 0x1
        OfflinePlacesFeature     : QGeoServiceProvider.PlacesFeature = ... # 0x2
        SavePlaceFeature         : QGeoServiceProvider.PlacesFeature = ... # 0x4
        RemovePlaceFeature       : QGeoServiceProvider.PlacesFeature = ... # 0x8
        SaveCategoryFeature      : QGeoServiceProvider.PlacesFeature = ... # 0x10
        RemoveCategoryFeature    : QGeoServiceProvider.PlacesFeature = ... # 0x20
        PlaceRecommendationsFeature: QGeoServiceProvider.PlacesFeature = ... # 0x40
        SearchSuggestionsFeature : QGeoServiceProvider.PlacesFeature = ... # 0x80
        LocalizedPlacesFeature   : QGeoServiceProvider.PlacesFeature = ... # 0x100
        NotificationsFeature     : QGeoServiceProvider.PlacesFeature = ... # 0x200
        PlaceMatchingFeature     : QGeoServiceProvider.PlacesFeature = ... # 0x400


    class RoutingFeature(enum.Flag):

        AnyRoutingFeatures       : QGeoServiceProvider.RoutingFeature = ... # -0x1
        NoRoutingFeatures        : QGeoServiceProvider.RoutingFeature = ... # 0x0
        OnlineRoutingFeature     : QGeoServiceProvider.RoutingFeature = ... # 0x1
        OfflineRoutingFeature    : QGeoServiceProvider.RoutingFeature = ... # 0x2
        LocalizedRoutingFeature  : QGeoServiceProvider.RoutingFeature = ... # 0x4
        RouteUpdatesFeature      : QGeoServiceProvider.RoutingFeature = ... # 0x8
        AlternativeRoutesFeature : QGeoServiceProvider.RoutingFeature = ... # 0x10
        ExcludeAreasRoutingFeature: QGeoServiceProvider.RoutingFeature = ... # 0x20


    def __init__(self, providerName: str, parameters: Dict[str, Any] = ..., allowExperimental: bool = ...) -> None: ...

    @staticmethod
    def availableServiceProviders() -> List[str]: ...
    def error(self) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def errorString(self) -> str: ...
    def geocodingError(self) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def geocodingErrorString(self) -> str: ...
    def geocodingFeatures(self) -> PySide6.QtLocation.QGeoServiceProvider.GeocodingFeature: ...
    def geocodingManager(self) -> PySide6.QtLocation.QGeoCodingManager: ...
    def mappingError(self) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def mappingErrorString(self) -> str: ...
    def mappingFeatures(self) -> PySide6.QtLocation.QGeoServiceProvider.MappingFeature: ...
    def navigationError(self) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def navigationErrorString(self) -> str: ...
    def navigationFeatures(self) -> PySide6.QtLocation.QGeoServiceProvider.NavigationFeature: ...
    def placeManager(self) -> PySide6.QtLocation.QPlaceManager: ...
    def placesError(self) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def placesErrorString(self) -> str: ...
    def placesFeatures(self) -> PySide6.QtLocation.QGeoServiceProvider.PlacesFeature: ...
    def routingError(self) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def routingErrorString(self) -> str: ...
    def routingFeatures(self) -> PySide6.QtLocation.QGeoServiceProvider.RoutingFeature: ...
    def routingManager(self) -> PySide6.QtLocation.QGeoRoutingManager: ...
    def setAllowExperimental(self, allow: bool) -> None: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...
    def setParameters(self, parameters: Dict[str, Any]) -> None: ...


class QGeoServiceProviderFactory(Shiboken.Object):

    def __init__(self) -> None: ...

    def createGeocodingManagerEngine(self, parameters: Dict[str, Any], error: PySide6.QtLocation.QGeoServiceProvider.Error) -> Tuple[PySide6.QtLocation.QGeoCodingManagerEngine, str]: ...
    def createPlaceManagerEngine(self, parameters: Dict[str, Any], error: PySide6.QtLocation.QGeoServiceProvider.Error) -> Tuple[PySide6.QtLocation.QPlaceManagerEngine, str]: ...
    def createRoutingManagerEngine(self, parameters: Dict[str, Any], error: PySide6.QtLocation.QGeoServiceProvider.Error) -> Tuple[PySide6.QtLocation.QGeoRoutingManagerEngine, str]: ...


class QIntList(object): ...


class QPlace(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlace) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def appendContactDetail(self, contactType: str, detail: PySide6.QtLocation.QPlaceContactDetail) -> None: ...
    def attribution(self) -> str: ...
    def categories(self) -> List[PySide6.QtLocation.QPlaceCategory]: ...
    def contactDetails(self, contactType: str) -> List[PySide6.QtLocation.QPlaceContactDetail]: ...
    def contactTypes(self) -> List[str]: ...
    def content(self, type: PySide6.QtLocation.QPlaceContent.Type) -> Dict[int, PySide6.QtLocation.QPlaceContent]: ...
    def detailsFetched(self) -> bool: ...
    def extendedAttribute(self, attributeType: str) -> PySide6.QtLocation.QPlaceAttribute: ...
    def extendedAttributeTypes(self) -> List[str]: ...
    def icon(self) -> PySide6.QtLocation.QPlaceIcon: ...
    def insertContent(self, type: PySide6.QtLocation.QPlaceContent.Type, content: Dict[int, PySide6.QtLocation.QPlaceContent]) -> None: ...
    def isEmpty(self) -> bool: ...
    def location(self) -> PySide6.QtPositioning.QGeoLocation: ...
    def name(self) -> str: ...
    def placeId(self) -> str: ...
    def primaryEmail(self) -> str: ...
    def primaryFax(self) -> str: ...
    def primaryPhone(self) -> str: ...
    def primaryWebsite(self) -> PySide6.QtCore.QUrl: ...
    def ratings(self) -> PySide6.QtLocation.QPlaceRatings: ...
    def removeContactDetails(self, contactType: str) -> None: ...
    def removeExtendedAttribute(self, attributeType: str) -> None: ...
    def setAttribution(self, attribution: str) -> None: ...
    def setCategories(self, categories: Sequence[PySide6.QtLocation.QPlaceCategory]) -> None: ...
    def setCategory(self, category: PySide6.QtLocation.QPlaceCategory) -> None: ...
    def setContactDetails(self, contactType: str, details: Sequence[PySide6.QtLocation.QPlaceContactDetail]) -> None: ...
    def setContent(self, type: PySide6.QtLocation.QPlaceContent.Type, content: Dict[int, PySide6.QtLocation.QPlaceContent]) -> None: ...
    def setDetailsFetched(self, fetched: bool) -> None: ...
    def setExtendedAttribute(self, attributeType: str, attribute: PySide6.QtLocation.QPlaceAttribute) -> None: ...
    def setIcon(self, icon: PySide6.QtLocation.QPlaceIcon) -> None: ...
    def setLocation(self, location: PySide6.QtPositioning.QGeoLocation) -> None: ...
    def setName(self, name: str) -> None: ...
    def setPlaceId(self, identifier: str) -> None: ...
    def setRatings(self, ratings: PySide6.QtLocation.QPlaceRatings) -> None: ...
    def setSupplier(self, supplier: PySide6.QtLocation.QPlaceSupplier) -> None: ...
    def setTotalContentCount(self, type: PySide6.QtLocation.QPlaceContent.Type, total: int) -> None: ...
    def supplier(self) -> PySide6.QtLocation.QPlaceSupplier: ...
    def swap(self, other: PySide6.QtLocation.QPlace) -> None: ...
    def totalContentCount(self, type: PySide6.QtLocation.QPlaceContent.Type) -> int: ...


class QPlaceAttribute(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceAttribute) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def isEmpty(self) -> bool: ...
    def label(self) -> str: ...
    def setLabel(self, label: str) -> None: ...
    def setText(self, text: str) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceAttribute) -> None: ...
    def text(self) -> str: ...


class QPlaceCategory(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceCategory) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def categoryId(self) -> str: ...
    def icon(self) -> PySide6.QtLocation.QPlaceIcon: ...
    def isEmpty(self) -> bool: ...
    def name(self) -> str: ...
    def setCategoryId(self, identifier: str) -> None: ...
    def setIcon(self, icon: PySide6.QtLocation.QPlaceIcon) -> None: ...
    def setName(self, name: str) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceCategory) -> None: ...


class QPlaceContactDetail(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceContactDetail) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def clear(self) -> None: ...
    def label(self) -> str: ...
    def setLabel(self, label: str) -> None: ...
    def setValue(self, value: str) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceContactDetail) -> None: ...
    def value(self) -> str: ...


class QPlaceContent(Shiboken.Object):

    class DataTag(enum.Enum):

        ContentSupplier          : QPlaceContent.DataTag = ... # 0x0
        ContentUser              : QPlaceContent.DataTag = ... # 0x1
        ContentAttribution       : QPlaceContent.DataTag = ... # 0x2
        ImageId                  : QPlaceContent.DataTag = ... # 0x3
        ImageUrl                 : QPlaceContent.DataTag = ... # 0x4
        ImageMimeType            : QPlaceContent.DataTag = ... # 0x5
        EditorialTitle           : QPlaceContent.DataTag = ... # 0x6
        EditorialText            : QPlaceContent.DataTag = ... # 0x7
        EditorialLanguage        : QPlaceContent.DataTag = ... # 0x8
        ReviewId                 : QPlaceContent.DataTag = ... # 0x9
        ReviewDateTime           : QPlaceContent.DataTag = ... # 0xa
        ReviewTitle              : QPlaceContent.DataTag = ... # 0xb
        ReviewText               : QPlaceContent.DataTag = ... # 0xc
        ReviewLanguage           : QPlaceContent.DataTag = ... # 0xd
        ReviewRating             : QPlaceContent.DataTag = ... # 0xe
        CustomDataTag            : QPlaceContent.DataTag = ... # 0x3e8


    class Type(enum.Enum):

        NoType                   : QPlaceContent.Type = ... # 0x0
        ImageType                : QPlaceContent.Type = ... # 0x1
        ReviewType               : QPlaceContent.Type = ... # 0x2
        EditorialType            : QPlaceContent.Type = ... # 0x3
        CustomType               : QPlaceContent.Type = ... # 0x100


    @overload
    def __init__(self, other: Union[PySide6.QtLocation.QPlaceContent, PySide6.QtLocation.QPlaceContent.Type]) -> None: ...
    @overload
    def __init__(self, type: PySide6.QtLocation.QPlaceContent.Type = ...) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def attribution(self) -> str: ...
    def dataTags(self) -> List[PySide6.QtLocation.QPlaceContent.DataTag]: ...
    def setAttribution(self, attribution: str) -> None: ...
    def setSupplier(self, supplier: PySide6.QtLocation.QPlaceSupplier) -> None: ...
    def setUser(self, user: PySide6.QtLocation.QPlaceUser) -> None: ...
    def setValue(self, tag: PySide6.QtLocation.QPlaceContent.DataTag, arg__2: Any) -> None: ...
    def supplier(self) -> PySide6.QtLocation.QPlaceSupplier: ...
    def swap(self, other: Union[PySide6.QtLocation.QPlaceContent, PySide6.QtLocation.QPlaceContent.Type]) -> None: ...
    def type(self) -> PySide6.QtLocation.QPlaceContent.Type: ...
    def user(self) -> PySide6.QtLocation.QPlaceUser: ...
    def value(self, tag: PySide6.QtLocation.QPlaceContent.DataTag) -> Any: ...


class QPlaceContentReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def content(self) -> Dict[int, PySide6.QtLocation.QPlaceContent]: ...
    def nextPageRequest(self) -> PySide6.QtLocation.QPlaceContentRequest: ...
    def previousPageRequest(self) -> PySide6.QtLocation.QPlaceContentRequest: ...
    def request(self) -> PySide6.QtLocation.QPlaceContentRequest: ...
    def setContent(self, content: Dict[int, PySide6.QtLocation.QPlaceContent]) -> None: ...
    def setNextPageRequest(self, next: PySide6.QtLocation.QPlaceContentRequest) -> None: ...
    def setPreviousPageRequest(self, previous: PySide6.QtLocation.QPlaceContentRequest) -> None: ...
    def setRequest(self, request: PySide6.QtLocation.QPlaceContentRequest) -> None: ...
    def setTotalCount(self, total: int) -> None: ...
    def totalCount(self) -> int: ...
    def type(self) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceContentRequest(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceContentRequest) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def clear(self) -> None: ...
    def contentContext(self) -> Any: ...
    def contentType(self) -> PySide6.QtLocation.QPlaceContent.Type: ...
    def limit(self) -> int: ...
    def placeId(self) -> str: ...
    def setContentContext(self, context: Any) -> None: ...
    def setContentType(self, type: PySide6.QtLocation.QPlaceContent.Type) -> None: ...
    def setLimit(self, limit: int) -> None: ...
    def setPlaceId(self, identifier: str) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceContentRequest) -> None: ...


class QPlaceDetailsReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def place(self) -> PySide6.QtLocation.QPlace: ...
    def setPlace(self, place: PySide6.QtLocation.QPlace) -> None: ...
    def type(self) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceIcon(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceIcon) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def isEmpty(self) -> bool: ...
    def manager(self) -> PySide6.QtLocation.QPlaceManager: ...
    def parameters(self) -> Dict[str, Any]: ...
    def setManager(self, manager: PySide6.QtLocation.QPlaceManager) -> None: ...
    def setParameters(self, parameters: Dict[str, Any]) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceIcon) -> None: ...
    def url(self, size: PySide6.QtCore.QSize = ...) -> PySide6.QtCore.QUrl: ...


class QPlaceIdReply(PySide6.QtLocation.QPlaceReply):

    class OperationType(enum.Enum):

        SavePlace                : QPlaceIdReply.OperationType = ... # 0x0
        SaveCategory             : QPlaceIdReply.OperationType = ... # 0x1
        RemovePlace              : QPlaceIdReply.OperationType = ... # 0x2
        RemoveCategory           : QPlaceIdReply.OperationType = ... # 0x3


    def __init__(self, operationType: PySide6.QtLocation.QPlaceIdReply.OperationType, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def id(self) -> str: ...
    def operationType(self) -> PySide6.QtLocation.QPlaceIdReply.OperationType: ...
    def setId(self, identifier: str) -> None: ...
    def type(self) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceManager(PySide6.QtCore.QObject):

    categoryAdded            : ClassVar[Signal] = ... # categoryAdded(QPlaceCategory,QString)
    categoryRemoved          : ClassVar[Signal] = ... # categoryRemoved(QString,QString)
    categoryUpdated          : ClassVar[Signal] = ... # categoryUpdated(QPlaceCategory,QString)
    dataChanged              : ClassVar[Signal] = ... # dataChanged()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QPlaceReply*,QPlaceReply::Error,QString)
    finished                 : ClassVar[Signal] = ... # finished(QPlaceReply*)
    placeAdded               : ClassVar[Signal] = ... # placeAdded(QString)
    placeRemoved             : ClassVar[Signal] = ... # placeRemoved(QString)
    placeUpdated             : ClassVar[Signal] = ... # placeUpdated(QString)
    def category(self, categoryId: str) -> PySide6.QtLocation.QPlaceCategory: ...
    def childCategories(self, parentId: str = ...) -> List[PySide6.QtLocation.QPlaceCategory]: ...
    def childCategoryIds(self, parentId: str = ...) -> List[str]: ...
    def compatiblePlace(self, place: PySide6.QtLocation.QPlace) -> PySide6.QtLocation.QPlace: ...
    def getPlaceContent(self, request: PySide6.QtLocation.QPlaceContentRequest) -> PySide6.QtLocation.QPlaceContentReply: ...
    def getPlaceDetails(self, placeId: str) -> PySide6.QtLocation.QPlaceDetailsReply: ...
    def initializeCategories(self) -> PySide6.QtLocation.QPlaceReply: ...
    def locales(self) -> List[PySide6.QtCore.QLocale]: ...
    def managerName(self) -> str: ...
    def managerVersion(self) -> int: ...
    def matchingPlaces(self, request: PySide6.QtLocation.QPlaceMatchRequest) -> PySide6.QtLocation.QPlaceMatchReply: ...
    def parentCategoryId(self, categoryId: str) -> str: ...
    def removeCategory(self, categoryId: str) -> PySide6.QtLocation.QPlaceIdReply: ...
    def removePlace(self, placeId: str) -> PySide6.QtLocation.QPlaceIdReply: ...
    def saveCategory(self, category: PySide6.QtLocation.QPlaceCategory, parentId: str = ...) -> PySide6.QtLocation.QPlaceIdReply: ...
    def savePlace(self, place: PySide6.QtLocation.QPlace) -> PySide6.QtLocation.QPlaceIdReply: ...
    def search(self, query: PySide6.QtLocation.QPlaceSearchRequest) -> PySide6.QtLocation.QPlaceSearchReply: ...
    def searchSuggestions(self, request: PySide6.QtLocation.QPlaceSearchRequest) -> PySide6.QtLocation.QPlaceSearchSuggestionReply: ...
    def setLocale(self, locale: Union[PySide6.QtCore.QLocale, PySide6.QtCore.QLocale.Language]) -> None: ...
    def setLocales(self, locale: Sequence[PySide6.QtCore.QLocale]) -> None: ...


class QPlaceManagerEngine(PySide6.QtCore.QObject):

    categoryAdded            : ClassVar[Signal] = ... # categoryAdded(QPlaceCategory,QString)
    categoryRemoved          : ClassVar[Signal] = ... # categoryRemoved(QString,QString)
    categoryUpdated          : ClassVar[Signal] = ... # categoryUpdated(QPlaceCategory,QString)
    dataChanged              : ClassVar[Signal] = ... # dataChanged()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QPlaceReply*,QPlaceReply::Error,QString)
    finished                 : ClassVar[Signal] = ... # finished(QPlaceReply*)
    placeAdded               : ClassVar[Signal] = ... # placeAdded(QString)
    placeRemoved             : ClassVar[Signal] = ... # placeRemoved(QString)
    placeUpdated             : ClassVar[Signal] = ... # placeUpdated(QString)

    def __init__(self, parameters: Dict[str, Any], parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def category(self, categoryId: str) -> PySide6.QtLocation.QPlaceCategory: ...
    def childCategories(self, parentId: str) -> List[PySide6.QtLocation.QPlaceCategory]: ...
    def childCategoryIds(self, categoryId: str) -> List[str]: ...
    def compatiblePlace(self, original: PySide6.QtLocation.QPlace) -> PySide6.QtLocation.QPlace: ...
    def constructIconUrl(self, icon: PySide6.QtLocation.QPlaceIcon, size: PySide6.QtCore.QSize) -> PySide6.QtCore.QUrl: ...
    def getPlaceContent(self, request: PySide6.QtLocation.QPlaceContentRequest) -> PySide6.QtLocation.QPlaceContentReply: ...
    def getPlaceDetails(self, placeId: str) -> PySide6.QtLocation.QPlaceDetailsReply: ...
    def initializeCategories(self) -> PySide6.QtLocation.QPlaceReply: ...
    def locales(self) -> List[PySide6.QtCore.QLocale]: ...
    def manager(self) -> PySide6.QtLocation.QPlaceManager: ...
    def managerName(self) -> str: ...
    def managerVersion(self) -> int: ...
    def matchingPlaces(self, request: PySide6.QtLocation.QPlaceMatchRequest) -> PySide6.QtLocation.QPlaceMatchReply: ...
    def parentCategoryId(self, categoryId: str) -> str: ...
    def removeCategory(self, categoryId: str) -> PySide6.QtLocation.QPlaceIdReply: ...
    def removePlace(self, placeId: str) -> PySide6.QtLocation.QPlaceIdReply: ...
    def saveCategory(self, category: PySide6.QtLocation.QPlaceCategory, parentId: str) -> PySide6.QtLocation.QPlaceIdReply: ...
    def savePlace(self, place: PySide6.QtLocation.QPlace) -> PySide6.QtLocation.QPlaceIdReply: ...
    def search(self, request: PySide6.QtLocation.QPlaceSearchRequest) -> PySide6.QtLocation.QPlaceSearchReply: ...
    def searchSuggestions(self, request: PySide6.QtLocation.QPlaceSearchRequest) -> PySide6.QtLocation.QPlaceSearchSuggestionReply: ...
    def setLocales(self, locales: Sequence[PySide6.QtCore.QLocale]) -> None: ...


class QPlaceMatchReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def places(self) -> List[PySide6.QtLocation.QPlace]: ...
    def request(self) -> PySide6.QtLocation.QPlaceMatchRequest: ...
    def setPlaces(self, results: Sequence[PySide6.QtLocation.QPlace]) -> None: ...
    def setRequest(self, request: PySide6.QtLocation.QPlaceMatchRequest) -> None: ...
    def type(self) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceMatchRequest(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceMatchRequest) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def clear(self) -> None: ...
    def parameters(self) -> Dict[str, Any]: ...
    def places(self) -> List[PySide6.QtLocation.QPlace]: ...
    def setParameters(self, parameters: Dict[str, Any]) -> None: ...
    def setPlaces(self, places: Sequence[PySide6.QtLocation.QPlace]) -> None: ...
    def setResults(self, results: Sequence[PySide6.QtLocation.QPlaceSearchResult]) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceMatchRequest) -> None: ...


class QPlaceProposedSearchResult(PySide6.QtLocation.QPlaceSearchResult):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSearchResult) -> None: ...

    def searchRequest(self) -> PySide6.QtLocation.QPlaceSearchRequest: ...
    def setSearchRequest(self, request: PySide6.QtLocation.QPlaceSearchRequest) -> None: ...


class QPlaceRatings(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceRatings) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def average(self) -> float: ...
    def count(self) -> int: ...
    def isEmpty(self) -> bool: ...
    def maximum(self) -> float: ...
    def setAverage(self, average: float) -> None: ...
    def setCount(self, count: int) -> None: ...
    def setMaximum(self, max: float) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceRatings) -> None: ...


class QPlaceReply(PySide6.QtCore.QObject):

    aborted                  : ClassVar[Signal] = ... # aborted()
    contentUpdated           : ClassVar[Signal] = ... # contentUpdated()
    errorOccurred            : ClassVar[Signal] = ... # errorOccurred(QPlaceReply::Error,QString)
    finished                 : ClassVar[Signal] = ... # finished()

    class Error(enum.Enum):

        NoError                  : QPlaceReply.Error = ... # 0x0
        PlaceDoesNotExistError   : QPlaceReply.Error = ... # 0x1
        CategoryDoesNotExistError: QPlaceReply.Error = ... # 0x2
        CommunicationError       : QPlaceReply.Error = ... # 0x3
        ParseError               : QPlaceReply.Error = ... # 0x4
        PermissionsError         : QPlaceReply.Error = ... # 0x5
        UnsupportedError         : QPlaceReply.Error = ... # 0x6
        BadArgumentError         : QPlaceReply.Error = ... # 0x7
        CancelError              : QPlaceReply.Error = ... # 0x8
        UnknownError             : QPlaceReply.Error = ... # 0x9


    class Type(enum.Enum):

        Reply                    : QPlaceReply.Type = ... # 0x0
        DetailsReply             : QPlaceReply.Type = ... # 0x1
        SearchReply              : QPlaceReply.Type = ... # 0x2
        SearchSuggestionReply    : QPlaceReply.Type = ... # 0x3
        ContentReply             : QPlaceReply.Type = ... # 0x4
        IdReply                  : QPlaceReply.Type = ... # 0x5
        MatchReply               : QPlaceReply.Type = ... # 0x6


    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def abort(self) -> None: ...
    def error(self) -> PySide6.QtLocation.QPlaceReply.Error: ...
    def errorString(self) -> str: ...
    def isFinished(self) -> bool: ...
    def setError(self, error: PySide6.QtLocation.QPlaceReply.Error, errorString: str) -> None: ...
    def setFinished(self, finished: bool) -> None: ...
    def type(self) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceResult(PySide6.QtLocation.QPlaceSearchResult):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSearchResult) -> None: ...

    def distance(self) -> float: ...
    def isSponsored(self) -> bool: ...
    def place(self) -> PySide6.QtLocation.QPlace: ...
    def setDistance(self, distance: float) -> None: ...
    def setPlace(self, place: PySide6.QtLocation.QPlace) -> None: ...
    def setSponsored(self, sponsored: bool) -> None: ...


class QPlaceSearchReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def nextPageRequest(self) -> PySide6.QtLocation.QPlaceSearchRequest: ...
    def previousPageRequest(self) -> PySide6.QtLocation.QPlaceSearchRequest: ...
    def request(self) -> PySide6.QtLocation.QPlaceSearchRequest: ...
    def results(self) -> List[PySide6.QtLocation.QPlaceSearchResult]: ...
    def setNextPageRequest(self, next: PySide6.QtLocation.QPlaceSearchRequest) -> None: ...
    def setPreviousPageRequest(self, previous: PySide6.QtLocation.QPlaceSearchRequest) -> None: ...
    def setRequest(self, request: PySide6.QtLocation.QPlaceSearchRequest) -> None: ...
    def setResults(self, results: Sequence[PySide6.QtLocation.QPlaceSearchResult]) -> None: ...
    def type(self) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceSearchRequest(Shiboken.Object):

    class RelevanceHint(enum.Enum):

        UnspecifiedHint          : QPlaceSearchRequest.RelevanceHint = ... # 0x0
        DistanceHint             : QPlaceSearchRequest.RelevanceHint = ... # 0x1
        LexicalPlaceNameHint     : QPlaceSearchRequest.RelevanceHint = ... # 0x2


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSearchRequest) -> None: ...

    def categories(self) -> List[PySide6.QtLocation.QPlaceCategory]: ...
    def clear(self) -> None: ...
    def limit(self) -> int: ...
    def recommendationId(self) -> str: ...
    def relevanceHint(self) -> PySide6.QtLocation.QPlaceSearchRequest.RelevanceHint: ...
    def searchArea(self) -> PySide6.QtPositioning.QGeoShape: ...
    def searchContext(self) -> Any: ...
    def searchTerm(self) -> str: ...
    def setCategories(self, categories: Sequence[PySide6.QtLocation.QPlaceCategory]) -> None: ...
    def setCategory(self, category: PySide6.QtLocation.QPlaceCategory) -> None: ...
    def setLimit(self, limit: int) -> None: ...
    def setRecommendationId(self, recommendationId: str) -> None: ...
    def setRelevanceHint(self, hint: PySide6.QtLocation.QPlaceSearchRequest.RelevanceHint) -> None: ...
    def setSearchArea(self, area: PySide6.QtPositioning.QGeoShape) -> None: ...
    def setSearchContext(self, context: Any) -> None: ...
    def setSearchTerm(self, term: str) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceSearchRequest) -> None: ...


class QPlaceSearchResult(Shiboken.Object):

    class SearchResultType(enum.Enum):

        UnknownSearchResult      : QPlaceSearchResult.SearchResultType = ... # 0x0
        PlaceResult              : QPlaceSearchResult.SearchResultType = ... # 0x1
        ProposedSearchResult     : QPlaceSearchResult.SearchResultType = ... # 0x2


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSearchResult) -> None: ...

    def icon(self) -> PySide6.QtLocation.QPlaceIcon: ...
    def setIcon(self, icon: PySide6.QtLocation.QPlaceIcon) -> None: ...
    def setTitle(self, title: str) -> None: ...
    def title(self) -> str: ...
    def type(self) -> PySide6.QtLocation.QPlaceSearchResult.SearchResultType: ...


class QPlaceSearchSuggestionReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def setSuggestions(self, suggestions: Sequence[str]) -> None: ...
    def suggestions(self) -> List[str]: ...
    def type(self) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceSupplier(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSupplier) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def icon(self) -> PySide6.QtLocation.QPlaceIcon: ...
    def isEmpty(self) -> bool: ...
    def name(self) -> str: ...
    def setIcon(self, icon: PySide6.QtLocation.QPlaceIcon) -> None: ...
    def setName(self, data: str) -> None: ...
    def setSupplierId(self, identifier: str) -> None: ...
    def setUrl(self, data: Union[PySide6.QtCore.QUrl, str]) -> None: ...
    def supplierId(self) -> str: ...
    def swap(self, other: PySide6.QtLocation.QPlaceSupplier) -> None: ...
    def url(self) -> PySide6.QtCore.QUrl: ...


class QPlaceUser(Shiboken.Object):

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, other: PySide6.QtLocation.QPlaceUser) -> None: ...

    @staticmethod
    def __copy__() -> None: ...
    def name(self) -> str: ...
    def setName(self, name: str) -> None: ...
    def setUserId(self, identifier: str) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceUser) -> None: ...
    def userId(self) -> str: ...


# eof
