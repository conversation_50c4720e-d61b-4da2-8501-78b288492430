<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.Qt3DCore">
    <load-typesystem name="typesystem_gui.xml" generate="no"/>
    <smart-pointer-type name="QSharedPointer" type="shared" getter="data"
                        reset-method="reset"
                        instantiations="Qt3DCore::QAspectJob=Qt3DCore::QAspectJobPtr,Qt3DCore::QBackendNodeMapper=Qt3DCore::QBackendNodeMapperPtr,Qt3DCore::QEntity=Qt3DCore::QEntityPtr"/>
    <namespace-type name="Qt3DCore" generate-using="no">
        <object-type name="QAbstractAspect"/>
        <object-type name="QAbstractFunctor"/>
        <object-type name="QAbstractSkeleton"/>
        <object-type name="QArmature"/>
        <object-type name="QAspectEngine">
          <enum-type name="RunMode"/>
          <modify-function signature="registerAspect(Qt3DCore::QAbstractAspect*)">
            <modify-argument index="this">
              <parent index="1" action="add"/>
            </modify-argument>
          </modify-function>
        </object-type>
        <object-type name="QAspectJob"/>
        <object-type name="QBackendNode">
            <enum-type name="Mode"/>
        </object-type>
        <object-type name="QBackendNodeMapper"/>
        <object-type name="QAttribute">
            <enum-type name="AttributeType"/>
            <enum-type name="VertexBaseType"/>
        </object-type>
        <!-- TODO: Solve issues related to windows and a unresolved
            external symbol
        <object-type name="QBackendNodeMapper"/>-->
        <object-type name="QBoundingVolume"/>
        <object-type name="QBuffer">
            <enum-type name="AccessType"/>
            <enum-type name="UsageType"/>
            <!-- Disambiguate from QtCore/qbuffer.h -->
            <include file-name="Qt3DCore/qbuffer.h" location="global"/>
        </object-type>
        <object-type name="QComponent"/>
        <object-type name="QEntity">
          <modify-function signature="addComponent(Qt3DCore::QComponent*)">
            <modify-argument index="this">
              <parent index="1" action="add"/>
            </modify-argument>
          </modify-function>
        </object-type>
        <object-type name="QCoreSettings"/>
        <object-type name="QGeometry"/>
        <object-type name="QGeometryView">
            <enum-type name="PrimitiveType"/>
        </object-type>
        <object-type name="QJoint">
          <modify-function signature="addChildJoint(Qt3DCore::QJoint*)">
            <modify-argument index="this">
              <parent index="1" action="add"/>
            </modify-argument>
          </modify-function>
        </object-type>
        <object-type name="QNode"/>
        <value-type name="QNodeId"/>
        <value-type name="QNodeIdTypePair"/>
        <object-type name="QSkeleton"/>
        <object-type name="QSkeletonLoader">
            <enum-type name="Status"/>
        </object-type>
        <object-type name="QTransform">
            <!-- Disambiguate from QtGui/qtransform.h -->
            <include file-name="Qt3DCore/qtransform.h" location="global"/>
        </object-type>
    </namespace-type>

    <!-- QtNetwork is pulled in via QtBluetoothDepends. -->
    <suppress-warning text="^Scoped enum 'Q(Ocsp)|(Dtls).*' does not have a type entry.*$"/>

</typesystem>
