<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtWebEngineQuick">
    <load-typesystem name="typesystem_qml.xml" generate="no"/>

    <namespace-type name="QtWebEngineQuick"/> <!-- initialize() -->

    <object-type name="QQuickWebEngineProfile">
        <enum-type name="HttpCacheType"/>
        <enum-type name="PersistentCookiesPolicy"/>
    </object-type>

</typesystem>
