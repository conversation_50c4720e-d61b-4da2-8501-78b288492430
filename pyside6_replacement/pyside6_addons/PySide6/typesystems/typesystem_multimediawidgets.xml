<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtMultimediaWidgets">
  <load-typesystem name="typesystem_core.xml" generate="no"/>
  <load-typesystem name="typesystem_gui.xml" generate="no"/>
  <load-typesystem name="typesystem_multimedia.xml" generate="no"/>
  <load-typesystem name="typesystem_widgets.xml" generate="no"/>

  <object-type name="QGraphicsVideoItem"/>
  <object-type name="QVideoWidget"/>
</typesystem>
