<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtCharts">
  <load-typesystem name="typesystem_widgets.xml" generate="no"/>
  <!-- PYSIDE-1101 Removing inherited method to avoid argument conflict
  on the QChart::scroll overload -->
  <rejection class="QGraphicsItem" function-name="scroll"/>
  <object-type name="QAbstractAxis">
      <enum-type name="AxisType"/>
  </object-type>
  <object-type name="QAbstractBarSeries">
      <enum-type name="LabelsPosition"/>
      <modify-function signature="append(QBarSet*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="append(QList&lt;QBarSet*&gt;)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="insert(int,QBarSet*)">
        <modify-argument index="2">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="take(QBarSet*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
  </object-type>
  <object-type name="QAbstractSeries">
      <enum-type name="SeriesType"/>
  </object-type>
  <object-type name="QAreaLegendMarker"/>
  <object-type name="QAreaSeries">
    <modify-function signature="setUpperSeries(QLineSeries*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setLowerSeries(QLineSeries*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QBarCategoryAxis"/>
  <object-type name="QBarLegendMarker"/>
  <object-type name="QBarModelMapper"/>
  <object-type name="QBarSeries"/>
  <object-type name="QBarSet"/>
  <object-type name="QBoxPlotLegendMarker"/>
  <object-type name="QBoxPlotModelMapper"/>
  <object-type name="QBoxPlotSeries">
      <modify-function signature="append(QBoxSet*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="append(QList&lt;QBoxSet*&gt;)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="insert(int,QBoxSet*)">
        <modify-argument index="2">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="take(QBoxSet*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
  </object-type>
  <object-type name="QBoxSet">
      <enum-type name="ValuePositions" python-type="IntEnum"/>
  </object-type>
  <object-type name="QCandlestickLegendMarker"/>
  <object-type name="QCandlestickModelMapper"/>
  <object-type name="QCandlestickSeries">
      <modify-function signature="append(QCandlestickSet*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="append(QList&lt;QCandlestickSet*&gt;)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="insert(int,QCandlestickSet*)">
        <modify-argument index="2">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="take(QCandlestickSet*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
  </object-type>
  <object-type name="QCandlestickSet"/>
  <object-type name="QCategoryAxis">
      <enum-type name="AxisLabelsPosition"/>
  </object-type>
  <object-type name="QChart">
      <enum-type name="ChartType"/>
      <enum-type name="ChartTheme"/>
      <enum-type name="AnimationOption" flags="AnimationOptions"/>
      <modify-function signature="addAxis(QAbstractAxis*,QFlags&lt;Qt::AlignmentFlag&gt;)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="addSeries(QAbstractSeries*)">
          <modify-argument index="1">
               <parent index="this" action="add"/>
          </modify-argument>
      </modify-function>
      <modify-function signature="setAxisX(QAbstractAxis*,QAbstractSeries*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
        <modify-argument index="2">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="setAxisY(QAbstractAxis*,QAbstractSeries*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
        <modify-argument index="2">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="removeAxis(QAbstractAxis*)">
        <inject-code file="../glue/qtcharts.cpp" snippet="qchart-releaseownership"/>
      </modify-function>
      <modify-function signature="removeSeries(QAbstractSeries*)">
        <inject-code file="../glue/qtcharts.cpp" snippet="qchart-releaseownership"/>
      </modify-function>
  </object-type>
  <object-type name="QChartView">
      <enum-type name="RubberBand" flags="RubberBands"/>
      <modify-function signature="QChartView(QChart*,QWidget*)">
          <modify-argument index="1">
               <parent index="this" action="add"/>
          </modify-argument>
      </modify-function>
      <modify-function signature="setChart(QChart*)">
          <modify-argument index="1">
               <parent index="this" action="add"/>
          </modify-argument>
      </modify-function>
  </object-type>
  <object-type name="QDateTimeAxis"/>
  <object-type name="QHBarModelMapper"/>
  <object-type name="QHBoxPlotModelMapper"/>
  <object-type name="QHCandlestickModelMapper"/>
  <object-type name="QHorizontalBarSeries"/>
  <object-type name="QHorizontalPercentBarSeries"/>
  <object-type name="QHorizontalStackedBarSeries"/>
  <object-type name="QHPieModelMapper"/>
  <object-type name="QHXYModelMapper"/>
  <object-type name="QLegend">
      <enum-type name="MarkerShape"/>
  </object-type>
  <object-type name="QLegendMarker">
      <enum-type name="LegendMarkerType"/>
  </object-type>
  <object-type name="QLineSeries"/>
  <object-type name="QLogValueAxis"/>
  <object-type name="QPercentBarSeries"/>
  <object-type name="QPieLegendMarker"/>
  <object-type name="QPieModelMapper"/>
  <object-type name="QPieSlice">
      <enum-type name="LabelPosition"/>
  </object-type>
  <object-type name="QPieSeries">
      <modify-function signature="append(QPieSlice*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="append(QList&lt;QPieSlice*&gt;)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="insert(int,QPieSlice*)">
        <modify-argument index="2">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="take(QPieSlice*)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
  </object-type>
  <object-type name="QPolarChart">
      <enum-type name="PolarOrientation" flags="PolarOrientations"/>
      <modify-function signature="addAxis(QAbstractAxis*,QPolarChart::PolarOrientation)">
        <modify-argument index="1">
          <parent index="this" action="add"/>
        </modify-argument>
      </modify-function>
      <!-- Compile error assigning default flags value -->
      <modify-function signature="axes(QFlags&lt;QPolarChart::PolarOrientation&gt;,QAbstractSeries*)const" remove="all"/>
  </object-type>
  <object-type name="QScatterSeries">
      <enum-type name="MarkerShape"/>
  </object-type>
  <object-type name="QSplineSeries"/>
  <object-type name="QStackedBarSeries"/>
  <object-type name="QValueAxis">
      <enum-type name="TickType"/>
  </object-type>
  <object-type name="QVBarModelMapper"/>
  <object-type name="QVBoxPlotModelMapper"/>
  <object-type name="QVCandlestickModelMapper"/>
  <object-type name="QVPieModelMapper"/>
  <object-type name="QVXYModelMapper"/>
  <object-type name="QXYLegendMarker"/>
  <object-type name="QXYModelMapper"/>
  <!-- Add numpy versions as separate functions since passing ndarrays to other
       typecheck macros causes:
       FIXME Subscripted generics cannot be used with class and instance checks -->
  <object-type name="QXYSeries">
      <enum-type name="PointConfiguration"/>
      <include file-name="pyside_numpy.h" location="global"/>
      <add-function signature="appendNp(PyArrayObject *@x@, PyArrayObject *@y@)">
          <inject-code file="../glue/qtcharts.cpp" snippet="qxyseries-appendnp-numpy-x-y"/>
          <inject-documentation format="target" mode="append">
          Adds the list of data points specified by two
          one-dimensional, equally sized numpy arrays representing the x, y values, respectively.
          </inject-documentation>
      </add-function>
      <add-function signature="replaceNp(PyArrayObject *@x@, PyArrayObject *@y@)">
          <inject-code file="../glue/qtcharts.cpp" snippet="qxyseries-replacenp-numpy-x-y"/>
          <inject-documentation format="target" mode="append">
          Replaces the current points with the points specified by two
          one-dimensional, equally sized numpy arrays representing the x, y values, respectively.
          </inject-documentation>
      </add-function>
  </object-type>
</typesystem>
