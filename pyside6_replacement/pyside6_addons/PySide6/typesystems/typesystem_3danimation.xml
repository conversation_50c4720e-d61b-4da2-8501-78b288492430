<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.Qt3DAnimation">
    <load-typesystem name="typesystem_3drender.xml" generate="no"/>
    <namespace-type name="Qt3DAnimation">
        <object-type name="QAbstractAnimation">
            <enum-type name="AnimationType"/>
            <!-- Disambiguate from QtCore/qabstractanimation.h -->
            <include file-name="Qt3DAnimation/qabstractanimation.h" location="global"/>
        </object-type>
        <object-type name="QAbstractAnimationClip"/>
        <value-type name="QAnimationClipData" since="6.1"/>
        <object-type name="QAbstractChannelMapping"/>
        <object-type name="QAbstractClipAnimator">
            <enum-type name="Loops"/>
        </object-type>
        <object-type name="QAbstractClipBlendNode"/>
        <object-type name="QAdditiveClipBlend"/>
        <object-type name="QAnimationAspect"/>
        <object-type name="QAnimationCallback">
            <enum-type name="Flag"/>
        </object-type>
        <object-type name="QAnimationClip"/>
        <object-type name="QAnimationClipLoader">
            <enum-type name="Status"/>
        </object-type>
        <object-type name="QAnimationController"/>
        <object-type name="QAnimationGroup">
            <!-- Disambiguate from QtCore/qanimationgroup.h -->
            <include file-name="Qt3DAnimation/qanimationgroup.h" location="global"/>
        </object-type>
        <object-type name="QBlendedClipAnimator"/>
        <value-type name="QChannel"/>
        <value-type name="QChannelComponent"/>
        <object-type name="QChannelMapper" since="6.1"/>
        <object-type name="QChannelMapping"/>
        <object-type name="QClipAnimator"/>
        <object-type name="QClipBlendValue"/>
        <object-type name="QClock"/>
        <object-type name="QKeyFrame">
            <enum-type name="InterpolationType"/>
        </object-type>
        <object-type name="QKeyframeAnimation">
            <enum-type name="RepeatMode"/>
        </object-type>
        <object-type name="QLerpClipBlend"/>
        <object-type name="QMorphingAnimation">
            <enum-type name="Method"/>
        </object-type>
        <object-type name="QMorphTarget"/>
        <object-type name="QSkeletonMapping"/>
        <object-type name="QVertexBlendAnimation"/>
    </namespace-type>
</typesystem>
