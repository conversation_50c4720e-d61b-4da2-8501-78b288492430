# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.QtSpatialAudio, except for defaults which are replaced by "...".
"""

# Module `PySide6.QtSpatialAudio`

import PySide6.QtSpatialAudio
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtMultimedia

import enum
from typing import ClassVar, Optional, Union, overload
from PySide6.QtCore import Signal


NoneType = type(None)


class QAmbientSound(PySide6.QtCore.QObject):

    autoPlayChanged          : ClassVar[Signal] = ... # autoPlayChanged()
    loopsChanged             : ClassVar[Signal] = ... # loopsChanged()
    sourceChanged            : ClassVar[Signal] = ... # sourceChanged()
    volumeChanged            : ClassVar[Signal] = ... # volumeChanged()

    class Loops(enum.Enum):

        Infinite                 : QAmbientSound.Loops = ... # -0x1
        Once                     : QAmbientSound.Loops = ... # 0x1


    def __init__(self, engine: PySide6.QtSpatialAudio.QAudioEngine) -> None: ...

    def autoPlay(self) -> bool: ...
    def engine(self) -> PySide6.QtSpatialAudio.QAudioEngine: ...
    def loops(self) -> int: ...
    def pause(self) -> None: ...
    def play(self) -> None: ...
    def setAutoPlay(self, autoPlay: bool) -> None: ...
    def setLoops(self, loops: int) -> None: ...
    def setSource(self, url: Union[PySide6.QtCore.QUrl, str]) -> None: ...
    def setVolume(self, volume: float) -> None: ...
    def source(self) -> PySide6.QtCore.QUrl: ...
    def stop(self) -> None: ...
    def volume(self) -> float: ...


class QAudioEngine(PySide6.QtCore.QObject):

    distanceScaleChanged     : ClassVar[Signal] = ... # distanceScaleChanged()
    masterVolumeChanged      : ClassVar[Signal] = ... # masterVolumeChanged()
    outputDeviceChanged      : ClassVar[Signal] = ... # outputDeviceChanged()
    outputModeChanged        : ClassVar[Signal] = ... # outputModeChanged()
    pausedChanged            : ClassVar[Signal] = ... # pausedChanged()

    class OutputMode(enum.Enum):

        Surround                 : QAudioEngine.OutputMode = ... # 0x0
        Stereo                   : QAudioEngine.OutputMode = ... # 0x1
        Headphone                : QAudioEngine.OutputMode = ... # 0x2


    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, parent: PySide6.QtCore.QObject) -> None: ...
    @overload
    def __init__(self, sampleRate: int, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

    def distanceScale(self) -> float: ...
    def masterVolume(self) -> float: ...
    def outputDevice(self) -> PySide6.QtMultimedia.QAudioDevice: ...
    def outputMode(self) -> PySide6.QtSpatialAudio.QAudioEngine.OutputMode: ...
    def pause(self) -> None: ...
    def paused(self) -> bool: ...
    def resume(self) -> None: ...
    def roomEffectsEnabled(self) -> bool: ...
    def sampleRate(self) -> int: ...
    def setDistanceScale(self, scale: float) -> None: ...
    def setMasterVolume(self, volume: float) -> None: ...
    def setOutputDevice(self, device: PySide6.QtMultimedia.QAudioDevice) -> None: ...
    def setOutputMode(self, mode: PySide6.QtSpatialAudio.QAudioEngine.OutputMode) -> None: ...
    def setPaused(self, paused: bool) -> None: ...
    def setRoomEffectsEnabled(self, enabled: bool) -> None: ...
    def start(self) -> None: ...
    def stop(self) -> None: ...


class QAudioListener(PySide6.QtCore.QObject):

    destroyed                : ClassVar[Signal] = ... # destroyed()
    objectNameChanged        : ClassVar[Signal] = ... # objectNameChanged(QString)

    def __init__(self, engine: PySide6.QtSpatialAudio.QAudioEngine) -> None: ...

    def engine(self) -> PySide6.QtSpatialAudio.QAudioEngine: ...
    def position(self) -> PySide6.QtGui.QVector3D: ...
    def rotation(self) -> PySide6.QtGui.QQuaternion: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D) -> None: ...
    def setRotation(self, q: PySide6.QtGui.QQuaternion) -> None: ...


class QAudioRoom(PySide6.QtCore.QObject):

    dimensionsChanged        : ClassVar[Signal] = ... # dimensionsChanged()
    positionChanged          : ClassVar[Signal] = ... # positionChanged()
    reflectionGainChanged    : ClassVar[Signal] = ... # reflectionGainChanged()
    reverbBrightnessChanged  : ClassVar[Signal] = ... # reverbBrightnessChanged()
    reverbGainChanged        : ClassVar[Signal] = ... # reverbGainChanged()
    reverbTimeChanged        : ClassVar[Signal] = ... # reverbTimeChanged()
    rotationChanged          : ClassVar[Signal] = ... # rotationChanged()
    wallsChanged             : ClassVar[Signal] = ... # wallsChanged()

    class Material(enum.Enum):

        Transparent              : QAudioRoom.Material = ... # 0x0
        AcousticCeilingTiles     : QAudioRoom.Material = ... # 0x1
        BrickBare                : QAudioRoom.Material = ... # 0x2
        BrickPainted             : QAudioRoom.Material = ... # 0x3
        ConcreteBlockCoarse      : QAudioRoom.Material = ... # 0x4
        ConcreteBlockPainted     : QAudioRoom.Material = ... # 0x5
        CurtainHeavy             : QAudioRoom.Material = ... # 0x6
        FiberGlassInsulation     : QAudioRoom.Material = ... # 0x7
        GlassThin                : QAudioRoom.Material = ... # 0x8
        GlassThick               : QAudioRoom.Material = ... # 0x9
        Grass                    : QAudioRoom.Material = ... # 0xa
        LinoleumOnConcrete       : QAudioRoom.Material = ... # 0xb
        Marble                   : QAudioRoom.Material = ... # 0xc
        Metal                    : QAudioRoom.Material = ... # 0xd
        ParquetOnConcrete        : QAudioRoom.Material = ... # 0xe
        PlasterRough             : QAudioRoom.Material = ... # 0xf
        PlasterSmooth            : QAudioRoom.Material = ... # 0x10
        PlywoodPanel             : QAudioRoom.Material = ... # 0x11
        PolishedConcreteOrTile   : QAudioRoom.Material = ... # 0x12
        Sheetrock                : QAudioRoom.Material = ... # 0x13
        WaterOrIceSurface        : QAudioRoom.Material = ... # 0x14
        WoodCeiling              : QAudioRoom.Material = ... # 0x15
        WoodPanel                : QAudioRoom.Material = ... # 0x16
        UniformMaterial          : QAudioRoom.Material = ... # 0x17


    class Wall(enum.Enum):

        LeftWall                 : QAudioRoom.Wall = ... # 0x0
        RightWall                : QAudioRoom.Wall = ... # 0x1
        Floor                    : QAudioRoom.Wall = ... # 0x2
        Ceiling                  : QAudioRoom.Wall = ... # 0x3
        FrontWall                : QAudioRoom.Wall = ... # 0x4
        BackWall                 : QAudioRoom.Wall = ... # 0x5


    def __init__(self, engine: PySide6.QtSpatialAudio.QAudioEngine) -> None: ...

    def dimensions(self) -> PySide6.QtGui.QVector3D: ...
    def position(self) -> PySide6.QtGui.QVector3D: ...
    def reflectionGain(self) -> float: ...
    def reverbBrightness(self) -> float: ...
    def reverbGain(self) -> float: ...
    def reverbTime(self) -> float: ...
    def rotation(self) -> PySide6.QtGui.QQuaternion: ...
    def setDimensions(self, dim: PySide6.QtGui.QVector3D) -> None: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D) -> None: ...
    def setReflectionGain(self, factor: float) -> None: ...
    def setReverbBrightness(self, factor: float) -> None: ...
    def setReverbGain(self, factor: float) -> None: ...
    def setReverbTime(self, factor: float) -> None: ...
    def setRotation(self, q: PySide6.QtGui.QQuaternion) -> None: ...
    def setWallMaterial(self, wall: PySide6.QtSpatialAudio.QAudioRoom.Wall, material: PySide6.QtSpatialAudio.QAudioRoom.Material) -> None: ...
    def wallMaterial(self, wall: PySide6.QtSpatialAudio.QAudioRoom.Wall) -> PySide6.QtSpatialAudio.QAudioRoom.Material: ...


class QIntList(object): ...


class QSpatialSound(PySide6.QtCore.QObject):

    autoPlayChanged          : ClassVar[Signal] = ... # autoPlayChanged()
    directivityChanged       : ClassVar[Signal] = ... # directivityChanged()
    directivityOrderChanged  : ClassVar[Signal] = ... # directivityOrderChanged()
    distanceCutoffChanged    : ClassVar[Signal] = ... # distanceCutoffChanged()
    distanceModelChanged     : ClassVar[Signal] = ... # distanceModelChanged()
    loopsChanged             : ClassVar[Signal] = ... # loopsChanged()
    manualAttenuationChanged : ClassVar[Signal] = ... # manualAttenuationChanged()
    nearFieldGainChanged     : ClassVar[Signal] = ... # nearFieldGainChanged()
    occlusionIntensityChanged: ClassVar[Signal] = ... # occlusionIntensityChanged()
    positionChanged          : ClassVar[Signal] = ... # positionChanged()
    rotationChanged          : ClassVar[Signal] = ... # rotationChanged()
    sizeChanged              : ClassVar[Signal] = ... # sizeChanged()
    sourceChanged            : ClassVar[Signal] = ... # sourceChanged()
    volumeChanged            : ClassVar[Signal] = ... # volumeChanged()

    class DistanceModel(enum.Enum):

        Logarithmic              : QSpatialSound.DistanceModel = ... # 0x0
        Linear                   : QSpatialSound.DistanceModel = ... # 0x1
        ManualAttenuation        : QSpatialSound.DistanceModel = ... # 0x2


    class Loops(enum.Enum):

        Infinite                 : QSpatialSound.Loops = ... # -0x1
        Once                     : QSpatialSound.Loops = ... # 0x1


    def __init__(self, engine: PySide6.QtSpatialAudio.QAudioEngine) -> None: ...

    def autoPlay(self) -> bool: ...
    def directivity(self) -> float: ...
    def directivityOrder(self) -> float: ...
    def distanceCutoff(self) -> float: ...
    def distanceModel(self) -> PySide6.QtSpatialAudio.QSpatialSound.DistanceModel: ...
    def engine(self) -> PySide6.QtSpatialAudio.QAudioEngine: ...
    def loops(self) -> int: ...
    def manualAttenuation(self) -> float: ...
    def nearFieldGain(self) -> float: ...
    def occlusionIntensity(self) -> float: ...
    def pause(self) -> None: ...
    def play(self) -> None: ...
    def position(self) -> PySide6.QtGui.QVector3D: ...
    def rotation(self) -> PySide6.QtGui.QQuaternion: ...
    def setAutoPlay(self, autoPlay: bool) -> None: ...
    def setDirectivity(self, alpha: float) -> None: ...
    def setDirectivityOrder(self, alpha: float) -> None: ...
    def setDistanceCutoff(self, cutoff: float) -> None: ...
    def setDistanceModel(self, model: PySide6.QtSpatialAudio.QSpatialSound.DistanceModel) -> None: ...
    def setLoops(self, loops: int) -> None: ...
    def setManualAttenuation(self, attenuation: float) -> None: ...
    def setNearFieldGain(self, gain: float) -> None: ...
    def setOcclusionIntensity(self, occlusion: float) -> None: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D) -> None: ...
    def setRotation(self, q: PySide6.QtGui.QQuaternion) -> None: ...
    def setSize(self, size: float) -> None: ...
    def setSource(self, url: Union[PySide6.QtCore.QUrl, str]) -> None: ...
    def setVolume(self, volume: float) -> None: ...
    def size(self) -> float: ...
    def source(self) -> PySide6.QtCore.QUrl: ...
    def stop(self) -> None: ...
    def volume(self) -> float: ...


# eof
