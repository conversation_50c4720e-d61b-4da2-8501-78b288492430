# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.Qt3DAnimation, except for defaults which are replaced by "...".
"""

# Module `PySide6.Qt3DAnimation`

import PySide6.Qt3DAnimation
import PySide6.QtCore
import PySide6.QtGui
import PySide6.Qt3DCore
import PySide6.Qt3DRender

import enum
from typing import Any, ClassVar, List, Optional, Sequence, Type, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QIntList(object): ...


class Qt3DAnimation(Shiboken.Object):

    class QAbstractAnimation(PySide6.QtCore.QObject):

        animationNameChanged     : ClassVar[Signal] = ... # animationNameChanged(QString)
        durationChanged          : ClassVar[Signal] = ... # durationChanged(float)
        positionChanged          : ClassVar[Signal] = ... # positionChanged(float)

        class AnimationType(enum.Enum):

            KeyframeAnimation        : Qt3DAnimation.QAbstractAnimation.AnimationType = ... # 0x1
            MorphingAnimation        : Qt3DAnimation.QAbstractAnimation.AnimationType = ... # 0x2
            VertexBlendAnimation     : Qt3DAnimation.QAbstractAnimation.AnimationType = ... # 0x3


        def animationName(self) -> str: ...
        def animationType(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimation.AnimationType: ...
        def duration(self) -> float: ...
        def position(self) -> float: ...
        def setAnimationName(self, name: str) -> None: ...
        def setDuration(self, duration: float) -> None: ...
        def setPosition(self, position: float) -> None: ...

    class QAbstractAnimationClip(PySide6.Qt3DCore.Qt3DCore.QNode):

        durationChanged          : ClassVar[Signal] = ... # durationChanged(float)
        def duration(self) -> float: ...

    class QAbstractChannelMapping(PySide6.Qt3DCore.Qt3DCore.QNode): ...

    class QAbstractClipAnimator(PySide6.Qt3DCore.Qt3DCore.QComponent):

        channelMapperChanged     : ClassVar[Signal] = ... # channelMapperChanged(Qt3DAnimation::QChannelMapper*)
        clockChanged             : ClassVar[Signal] = ... # clockChanged(Qt3DAnimation::QClock*)
        loopCountChanged         : ClassVar[Signal] = ... # loopCountChanged(int)
        normalizedTimeChanged    : ClassVar[Signal] = ... # normalizedTimeChanged(float)
        runningChanged           : ClassVar[Signal] = ... # runningChanged(bool)

        class Loops(enum.Enum):

            Infinite                 : Qt3DAnimation.QAbstractClipAnimator.Loops = ... # -0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def channelMapper(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QChannelMapper: ...
        def clock(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QClock: ...
        def isRunning(self) -> bool: ...
        def loopCount(self) -> int: ...
        def normalizedTime(self) -> float: ...
        def setChannelMapper(self, channelMapper: PySide6.Qt3DAnimation.Qt3DAnimation.QChannelMapper) -> None: ...
        def setClock(self, clock: PySide6.Qt3DAnimation.Qt3DAnimation.QClock) -> None: ...
        def setLoopCount(self, loops: int) -> None: ...
        def setNormalizedTime(self, timeFraction: float) -> None: ...
        def setRunning(self, running: bool) -> None: ...
        def start(self) -> None: ...
        def stop(self) -> None: ...

    class QAbstractClipBlendNode(PySide6.Qt3DCore.Qt3DCore.QNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QAdditiveClipBlend(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode):

        additiveClipChanged      : ClassVar[Signal] = ... # additiveClipChanged(Qt3DAnimation::QAbstractClipBlendNode*)
        additiveFactorChanged    : ClassVar[Signal] = ... # additiveFactorChanged(float)
        baseClipChanged          : ClassVar[Signal] = ... # baseClipChanged(Qt3DAnimation::QAbstractClipBlendNode*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def additiveClip(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode: ...
        def additiveFactor(self) -> float: ...
        def baseClip(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode: ...
        def setAdditiveClip(self, additiveClip: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode) -> None: ...
        def setAdditiveFactor(self, additiveFactor: float) -> None: ...
        def setBaseClip(self, baseClip: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode) -> None: ...

    class QAnimationAspect(PySide6.Qt3DCore.Qt3DCore.QAbstractAspect):

        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...


    class QAnimationCallback(Shiboken.Object):

        class Flag(enum.Enum):

            OnOwningThread           : Qt3DAnimation.QAnimationCallback.Flag = ... # 0x0
            OnThreadPool             : Qt3DAnimation.QAnimationCallback.Flag = ... # 0x1


        def __init__(self) -> None: ...

        def valueChanged(self, value: Any) -> None: ...

    class QAnimationClip(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimationClip):

        clipDataChanged          : ClassVar[Signal] = ... # clipDataChanged(Qt3DAnimation::QAnimationClipData)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def clipData(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAnimationClipData: ...
        def setClipData(self, clipData: PySide6.Qt3DAnimation.Qt3DAnimation.QAnimationClipData) -> None: ...

    class QAnimationClipData(Shiboken.Object):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, arg__1: PySide6.Qt3DAnimation.Qt3DAnimation.QAnimationClipData) -> None: ...

        @staticmethod
        def __copy__() -> None: ...
        def appendChannel(self, c: PySide6.Qt3DAnimation.Qt3DAnimation.QChannel) -> None: ...
        def begin(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QChannel: ...
        def cbegin(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QChannel: ...
        def cend(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QChannel: ...
        def channelCount(self) -> int: ...
        def clearChannels(self) -> None: ...
        def end(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QChannel: ...
        def insertChannel(self, index: int, c: PySide6.Qt3DAnimation.Qt3DAnimation.QChannel) -> None: ...
        def isValid(self) -> bool: ...
        def name(self) -> str: ...
        def removeChannel(self, index: int) -> None: ...
        def setName(self, name: str) -> None: ...

    class QAnimationClipLoader(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimationClip):

        sourceChanged            : ClassVar[Signal] = ... # sourceChanged(QUrl)
        statusChanged            : ClassVar[Signal] = ... # statusChanged(Status)

        class Status(enum.Enum):

            NotReady                 : Qt3DAnimation.QAnimationClipLoader.Status = ... # 0x0
            Ready                    : Qt3DAnimation.QAnimationClipLoader.Status = ... # 0x1
            Error                    : Qt3DAnimation.QAnimationClipLoader.Status = ... # 0x2


        @overload
        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...
        @overload
        def __init__(self, source: Union[PySide6.QtCore.QUrl, str], parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setSource(self, source: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def source(self) -> PySide6.QtCore.QUrl: ...
        def status(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAnimationClipLoader.Status: ...

    class QAnimationController(PySide6.QtCore.QObject):

        activeAnimationGroupChanged: ClassVar[Signal] = ... # activeAnimationGroupChanged(int)
        entityChanged            : ClassVar[Signal] = ... # entityChanged(Qt3DCore::QEntity*)
        positionChanged          : ClassVar[Signal] = ... # positionChanged(float)
        positionOffsetChanged    : ClassVar[Signal] = ... # positionOffsetChanged(float)
        positionScaleChanged     : ClassVar[Signal] = ... # positionScaleChanged(float)
        recursiveChanged         : ClassVar[Signal] = ... # recursiveChanged(bool)

        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def activeAnimationGroup(self) -> int: ...
        def addAnimationGroup(self, animationGroups: PySide6.Qt3DAnimation.Qt3DAnimation.QAnimationGroup) -> None: ...
        def animationGroupList(self) -> List[PySide6.Qt3DAnimation.Qt3DAnimation.QAnimationGroup]: ...
        def entity(self) -> PySide6.Qt3DCore.Qt3DCore.QEntity: ...
        def getAnimationIndex(self, name: str) -> int: ...
        def getGroup(self, index: int) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAnimationGroup: ...
        def position(self) -> float: ...
        def positionOffset(self) -> float: ...
        def positionScale(self) -> float: ...
        def recursive(self) -> bool: ...
        def removeAnimationGroup(self, animationGroups: PySide6.Qt3DAnimation.Qt3DAnimation.QAnimationGroup) -> None: ...
        def setActiveAnimationGroup(self, index: int) -> None: ...
        def setAnimationGroups(self, animationGroups: Sequence[PySide6.Qt3DAnimation.Qt3DAnimation.QAnimationGroup]) -> None: ...
        def setEntity(self, entity: PySide6.Qt3DCore.Qt3DCore.QEntity) -> None: ...
        def setPosition(self, position: float) -> None: ...
        def setPositionOffset(self, offset: float) -> None: ...
        def setPositionScale(self, scale: float) -> None: ...
        def setRecursive(self, recursive: bool) -> None: ...

    class QAnimationGroup(PySide6.QtCore.QObject):

        durationChanged          : ClassVar[Signal] = ... # durationChanged(float)
        nameChanged              : ClassVar[Signal] = ... # nameChanged(QString)
        positionChanged          : ClassVar[Signal] = ... # positionChanged(float)

        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def addAnimation(self, animation: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimation) -> None: ...
        def animationList(self) -> List[PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimation]: ...
        def duration(self) -> float: ...
        def name(self) -> str: ...
        def position(self) -> float: ...
        def removeAnimation(self, animation: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimation) -> None: ...
        def setAnimations(self, animations: Sequence[PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimation]) -> None: ...
        def setName(self, name: str) -> None: ...
        def setPosition(self, position: float) -> None: ...

    class QBlendedClipAnimator(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipAnimator):

        blendTreeChanged         : ClassVar[Signal] = ... # blendTreeChanged(QAbstractClipBlendNode*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def blendTree(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode: ...
        def setBlendTree(self, blendTree: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode) -> None: ...

    class QChannel(Shiboken.Object):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, arg__1: PySide6.Qt3DAnimation.Qt3DAnimation.QChannel) -> None: ...
        @overload
        def __init__(self, name: str) -> None: ...

        @staticmethod
        def __copy__() -> None: ...
        def appendChannelComponent(self, component: PySide6.Qt3DAnimation.Qt3DAnimation.QChannelComponent) -> None: ...
        def begin(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QChannelComponent: ...
        def cbegin(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QChannelComponent: ...
        def cend(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QChannelComponent: ...
        def channelComponentCount(self) -> int: ...
        def clearChannelComponents(self) -> None: ...
        def end(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QChannelComponent: ...
        def insertChannelComponent(self, index: int, component: PySide6.Qt3DAnimation.Qt3DAnimation.QChannelComponent) -> None: ...
        def jointIndex(self) -> int: ...
        def name(self) -> str: ...
        def removeChannelComponent(self, index: int) -> None: ...
        def setJointIndex(self, jointIndex: int) -> None: ...
        def setName(self, name: str) -> None: ...

    class QChannelComponent(Shiboken.Object):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, arg__1: PySide6.Qt3DAnimation.Qt3DAnimation.QChannelComponent) -> None: ...
        @overload
        def __init__(self, name: str) -> None: ...

        @staticmethod
        def __copy__() -> None: ...
        def appendKeyFrame(self, kf: PySide6.Qt3DAnimation.Qt3DAnimation.QKeyFrame) -> None: ...
        def begin(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QKeyFrame: ...
        def cbegin(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QKeyFrame: ...
        def cend(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QKeyFrame: ...
        def clearKeyFrames(self) -> None: ...
        def end(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QKeyFrame: ...
        def insertKeyFrame(self, index: int, kf: PySide6.Qt3DAnimation.Qt3DAnimation.QKeyFrame) -> None: ...
        def keyFrameCount(self) -> int: ...
        def name(self) -> str: ...
        def removeKeyFrame(self, index: int) -> None: ...
        def setName(self, name: str) -> None: ...

    class QChannelMapper(PySide6.Qt3DCore.Qt3DCore.QNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addMapping(self, mapping: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractChannelMapping) -> None: ...
        def mappings(self) -> List[PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractChannelMapping]: ...
        def removeMapping(self, mapping: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractChannelMapping) -> None: ...

    class QChannelMapping(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractChannelMapping):

        channelNameChanged       : ClassVar[Signal] = ... # channelNameChanged(QString)
        propertyChanged          : ClassVar[Signal] = ... # propertyChanged(QString)
        targetChanged            : ClassVar[Signal] = ... # targetChanged(Qt3DCore::QNode*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def channelName(self) -> str: ...
        def property(self) -> str: ...
        def setChannelName(self, channelName: str) -> None: ...
        def setProperty(self, property: str) -> None: ...
        def setTarget(self, target: PySide6.Qt3DCore.Qt3DCore.QNode) -> None: ...
        def target(self) -> PySide6.Qt3DCore.Qt3DCore.QNode: ...

    class QClipAnimator(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipAnimator):

        clipChanged              : ClassVar[Signal] = ... # clipChanged(Qt3DAnimation::QAbstractAnimationClip*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def clip(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimationClip: ...
        def setClip(self, clip: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimationClip) -> None: ...

    class QClipBlendValue(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode):

        clipChanged              : ClassVar[Signal] = ... # clipChanged(Qt3DAnimation::QAbstractAnimationClip*)

        @overload
        def __init__(self, clip: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimationClip, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...
        @overload
        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def clip(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimationClip: ...
        def setClip(self, clip: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimationClip) -> None: ...

    class QClock(PySide6.Qt3DCore.Qt3DCore.QNode):

        playbackRateChanged      : ClassVar[Signal] = ... # playbackRateChanged(double)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def playbackRate(self) -> float: ...
        def setPlaybackRate(self, playbackRate: float) -> None: ...

    class QKeyFrame(Shiboken.Object):

        class InterpolationType(enum.Enum):

            ConstantInterpolation    : Qt3DAnimation.QKeyFrame.InterpolationType = ... # 0x0
            LinearInterpolation      : Qt3DAnimation.QKeyFrame.InterpolationType = ... # 0x1
            BezierInterpolation      : Qt3DAnimation.QKeyFrame.InterpolationType = ... # 0x2


        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, coords: PySide6.QtGui.QVector2D) -> None: ...
        @overload
        def __init__(self, coords: PySide6.QtGui.QVector2D, lh: PySide6.QtGui.QVector2D, rh: PySide6.QtGui.QVector2D) -> None: ...

        def coordinates(self) -> PySide6.QtGui.QVector2D: ...
        def interpolationType(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QKeyFrame.InterpolationType: ...
        def leftControlPoint(self) -> PySide6.QtGui.QVector2D: ...
        def rightControlPoint(self) -> PySide6.QtGui.QVector2D: ...
        def setCoordinates(self, coords: PySide6.QtGui.QVector2D) -> None: ...
        def setInterpolationType(self, interp: PySide6.Qt3DAnimation.Qt3DAnimation.QKeyFrame.InterpolationType) -> None: ...
        def setLeftControlPoint(self, lh: PySide6.QtGui.QVector2D) -> None: ...
        def setRightControlPoint(self, rh: PySide6.QtGui.QVector2D) -> None: ...

    class QKeyframeAnimation(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimation):

        easingChanged            : ClassVar[Signal] = ... # easingChanged(QEasingCurve)
        endModeChanged           : ClassVar[Signal] = ... # endModeChanged(QKeyframeAnimation::RepeatMode)
        framePositionsChanged    : ClassVar[Signal] = ... # framePositionsChanged(QList<float>)
        startModeChanged         : ClassVar[Signal] = ... # startModeChanged(QKeyframeAnimation::RepeatMode)
        targetChanged            : ClassVar[Signal] = ... # targetChanged(Qt3DCore::QTransform*)
        targetNameChanged        : ClassVar[Signal] = ... # targetNameChanged(QString)

        class RepeatMode(enum.Enum):

            None_                    : Qt3DAnimation.QKeyframeAnimation.RepeatMode = ... # 0x0
            Constant                 : Qt3DAnimation.QKeyframeAnimation.RepeatMode = ... # 0x1
            Repeat                   : Qt3DAnimation.QKeyframeAnimation.RepeatMode = ... # 0x2


        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def addKeyframe(self, keyframe: PySide6.Qt3DCore.Qt3DCore.QTransform) -> None: ...
        def easing(self) -> PySide6.QtCore.QEasingCurve: ...
        def endMode(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QKeyframeAnimation.RepeatMode: ...
        def framePositions(self) -> List[float]: ...
        def keyframeList(self) -> List[PySide6.Qt3DCore.Qt3DCore.QTransform]: ...
        def removeKeyframe(self, keyframe: PySide6.Qt3DCore.Qt3DCore.QTransform) -> None: ...
        def setEasing(self, easing: Union[PySide6.QtCore.QEasingCurve, PySide6.QtCore.QEasingCurve.Type]) -> None: ...
        def setEndMode(self, mode: PySide6.Qt3DAnimation.Qt3DAnimation.QKeyframeAnimation.RepeatMode) -> None: ...
        def setFramePositions(self, positions: Sequence[float]) -> None: ...
        def setKeyframes(self, keyframes: Sequence[PySide6.Qt3DCore.Qt3DCore.QTransform]) -> None: ...
        def setStartMode(self, mode: PySide6.Qt3DAnimation.Qt3DAnimation.QKeyframeAnimation.RepeatMode) -> None: ...
        def setTarget(self, target: PySide6.Qt3DCore.Qt3DCore.QTransform) -> None: ...
        def setTargetName(self, name: str) -> None: ...
        def startMode(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QKeyframeAnimation.RepeatMode: ...
        def target(self) -> PySide6.Qt3DCore.Qt3DCore.QTransform: ...
        def targetName(self) -> str: ...

    class QLerpClipBlend(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode):

        blendFactorChanged       : ClassVar[Signal] = ... # blendFactorChanged(float)
        endClipChanged           : ClassVar[Signal] = ... # endClipChanged(Qt3DAnimation::QAbstractClipBlendNode*)
        startClipChanged         : ClassVar[Signal] = ... # startClipChanged(Qt3DAnimation::QAbstractClipBlendNode*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def blendFactor(self) -> float: ...
        def endClip(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode: ...
        def setBlendFactor(self, blendFactor: float) -> None: ...
        def setEndClip(self, endClip: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode) -> None: ...
        def setStartClip(self, startClip: PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode) -> None: ...
        def startClip(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractClipBlendNode: ...

    class QMorphTarget(PySide6.QtCore.QObject):

        attributeNamesChanged    : ClassVar[Signal] = ... # attributeNamesChanged(QStringList)

        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def addAttribute(self, attribute: PySide6.Qt3DCore.Qt3DCore.QAttribute) -> None: ...
        def attributeList(self) -> List[PySide6.Qt3DCore.Qt3DCore.QAttribute]: ...
        def attributeNames(self) -> List[str]: ...
        @staticmethod
        def fromGeometry(geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, attributes: Sequence[str]) -> PySide6.Qt3DAnimation.Qt3DAnimation.QMorphTarget: ...
        def removeAttribute(self, attribute: PySide6.Qt3DCore.Qt3DCore.QAttribute) -> None: ...
        def setAttributes(self, attributes: Sequence[PySide6.Qt3DCore.Qt3DCore.QAttribute]) -> None: ...

    class QMorphingAnimation(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimation):

        easingChanged            : ClassVar[Signal] = ... # easingChanged(QEasingCurve)
        interpolatorChanged      : ClassVar[Signal] = ... # interpolatorChanged(float)
        methodChanged            : ClassVar[Signal] = ... # methodChanged(QMorphingAnimation::Method)
        targetChanged            : ClassVar[Signal] = ... # targetChanged(Qt3DRender::QGeometryRenderer*)
        targetNameChanged        : ClassVar[Signal] = ... # targetNameChanged(QString)
        targetPositionsChanged   : ClassVar[Signal] = ... # targetPositionsChanged(QList<float>)

        class Method(enum.Enum):

            Normalized               : Qt3DAnimation.QMorphingAnimation.Method = ... # 0x0
            Relative                 : Qt3DAnimation.QMorphingAnimation.Method = ... # 0x1


        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def addMorphTarget(self, target: PySide6.Qt3DAnimation.Qt3DAnimation.QMorphTarget) -> None: ...
        def easing(self) -> PySide6.QtCore.QEasingCurve: ...
        def getWeights(self, positionIndex: int) -> List[float]: ...
        def interpolator(self) -> float: ...
        def method(self) -> PySide6.Qt3DAnimation.Qt3DAnimation.QMorphingAnimation.Method: ...
        def morphTargetList(self) -> List[PySide6.Qt3DAnimation.Qt3DAnimation.QMorphTarget]: ...
        def removeMorphTarget(self, target: PySide6.Qt3DAnimation.Qt3DAnimation.QMorphTarget) -> None: ...
        def setEasing(self, easing: Union[PySide6.QtCore.QEasingCurve, PySide6.QtCore.QEasingCurve.Type]) -> None: ...
        def setMethod(self, method: PySide6.Qt3DAnimation.Qt3DAnimation.QMorphingAnimation.Method) -> None: ...
        def setMorphTargets(self, targets: Sequence[PySide6.Qt3DAnimation.Qt3DAnimation.QMorphTarget]) -> None: ...
        def setTarget(self, target: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer) -> None: ...
        def setTargetName(self, name: str) -> None: ...
        def setTargetPositions(self, targetPositions: Sequence[float]) -> None: ...
        def setWeights(self, positionIndex: int, weights: Sequence[float]) -> None: ...
        def target(self) -> PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer: ...
        def targetName(self) -> str: ...
        def targetPositions(self) -> List[float]: ...

    class QSkeletonMapping(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractChannelMapping):

        skeletonChanged          : ClassVar[Signal] = ... # skeletonChanged(Qt3DCore::QAbstractSkeleton*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setSkeleton(self, skeleton: PySide6.Qt3DCore.Qt3DCore.QAbstractSkeleton) -> None: ...
        def skeleton(self) -> PySide6.Qt3DCore.Qt3DCore.QAbstractSkeleton: ...

    class QVertexBlendAnimation(PySide6.Qt3DAnimation.Qt3DAnimation.QAbstractAnimation):

        interpolatorChanged      : ClassVar[Signal] = ... # interpolatorChanged(float)
        targetChanged            : ClassVar[Signal] = ... # targetChanged(Qt3DRender::QGeometryRenderer*)
        targetNameChanged        : ClassVar[Signal] = ... # targetNameChanged(QString)
        targetPositionsChanged   : ClassVar[Signal] = ... # targetPositionsChanged(QList<float>)

        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def addMorphTarget(self, target: PySide6.Qt3DAnimation.Qt3DAnimation.QMorphTarget) -> None: ...
        def interpolator(self) -> float: ...
        def morphTargetList(self) -> List[PySide6.Qt3DAnimation.Qt3DAnimation.QMorphTarget]: ...
        def removeMorphTarget(self, target: PySide6.Qt3DAnimation.Qt3DAnimation.QMorphTarget) -> None: ...
        def setMorphTargets(self, targets: Sequence[PySide6.Qt3DAnimation.Qt3DAnimation.QMorphTarget]) -> None: ...
        def setTarget(self, target: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer) -> None: ...
        def setTargetName(self, name: str) -> None: ...
        def setTargetPositions(self, targetPositions: Sequence[float]) -> None: ...
        def target(self) -> PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer: ...
        def targetName(self) -> str: ...
        def targetPositions(self) -> List[float]: ...


# eof
