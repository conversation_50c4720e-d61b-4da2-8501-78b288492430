# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
PySide6.Qt3DRender, except for defaults which are replaced by "...".
"""

# Module `PySide6.Qt3DRender`

import PySide6.Qt3DRender
import PySide6.QtCore
import PySide6.QtGui
import PySide6.Qt3DCore

import enum
from typing import Any, ClassVar, List, Optional, Sequence, Tuple, Union, overload
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


NoneType = type(None)


class QIntList(object): ...


class Qt3DRender(Shiboken.Object):

    class API(enum.Enum):

        OpenGL                   : Qt3DRender.API = ... # 0x0
        Vulkan                   : Qt3DRender.API = ... # 0x1
        DirectX                  : Qt3DRender.API = ... # 0x2
        Metal                    : Qt3DRender.API = ... # 0x3
        RHI                      : Qt3DRender.API = ... # 0x4
        Null                     : Qt3DRender.API = ... # 0x5


    class PropertyReaderInterface(Shiboken.Object):

        def __init__(self) -> None: ...

        def readProperty(self, v: Any) -> Any: ...

    class PropertyReaderInterfacePtr(Shiboken.Object):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, pointee: PySide6.Qt3DRender.Qt3DRender.PropertyReaderInterface) -> None: ...

        @staticmethod
        def __copy__() -> None: ...
        def data(self) -> PySide6.Qt3DRender.Qt3DRender.PropertyReaderInterface: ...
        @overload
        def reset(self) -> None: ...
        @overload
        def reset(self, t: PySide6.Qt3DRender.Qt3DRender.PropertyReaderInterface) -> None: ...

    class QAbstractLight(PySide6.Qt3DCore.Qt3DCore.QComponent):

        colorChanged             : ClassVar[Signal] = ... # colorChanged(QColor)
        intensityChanged         : ClassVar[Signal] = ... # intensityChanged(float)

        class Type(enum.Enum):

            PointLight               : Qt3DRender.QAbstractLight.Type = ... # 0x0
            DirectionalLight         : Qt3DRender.QAbstractLight.Type = ... # 0x1
            SpotLight                : Qt3DRender.QAbstractLight.Type = ... # 0x2


        def color(self) -> PySide6.QtGui.QColor: ...
        def intensity(self) -> float: ...
        def setColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setIntensity(self, intensity: float) -> None: ...
        def type(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractLight.Type: ...

    class QAbstractRayCaster(PySide6.Qt3DCore.Qt3DCore.QComponent):

        filterModeChanged        : ClassVar[Signal] = ... # filterModeChanged(Qt3DRender::QAbstractRayCaster::FilterMode)
        hitsChanged              : ClassVar[Signal] = ... # hitsChanged(Qt3DRender::QAbstractRayCaster::Hits)
        runModeChanged           : ClassVar[Signal] = ... # runModeChanged(Qt3DRender::QAbstractRayCaster::RunMode)

        class FilterMode(enum.Enum):

            AcceptAnyMatchingLayers  : Qt3DRender.QAbstractRayCaster.FilterMode = ... # 0x0
            AcceptAllMatchingLayers  : Qt3DRender.QAbstractRayCaster.FilterMode = ... # 0x1
            DiscardAnyMatchingLayers : Qt3DRender.QAbstractRayCaster.FilterMode = ... # 0x2
            DiscardAllMatchingLayers : Qt3DRender.QAbstractRayCaster.FilterMode = ... # 0x3


        class RunMode(enum.Enum):

            Continuous               : Qt3DRender.QAbstractRayCaster.RunMode = ... # 0x0
            SingleShot               : Qt3DRender.QAbstractRayCaster.RunMode = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addLayer(self, layer: PySide6.Qt3DRender.Qt3DRender.QLayer) -> None: ...
        def filterMode(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractRayCaster.FilterMode: ...
        def hits(self) -> List[PySide6.Qt3DRender.Qt3DRender.QRayCasterHit]: ...
        def layers(self) -> List[PySide6.Qt3DRender.Qt3DRender.QLayer]: ...
        def removeLayer(self, layer: PySide6.Qt3DRender.Qt3DRender.QLayer) -> None: ...
        def runMode(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractRayCaster.RunMode: ...
        def setFilterMode(self, filterMode: PySide6.Qt3DRender.Qt3DRender.QAbstractRayCaster.FilterMode) -> None: ...
        def setRunMode(self, runMode: PySide6.Qt3DRender.Qt3DRender.QAbstractRayCaster.RunMode) -> None: ...

    class QAbstractTexture(PySide6.Qt3DCore.Qt3DCore.QNode):

        comparisonFunctionChanged: ClassVar[Signal] = ... # comparisonFunctionChanged(ComparisonFunction)
        comparisonModeChanged    : ClassVar[Signal] = ... # comparisonModeChanged(ComparisonMode)
        depthChanged             : ClassVar[Signal] = ... # depthChanged(int)
        formatChanged            : ClassVar[Signal] = ... # formatChanged(TextureFormat)
        generateMipMapsChanged   : ClassVar[Signal] = ... # generateMipMapsChanged(bool)
        handleChanged            : ClassVar[Signal] = ... # handleChanged(QVariant)
        handleTypeChanged        : ClassVar[Signal] = ... # handleTypeChanged(HandleType)
        heightChanged            : ClassVar[Signal] = ... # heightChanged(int)
        layersChanged            : ClassVar[Signal] = ... # layersChanged(int)
        magnificationFilterChanged: ClassVar[Signal] = ... # magnificationFilterChanged(Filter)
        maximumAnisotropyChanged : ClassVar[Signal] = ... # maximumAnisotropyChanged(float)
        minificationFilterChanged: ClassVar[Signal] = ... # minificationFilterChanged(Filter)
        mipLevelsChanged         : ClassVar[Signal] = ... # mipLevelsChanged(int)
        samplesChanged           : ClassVar[Signal] = ... # samplesChanged(int)
        statusChanged            : ClassVar[Signal] = ... # statusChanged(Status)
        widthChanged             : ClassVar[Signal] = ... # widthChanged(int)

        class ComparisonFunction(enum.Enum):

            CompareNever             : Qt3DRender.QAbstractTexture.ComparisonFunction = ... # 0x200
            CompareLess              : Qt3DRender.QAbstractTexture.ComparisonFunction = ... # 0x201
            CompareEqual             : Qt3DRender.QAbstractTexture.ComparisonFunction = ... # 0x202
            CompareLessEqual         : Qt3DRender.QAbstractTexture.ComparisonFunction = ... # 0x203
            CompareGreater           : Qt3DRender.QAbstractTexture.ComparisonFunction = ... # 0x204
            CommpareNotEqual         : Qt3DRender.QAbstractTexture.ComparisonFunction = ... # 0x205
            CompareGreaterEqual      : Qt3DRender.QAbstractTexture.ComparisonFunction = ... # 0x206
            CompareAlways            : Qt3DRender.QAbstractTexture.ComparisonFunction = ... # 0x207


        class ComparisonMode(enum.Enum):

            CompareNone              : Qt3DRender.QAbstractTexture.ComparisonMode = ... # 0x0
            CompareRefToTexture      : Qt3DRender.QAbstractTexture.ComparisonMode = ... # 0x884e


        class CubeMapFace(enum.Enum):

            CubeMapPositiveX         : Qt3DRender.QAbstractTexture.CubeMapFace = ... # 0x8515
            CubeMapNegativeX         : Qt3DRender.QAbstractTexture.CubeMapFace = ... # 0x8516
            CubeMapPositiveY         : Qt3DRender.QAbstractTexture.CubeMapFace = ... # 0x8517
            CubeMapNegativeY         : Qt3DRender.QAbstractTexture.CubeMapFace = ... # 0x8518
            CubeMapPositiveZ         : Qt3DRender.QAbstractTexture.CubeMapFace = ... # 0x8519
            CubeMapNegativeZ         : Qt3DRender.QAbstractTexture.CubeMapFace = ... # 0x851a
            AllFaces                 : Qt3DRender.QAbstractTexture.CubeMapFace = ... # 0x851b


        class Filter(enum.Enum):

            Nearest                  : Qt3DRender.QAbstractTexture.Filter = ... # 0x2600
            Linear                   : Qt3DRender.QAbstractTexture.Filter = ... # 0x2601
            NearestMipMapNearest     : Qt3DRender.QAbstractTexture.Filter = ... # 0x2700
            LinearMipMapNearest      : Qt3DRender.QAbstractTexture.Filter = ... # 0x2701
            NearestMipMapLinear      : Qt3DRender.QAbstractTexture.Filter = ... # 0x2702
            LinearMipMapLinear       : Qt3DRender.QAbstractTexture.Filter = ... # 0x2703


        class HandleType(enum.Enum):

            NoHandle                 : Qt3DRender.QAbstractTexture.HandleType = ... # 0x0
            OpenGLTextureId          : Qt3DRender.QAbstractTexture.HandleType = ... # 0x1
            RHITextureId             : Qt3DRender.QAbstractTexture.HandleType = ... # 0x2


        class Status(enum.Enum):

            None_                    : Qt3DRender.QAbstractTexture.Status = ... # 0x0
            Loading                  : Qt3DRender.QAbstractTexture.Status = ... # 0x1
            Ready                    : Qt3DRender.QAbstractTexture.Status = ... # 0x2
            Error                    : Qt3DRender.QAbstractTexture.Status = ... # 0x3


        class Target(enum.Enum):

            TargetAutomatic          : Qt3DRender.QAbstractTexture.Target = ... # 0x0
            Target1D                 : Qt3DRender.QAbstractTexture.Target = ... # 0xde0
            Target2D                 : Qt3DRender.QAbstractTexture.Target = ... # 0xde1
            Target3D                 : Qt3DRender.QAbstractTexture.Target = ... # 0x806f
            TargetRectangle          : Qt3DRender.QAbstractTexture.Target = ... # 0x84f5
            TargetCubeMap            : Qt3DRender.QAbstractTexture.Target = ... # 0x8513
            Target1DArray            : Qt3DRender.QAbstractTexture.Target = ... # 0x8c18
            Target2DArray            : Qt3DRender.QAbstractTexture.Target = ... # 0x8c1a
            TargetBuffer             : Qt3DRender.QAbstractTexture.Target = ... # 0x8c2a
            TargetCubeMapArray       : Qt3DRender.QAbstractTexture.Target = ... # 0x9009
            Target2DMultisample      : Qt3DRender.QAbstractTexture.Target = ... # 0x9100
            Target2DMultisampleArray : Qt3DRender.QAbstractTexture.Target = ... # 0x9102


        class TextureFormat(enum.Enum):

            NoFormat                 : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x0
            Automatic                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x1
            DepthFormat              : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x1902
            AlphaFormat              : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x1906
            RGBFormat                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x1907
            RGBAFormat               : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x1908
            LuminanceFormat          : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x1909
            LuminanceAlphaFormat     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x190a
            RG3B2                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x2a10
            RGB8_UNorm               : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8051
            RGB16_UNorm              : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8054
            RGBA4                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8056
            RGB5A1                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8057
            RGBA8_UNorm              : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8058
            RGB10A2                  : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8059
            RGBA16_UNorm             : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x805b
            D16                      : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x81a5
            D24                      : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x81a6
            D32                      : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x81a7
            R8_UNorm                 : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8229
            R16_UNorm                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x822a
            RG8_UNorm                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x822b
            RG16_UNorm               : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x822c
            R16F                     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x822d
            R32F                     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x822e
            RG16F                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x822f
            RG32F                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8230
            R8I                      : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8231
            R8U                      : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8232
            R16I                     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8233
            R16U                     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8234
            R32I                     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8235
            R32U                     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8236
            RG8I                     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8237
            RG8U                     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8238
            RG16I                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8239
            RG16U                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x823a
            RG32I                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x823b
            RG32U                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x823c
            RGB_DXT1                 : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x83f0
            RGBA_DXT1                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x83f1
            RGBA_DXT3                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x83f2
            RGBA_DXT5                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x83f3
            RGBA32F                  : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8814
            RGB32F                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8815
            RGBA16F                  : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x881a
            RGB16F                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x881b
            D24S8                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x88f0
            RG11B10F                 : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8c3a
            RGB9E5                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8c3d
            SRGB8                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8c41
            SRGB8_Alpha8             : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8c43
            SRGB_DXT1                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8c4c
            SRGB_Alpha_DXT1          : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8c4d
            SRGB_Alpha_DXT3          : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8c4e
            SRGB_Alpha_DXT5          : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8c4f
            D32F                     : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8cac
            D32FS8X24                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8cad
            R5G6B5                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d62
            RGB8_ETC1                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d64
            RGBA32U                  : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d70
            RGB32U                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d71
            RGBA16U                  : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d76
            RGB16U                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d77
            RGBA8U                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d7c
            RGB8U                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d7d
            RGBA32I                  : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d82
            RGB32I                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d83
            RGBA16I                  : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d88
            RGB16I                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d89
            RGBA8I                   : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d8e
            RGB8I                    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8d8f
            R_ATI1N_UNorm            : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8dbb
            R_ATI1N_SNorm            : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8dbc
            RG_ATI2N_UNorm           : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8dbd
            RG_ATI2N_SNorm           : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8dbe
            RGB_BP_UNorm             : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8e8c
            SRGB_BP_UNorm            : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8e8d
            RGB_BP_SIGNED_FLOAT      : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8e8e
            RGB_BP_UNSIGNED_FLOAT    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8e8f
            R8_SNorm                 : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8f94
            RG8_SNorm                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8f95
            RGB8_SNorm               : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8f96
            RGBA8_SNorm              : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8f97
            R16_SNorm                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8f98
            RG16_SNorm               : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8f99
            RGB16_SNorm              : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8f9a
            RGBA16_SNorm             : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x8f9b
            RGB10A2U                 : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x906f
            R11_EAC_UNorm            : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9270
            R11_EAC_SNorm            : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9271
            RG11_EAC_UNorm           : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9272
            RG11_EAC_SNorm           : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9273
            RGB8_ETC2                : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9274
            SRGB8_ETC2               : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9275
            RGB8_PunchThrough_Alpha1_ETC2: Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9276
            SRGB8_PunchThrough_Alpha1_ETC2: Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9277
            RGBA8_ETC2_EAC           : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9278
            SRGB8_Alpha8_ETC2_EAC    : Qt3DRender.QAbstractTexture.TextureFormat = ... # 0x9279


        @overload
        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...
        @overload
        def __init__(self, target: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Target, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addTextureImage(self, textureImage: PySide6.Qt3DRender.Qt3DRender.QAbstractTextureImage) -> None: ...
        def comparisonFunction(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.ComparisonFunction: ...
        def comparisonMode(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.ComparisonMode: ...
        def depth(self) -> int: ...
        def format(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.TextureFormat: ...
        def generateMipMaps(self) -> bool: ...
        def handle(self) -> Any: ...
        def handleType(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.HandleType: ...
        def height(self) -> int: ...
        def layers(self) -> int: ...
        def magnificationFilter(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Filter: ...
        def maximumAnisotropy(self) -> float: ...
        def minificationFilter(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Filter: ...
        def mipLevels(self) -> int: ...
        def removeTextureImage(self, textureImage: PySide6.Qt3DRender.Qt3DRender.QAbstractTextureImage) -> None: ...
        def samples(self) -> int: ...
        def setComparisonFunction(self, function: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.ComparisonFunction) -> None: ...
        def setComparisonMode(self, mode: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.ComparisonMode) -> None: ...
        def setDepth(self, depth: int) -> None: ...
        def setFormat(self, format: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.TextureFormat) -> None: ...
        def setGenerateMipMaps(self, gen: bool) -> None: ...
        def setHandle(self, handle: Any) -> None: ...
        def setHandleType(self, type: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.HandleType) -> None: ...
        def setHeight(self, height: int) -> None: ...
        def setLayers(self, layers: int) -> None: ...
        def setMagnificationFilter(self, f: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Filter) -> None: ...
        def setMaximumAnisotropy(self, anisotropy: float) -> None: ...
        def setMinificationFilter(self, f: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Filter) -> None: ...
        def setMipLevels(self, mipLevels: int) -> None: ...
        def setSamples(self, samples: int) -> None: ...
        def setSize(self, width: int, height: int = ..., depth: int = ...) -> None: ...
        def setStatus(self, status: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Status) -> None: ...
        def setWidth(self, width: int) -> None: ...
        def setWrapMode(self, wrapMode: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode) -> None: ...
        def status(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Status: ...
        def target(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Target: ...
        def textureImages(self) -> List[PySide6.Qt3DRender.Qt3DRender.QAbstractTextureImage]: ...
        def updateData(self, update: PySide6.Qt3DRender.Qt3DRender.QTextureDataUpdate) -> None: ...
        def width(self) -> int: ...
        def wrapMode(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode: ...

    class QAbstractTextureImage(PySide6.Qt3DCore.Qt3DCore.QNode):

        faceChanged              : ClassVar[Signal] = ... # faceChanged(QAbstractTexture::CubeMapFace)
        layerChanged             : ClassVar[Signal] = ... # layerChanged(int)
        mipLevelChanged          : ClassVar[Signal] = ... # mipLevelChanged(int)
        def dataGenerator(self) -> Tuple[PySide6.Qt3DRender.Qt3DRender.QTextureImageDataGenerator]: ...
        def face(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.CubeMapFace: ...
        def layer(self) -> int: ...
        def mipLevel(self) -> int: ...
        def notifyDataGeneratorChanged(self) -> None: ...
        def setFace(self, face: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.CubeMapFace) -> None: ...
        def setLayer(self, layer: int) -> None: ...
        def setMipLevel(self, level: int) -> None: ...

    class QAlphaCoverage(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QAlphaTest(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        alphaFunctionChanged     : ClassVar[Signal] = ... # alphaFunctionChanged(AlphaFunction)
        referenceValueChanged    : ClassVar[Signal] = ... # referenceValueChanged(float)

        class AlphaFunction(enum.Enum):

            Never                    : Qt3DRender.QAlphaTest.AlphaFunction = ... # 0x200
            Less                     : Qt3DRender.QAlphaTest.AlphaFunction = ... # 0x201
            Equal                    : Qt3DRender.QAlphaTest.AlphaFunction = ... # 0x202
            LessOrEqual              : Qt3DRender.QAlphaTest.AlphaFunction = ... # 0x203
            Greater                  : Qt3DRender.QAlphaTest.AlphaFunction = ... # 0x204
            NotEqual                 : Qt3DRender.QAlphaTest.AlphaFunction = ... # 0x205
            GreaterOrEqual           : Qt3DRender.QAlphaTest.AlphaFunction = ... # 0x206
            Always                   : Qt3DRender.QAlphaTest.AlphaFunction = ... # 0x207


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def alphaFunction(self) -> PySide6.Qt3DRender.Qt3DRender.QAlphaTest.AlphaFunction: ...
        def referenceValue(self) -> float: ...
        def setAlphaFunction(self, alphaFunction: PySide6.Qt3DRender.Qt3DRender.QAlphaTest.AlphaFunction) -> None: ...
        def setReferenceValue(self, referenceValue: float) -> None: ...

    class QBlendEquation(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        blendFunctionChanged     : ClassVar[Signal] = ... # blendFunctionChanged(BlendFunction)

        class BlendFunction(enum.Enum):

            Add                      : Qt3DRender.QBlendEquation.BlendFunction = ... # 0x8006
            Min                      : Qt3DRender.QBlendEquation.BlendFunction = ... # 0x8007
            Max                      : Qt3DRender.QBlendEquation.BlendFunction = ... # 0x8008
            Subtract                 : Qt3DRender.QBlendEquation.BlendFunction = ... # 0x800a
            ReverseSubtract          : Qt3DRender.QBlendEquation.BlendFunction = ... # 0x800b


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def blendFunction(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquation.BlendFunction: ...
        def setBlendFunction(self, blendFunction: PySide6.Qt3DRender.Qt3DRender.QBlendEquation.BlendFunction) -> None: ...

    class QBlendEquationArguments(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        bufferIndexChanged       : ClassVar[Signal] = ... # bufferIndexChanged(int)
        destinationAlphaChanged  : ClassVar[Signal] = ... # destinationAlphaChanged(Blending)
        destinationRgbChanged    : ClassVar[Signal] = ... # destinationRgbChanged(Blending)
        destinationRgbaChanged   : ClassVar[Signal] = ... # destinationRgbaChanged(Blending)
        sourceAlphaChanged       : ClassVar[Signal] = ... # sourceAlphaChanged(Blending)
        sourceRgbChanged         : ClassVar[Signal] = ... # sourceRgbChanged(Blending)
        sourceRgbaChanged        : ClassVar[Signal] = ... # sourceRgbaChanged(Blending)

        class Blending(enum.Enum):

            Zero                     : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x0
            One                      : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x1
            SourceColor              : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x300
            OneMinusSourceColor      : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x301
            SourceAlpha              : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x302
            OneMinusSourceAlpha      : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x303
            Source1Alpha             : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x303
            DestinationAlpha         : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x304
            Source1Color             : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x304
            OneMinusDestinationAlpha : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x305
            DestinationColor         : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x306
            OneMinusDestinationColor : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x307
            SourceAlphaSaturate      : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x308
            ConstantColor            : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x8001
            OneMinusConstantColor    : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x8002
            ConstantAlpha            : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x8003
            OneMinusConstantAlpha    : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x8004
            OneMinusSource1Alpha     : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x8005
            OneMinusSource1Color     : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x8006
            OneMinusSource1Color0    : Qt3DRender.QBlendEquationArguments.Blending = ... # 0x8006


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def bufferIndex(self) -> int: ...
        def destinationAlpha(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def destinationRgb(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def setBufferIndex(self, index: int) -> None: ...
        def setDestinationAlpha(self, destinationAlpha: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def setDestinationRgb(self, destinationRgb: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def setDestinationRgba(self, destinationRgba: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def setSourceAlpha(self, sourceAlpha: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def setSourceRgb(self, sourceRgb: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def setSourceRgba(self, sourceRgba: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending) -> None: ...
        def sourceAlpha(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def sourceRgb(self) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...

    class QBlitFramebuffer(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        destinationAttachmentPointChanged: ClassVar[Signal] = ... # destinationAttachmentPointChanged()
        destinationChanged       : ClassVar[Signal] = ... # destinationChanged()
        destinationRectChanged   : ClassVar[Signal] = ... # destinationRectChanged()
        interpolationMethodChanged: ClassVar[Signal] = ... # interpolationMethodChanged()
        sourceAttachmentPointChanged: ClassVar[Signal] = ... # sourceAttachmentPointChanged()
        sourceChanged            : ClassVar[Signal] = ... # sourceChanged()
        sourceRectChanged        : ClassVar[Signal] = ... # sourceRectChanged()

        class InterpolationMethod(enum.Enum):

            Nearest                  : Qt3DRender.QBlitFramebuffer.InterpolationMethod = ... # 0x0
            Linear                   : Qt3DRender.QBlitFramebuffer.InterpolationMethod = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def destination(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderTarget: ...
        def destinationAttachmentPoint(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput.AttachmentPoint: ...
        def destinationRect(self) -> PySide6.QtCore.QRectF: ...
        def interpolationMethod(self) -> PySide6.Qt3DRender.Qt3DRender.QBlitFramebuffer.InterpolationMethod: ...
        def setDestination(self, destination: PySide6.Qt3DRender.Qt3DRender.QRenderTarget) -> None: ...
        def setDestinationAttachmentPoint(self, destinationAttachmentPoint: PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput.AttachmentPoint) -> None: ...
        def setDestinationRect(self, destinationRect: Union[PySide6.QtCore.QRectF, PySide6.QtCore.QRect]) -> None: ...
        def setInterpolationMethod(self, interpolationMethod: PySide6.Qt3DRender.Qt3DRender.QBlitFramebuffer.InterpolationMethod) -> None: ...
        def setSource(self, source: PySide6.Qt3DRender.Qt3DRender.QRenderTarget) -> None: ...
        def setSourceAttachmentPoint(self, sourceAttachmentPoint: PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput.AttachmentPoint) -> None: ...
        def setSourceRect(self, sourceRect: Union[PySide6.QtCore.QRectF, PySide6.QtCore.QRect]) -> None: ...
        def source(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderTarget: ...
        def sourceAttachmentPoint(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput.AttachmentPoint: ...
        def sourceRect(self) -> PySide6.QtCore.QRectF: ...

    class QBufferCapture(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QCamera(PySide6.Qt3DCore.Qt3DCore.QEntity):

        aspectRatioChanged       : ClassVar[Signal] = ... # aspectRatioChanged(float)
        bottomChanged            : ClassVar[Signal] = ... # bottomChanged(float)
        exposureChanged          : ClassVar[Signal] = ... # exposureChanged(float)
        farPlaneChanged          : ClassVar[Signal] = ... # farPlaneChanged(float)
        fieldOfViewChanged       : ClassVar[Signal] = ... # fieldOfViewChanged(float)
        leftChanged              : ClassVar[Signal] = ... # leftChanged(float)
        nearPlaneChanged         : ClassVar[Signal] = ... # nearPlaneChanged(float)
        positionChanged          : ClassVar[Signal] = ... # positionChanged(QVector3D)
        projectionMatrixChanged  : ClassVar[Signal] = ... # projectionMatrixChanged(QMatrix4x4)
        projectionTypeChanged    : ClassVar[Signal] = ... # projectionTypeChanged(QCameraLens::ProjectionType)
        rightChanged             : ClassVar[Signal] = ... # rightChanged(float)
        topChanged               : ClassVar[Signal] = ... # topChanged(float)
        upVectorChanged          : ClassVar[Signal] = ... # upVectorChanged(QVector3D)
        viewCenterChanged        : ClassVar[Signal] = ... # viewCenterChanged(QVector3D)
        viewMatrixChanged        : ClassVar[Signal] = ... # viewMatrixChanged()
        viewVectorChanged        : ClassVar[Signal] = ... # viewVectorChanged(QVector3D)

        class CameraTranslationOption(enum.Enum):

            TranslateViewCenter      : Qt3DRender.QCamera.CameraTranslationOption = ... # 0x0
            DontTranslateViewCenter  : Qt3DRender.QCamera.CameraTranslationOption = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def aspectRatio(self) -> float: ...
        def bottom(self) -> float: ...
        def exposure(self) -> float: ...
        def farPlane(self) -> float: ...
        def fieldOfView(self) -> float: ...
        def left(self) -> float: ...
        def lens(self) -> PySide6.Qt3DRender.Qt3DRender.QCameraLens: ...
        def nearPlane(self) -> float: ...
        @overload
        def pan(self, angle: float) -> None: ...
        @overload
        def pan(self, angle: float, axis: PySide6.QtGui.QVector3D) -> None: ...
        @overload
        def panAboutViewCenter(self, angle: float) -> None: ...
        @overload
        def panAboutViewCenter(self, angle: float, axis: PySide6.QtGui.QVector3D) -> None: ...
        def panRotation(self, angle: float) -> PySide6.QtGui.QQuaternion: ...
        def position(self) -> PySide6.QtGui.QVector3D: ...
        def projectionMatrix(self) -> PySide6.QtGui.QMatrix4x4: ...
        def projectionType(self) -> PySide6.Qt3DRender.Qt3DRender.QCameraLens.ProjectionType: ...
        def right(self) -> float: ...
        def roll(self, angle: float) -> None: ...
        def rollAboutViewCenter(self, angle: float) -> None: ...
        def rollRotation(self, angle: float) -> PySide6.QtGui.QQuaternion: ...
        def rotate(self, q: PySide6.QtGui.QQuaternion) -> None: ...
        def rotateAboutViewCenter(self, q: PySide6.QtGui.QQuaternion) -> None: ...
        def rotation(self, angle: float, axis: PySide6.QtGui.QVector3D) -> PySide6.QtGui.QQuaternion: ...
        def setAspectRatio(self, aspectRatio: float) -> None: ...
        def setBottom(self, bottom: float) -> None: ...
        def setExposure(self, exposure: float) -> None: ...
        def setFarPlane(self, farPlane: float) -> None: ...
        def setFieldOfView(self, fieldOfView: float) -> None: ...
        def setLeft(self, left: float) -> None: ...
        def setNearPlane(self, nearPlane: float) -> None: ...
        def setPosition(self, position: PySide6.QtGui.QVector3D) -> None: ...
        def setProjectionMatrix(self, projectionMatrix: Union[PySide6.QtGui.QMatrix4x4, PySide6.QtGui.QTransform]) -> None: ...
        def setProjectionType(self, type: PySide6.Qt3DRender.Qt3DRender.QCameraLens.ProjectionType) -> None: ...
        def setRight(self, right: float) -> None: ...
        def setTop(self, top: float) -> None: ...
        def setUpVector(self, upVector: PySide6.QtGui.QVector3D) -> None: ...
        def setViewCenter(self, viewCenter: PySide6.QtGui.QVector3D) -> None: ...
        def tilt(self, angle: float) -> None: ...
        def tiltAboutViewCenter(self, angle: float) -> None: ...
        def tiltRotation(self, angle: float) -> PySide6.QtGui.QQuaternion: ...
        def top(self) -> float: ...
        def transform(self) -> PySide6.Qt3DCore.Qt3DCore.QTransform: ...
        def translate(self, vLocal: PySide6.QtGui.QVector3D, option: PySide6.Qt3DRender.Qt3DRender.QCamera.CameraTranslationOption = ...) -> None: ...
        def translateWorld(self, vWorld: PySide6.QtGui.QVector3D, option: PySide6.Qt3DRender.Qt3DRender.QCamera.CameraTranslationOption = ...) -> None: ...
        def upVector(self) -> PySide6.QtGui.QVector3D: ...
        def viewAll(self) -> None: ...
        def viewCenter(self) -> PySide6.QtGui.QVector3D: ...
        def viewEntity(self, entity: PySide6.Qt3DCore.Qt3DCore.QEntity) -> None: ...
        def viewMatrix(self) -> PySide6.QtGui.QMatrix4x4: ...
        def viewSphere(self, center: PySide6.QtGui.QVector3D, radius: float) -> None: ...
        def viewVector(self) -> PySide6.QtGui.QVector3D: ...

    class QCameraLens(PySide6.Qt3DCore.Qt3DCore.QComponent):

        aspectRatioChanged       : ClassVar[Signal] = ... # aspectRatioChanged(float)
        bottomChanged            : ClassVar[Signal] = ... # bottomChanged(float)
        exposureChanged          : ClassVar[Signal] = ... # exposureChanged(float)
        farPlaneChanged          : ClassVar[Signal] = ... # farPlaneChanged(float)
        fieldOfViewChanged       : ClassVar[Signal] = ... # fieldOfViewChanged(float)
        leftChanged              : ClassVar[Signal] = ... # leftChanged(float)
        nearPlaneChanged         : ClassVar[Signal] = ... # nearPlaneChanged(float)
        projectionMatrixChanged  : ClassVar[Signal] = ... # projectionMatrixChanged(QMatrix4x4)
        projectionTypeChanged    : ClassVar[Signal] = ... # projectionTypeChanged(QCameraLens::ProjectionType)
        rightChanged             : ClassVar[Signal] = ... # rightChanged(float)
        topChanged               : ClassVar[Signal] = ... # topChanged(float)
        viewSphere               : ClassVar[Signal] = ... # viewSphere(QVector3D,float)

        class ProjectionType(enum.Enum):

            OrthographicProjection   : Qt3DRender.QCameraLens.ProjectionType = ... # 0x0
            PerspectiveProjection    : Qt3DRender.QCameraLens.ProjectionType = ... # 0x1
            FrustumProjection        : Qt3DRender.QCameraLens.ProjectionType = ... # 0x2
            CustomProjection         : Qt3DRender.QCameraLens.ProjectionType = ... # 0x3


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def aspectRatio(self) -> float: ...
        def bottom(self) -> float: ...
        def exposure(self) -> float: ...
        def farPlane(self) -> float: ...
        def fieldOfView(self) -> float: ...
        def left(self) -> float: ...
        def nearPlane(self) -> float: ...
        def projectionMatrix(self) -> PySide6.QtGui.QMatrix4x4: ...
        def projectionType(self) -> PySide6.Qt3DRender.Qt3DRender.QCameraLens.ProjectionType: ...
        def right(self) -> float: ...
        def setAspectRatio(self, aspectRatio: float) -> None: ...
        def setBottom(self, bottom: float) -> None: ...
        def setExposure(self, exposure: float) -> None: ...
        def setFarPlane(self, farPlane: float) -> None: ...
        def setFieldOfView(self, fieldOfView: float) -> None: ...
        def setFrustumProjection(self, left: float, right: float, bottom: float, top: float, nearPlane: float, farPlane: float) -> None: ...
        def setLeft(self, left: float) -> None: ...
        def setNearPlane(self, nearPlane: float) -> None: ...
        def setOrthographicProjection(self, left: float, right: float, bottom: float, top: float, nearPlane: float, farPlane: float) -> None: ...
        def setPerspectiveProjection(self, fieldOfView: float, aspect: float, nearPlane: float, farPlane: float) -> None: ...
        def setProjectionMatrix(self, projectionMatrix: Union[PySide6.QtGui.QMatrix4x4, PySide6.QtGui.QTransform]) -> None: ...
        def setProjectionType(self, projectionType: PySide6.Qt3DRender.Qt3DRender.QCameraLens.ProjectionType) -> None: ...
        def setRight(self, right: float) -> None: ...
        def setTop(self, top: float) -> None: ...
        def top(self) -> float: ...
        def viewAll(self, cameraId: PySide6.Qt3DCore.Qt3DCore.QNodeId) -> None: ...
        def viewEntity(self, entityId: PySide6.Qt3DCore.Qt3DCore.QNodeId, cameraId: PySide6.Qt3DCore.Qt3DCore.QNodeId) -> None: ...

    class QCameraSelector(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        cameraChanged            : ClassVar[Signal] = ... # cameraChanged(Qt3DCore::QEntity*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def camera(self) -> PySide6.Qt3DCore.Qt3DCore.QEntity: ...
        def setCamera(self, camera: PySide6.Qt3DCore.Qt3DCore.QEntity) -> None: ...

    class QClearBuffers(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        buffersChanged           : ClassVar[Signal] = ... # buffersChanged(BufferType)
        clearColorChanged        : ClassVar[Signal] = ... # clearColorChanged(QColor)
        clearDepthValueChanged   : ClassVar[Signal] = ... # clearDepthValueChanged(float)
        clearStencilValueChanged : ClassVar[Signal] = ... # clearStencilValueChanged(int)
        colorBufferChanged       : ClassVar[Signal] = ... # colorBufferChanged(QRenderTargetOutput*)

        class BufferType(enum.Flag):

            None_                    : Qt3DRender.QClearBuffers.BufferType = ... # 0x0
            ColorBuffer              : Qt3DRender.QClearBuffers.BufferType = ... # 0x1
            DepthBuffer              : Qt3DRender.QClearBuffers.BufferType = ... # 0x2
            ColorDepthBuffer         : Qt3DRender.QClearBuffers.BufferType = ... # 0x3
            StencilBuffer            : Qt3DRender.QClearBuffers.BufferType = ... # 0x4
            DepthStencilBuffer       : Qt3DRender.QClearBuffers.BufferType = ... # 0x6
            ColorDepthStencilBuffer  : Qt3DRender.QClearBuffers.BufferType = ... # 0x7
            AllBuffers               : Qt3DRender.QClearBuffers.BufferType = ... # 0xffffffff


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def buffers(self) -> PySide6.Qt3DRender.Qt3DRender.QClearBuffers.BufferType: ...
        def clearColor(self) -> PySide6.QtGui.QColor: ...
        def clearDepthValue(self) -> float: ...
        def clearStencilValue(self) -> int: ...
        def colorBuffer(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput: ...
        def setBuffers(self, buffers: PySide6.Qt3DRender.Qt3DRender.QClearBuffers.BufferType) -> None: ...
        def setClearColor(self, color: Union[PySide6.QtGui.QColor, PySide6.QtGui.QRgba64, Any, PySide6.QtCore.Qt.GlobalColor, str, int]) -> None: ...
        def setClearDepthValue(self, clearDepthValue: float) -> None: ...
        def setClearStencilValue(self, clearStencilValue: int) -> None: ...
        def setColorBuffer(self, buffer: PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput) -> None: ...

    class QClipPlane(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        distanceChanged          : ClassVar[Signal] = ... # distanceChanged(float)
        normalChanged            : ClassVar[Signal] = ... # normalChanged(QVector3D)
        planeIndexChanged        : ClassVar[Signal] = ... # planeIndexChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def distance(self) -> float: ...
        def normal(self) -> PySide6.QtGui.QVector3D: ...
        def planeIndex(self) -> int: ...
        def setDistance(self, arg__1: float) -> None: ...
        def setNormal(self, arg__1: PySide6.QtGui.QVector3D) -> None: ...
        def setPlaneIndex(self, arg__1: int) -> None: ...

    class QColorMask(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        alphaMaskedChanged       : ClassVar[Signal] = ... # alphaMaskedChanged(bool)
        blueMaskedChanged        : ClassVar[Signal] = ... # blueMaskedChanged(bool)
        greenMaskedChanged       : ClassVar[Signal] = ... # greenMaskedChanged(bool)
        redMaskedChanged         : ClassVar[Signal] = ... # redMaskedChanged(bool)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def isAlphaMasked(self) -> bool: ...
        def isBlueMasked(self) -> bool: ...
        def isGreenMasked(self) -> bool: ...
        def isRedMasked(self) -> bool: ...
        def setAlphaMasked(self, alphaMasked: bool) -> None: ...
        def setBlueMasked(self, blueMasked: bool) -> None: ...
        def setGreenMasked(self, greenMasked: bool) -> None: ...
        def setRedMasked(self, redMasked: bool) -> None: ...

    class QComputeCommand(PySide6.Qt3DCore.Qt3DCore.QComponent):

        runTypeChanged           : ClassVar[Signal] = ... # runTypeChanged()
        workGroupXChanged        : ClassVar[Signal] = ... # workGroupXChanged()
        workGroupYChanged        : ClassVar[Signal] = ... # workGroupYChanged()
        workGroupZChanged        : ClassVar[Signal] = ... # workGroupZChanged()

        class RunType(enum.Enum):

            Continuous               : Qt3DRender.QComputeCommand.RunType = ... # 0x0
            Manual                   : Qt3DRender.QComputeCommand.RunType = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def runType(self) -> PySide6.Qt3DRender.Qt3DRender.QComputeCommand.RunType: ...
        def setRunType(self, runType: PySide6.Qt3DRender.Qt3DRender.QComputeCommand.RunType) -> None: ...
        def setWorkGroupX(self, workGroupX: int) -> None: ...
        def setWorkGroupY(self, workGroupY: int) -> None: ...
        def setWorkGroupZ(self, workGroupZ: int) -> None: ...
        @overload
        def trigger(self, frameCount: int = ...) -> None: ...
        @overload
        def trigger(self, workGroupX: int, workGroupY: int, workGroupZ: int, frameCount: int = ...) -> None: ...
        def workGroupX(self) -> int: ...
        def workGroupY(self) -> int: ...
        def workGroupZ(self) -> int: ...

    class QCullFace(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        modeChanged              : ClassVar[Signal] = ... # modeChanged(CullingMode)

        class CullingMode(enum.Enum):

            NoCulling                : Qt3DRender.QCullFace.CullingMode = ... # 0x0
            Front                    : Qt3DRender.QCullFace.CullingMode = ... # 0x404
            Back                     : Qt3DRender.QCullFace.CullingMode = ... # 0x405
            FrontAndBack             : Qt3DRender.QCullFace.CullingMode = ... # 0x408


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def mode(self) -> PySide6.Qt3DRender.Qt3DRender.QCullFace.CullingMode: ...
        def setMode(self, mode: PySide6.Qt3DRender.Qt3DRender.QCullFace.CullingMode) -> None: ...

    class QDepthRange(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        farValueChanged          : ClassVar[Signal] = ... # farValueChanged(double)
        nearValueChanged         : ClassVar[Signal] = ... # nearValueChanged(double)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def farValue(self) -> float: ...
        def nearValue(self) -> float: ...
        def setFarValue(self, value: float) -> None: ...
        def setNearValue(self, value: float) -> None: ...

    class QDepthTest(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        depthFunctionChanged     : ClassVar[Signal] = ... # depthFunctionChanged(DepthFunction)

        class DepthFunction(enum.Enum):

            Never                    : Qt3DRender.QDepthTest.DepthFunction = ... # 0x200
            Less                     : Qt3DRender.QDepthTest.DepthFunction = ... # 0x201
            Equal                    : Qt3DRender.QDepthTest.DepthFunction = ... # 0x202
            LessOrEqual              : Qt3DRender.QDepthTest.DepthFunction = ... # 0x203
            Greater                  : Qt3DRender.QDepthTest.DepthFunction = ... # 0x204
            NotEqual                 : Qt3DRender.QDepthTest.DepthFunction = ... # 0x205
            GreaterOrEqual           : Qt3DRender.QDepthTest.DepthFunction = ... # 0x206
            Always                   : Qt3DRender.QDepthTest.DepthFunction = ... # 0x207


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def depthFunction(self) -> PySide6.Qt3DRender.Qt3DRender.QDepthTest.DepthFunction: ...
        def setDepthFunction(self, depthFunction: PySide6.Qt3DRender.Qt3DRender.QDepthTest.DepthFunction) -> None: ...

    class QDirectionalLight(PySide6.Qt3DRender.Qt3DRender.QAbstractLight):

        worldDirectionChanged    : ClassVar[Signal] = ... # worldDirectionChanged(QVector3D)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setWorldDirection(self, worldDirection: PySide6.QtGui.QVector3D) -> None: ...
        def worldDirection(self) -> PySide6.QtGui.QVector3D: ...

    class QDispatchCompute(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        workGroupXChanged        : ClassVar[Signal] = ... # workGroupXChanged()
        workGroupYChanged        : ClassVar[Signal] = ... # workGroupYChanged()
        workGroupZChanged        : ClassVar[Signal] = ... # workGroupZChanged()

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setWorkGroupX(self, workGroupX: int) -> None: ...
        def setWorkGroupY(self, workGroupY: int) -> None: ...
        def setWorkGroupZ(self, workGroupZ: int) -> None: ...
        def workGroupX(self) -> int: ...
        def workGroupY(self) -> int: ...
        def workGroupZ(self) -> int: ...

    class QDithering(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QEffect(PySide6.Qt3DCore.Qt3DCore.QNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addParameter(self, parameter: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def addTechnique(self, t: PySide6.Qt3DRender.Qt3DRender.QTechnique) -> None: ...
        def parameters(self) -> List[PySide6.Qt3DRender.Qt3DRender.QParameter]: ...
        def removeParameter(self, parameter: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def removeTechnique(self, t: PySide6.Qt3DRender.Qt3DRender.QTechnique) -> None: ...
        def techniques(self) -> List[PySide6.Qt3DRender.Qt3DRender.QTechnique]: ...

    class QEnvironmentLight(PySide6.Qt3DCore.Qt3DCore.QComponent):

        irradianceChanged        : ClassVar[Signal] = ... # irradianceChanged(Qt3DRender::QAbstractTexture*)
        specularChanged          : ClassVar[Signal] = ... # specularChanged(Qt3DRender::QAbstractTexture*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def irradiance(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def setIrradiance(self, irradiance: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def setSpecular(self, specular: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def specular(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...

    class QFilterKey(PySide6.Qt3DCore.Qt3DCore.QNode):

        nameChanged              : ClassVar[Signal] = ... # nameChanged(QString)
        valueChanged             : ClassVar[Signal] = ... # valueChanged(QVariant)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def name(self) -> str: ...
        def setName(self, customType: str) -> None: ...
        def setValue(self, value: Any) -> None: ...
        def value(self) -> Any: ...

    class QFrameGraphNode(PySide6.Qt3DCore.Qt3DCore.QNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def parentFrameGraphNode(self) -> PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode: ...

    class QFrontFace(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        directionChanged         : ClassVar[Signal] = ... # directionChanged(WindingDirection)

        class WindingDirection(enum.Enum):

            ClockWise                : Qt3DRender.QFrontFace.WindingDirection = ... # 0x900
            CounterClockWise         : Qt3DRender.QFrontFace.WindingDirection = ... # 0x901


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def direction(self) -> PySide6.Qt3DRender.Qt3DRender.QFrontFace.WindingDirection: ...
        def setDirection(self, direction: PySide6.Qt3DRender.Qt3DRender.QFrontFace.WindingDirection) -> None: ...

    class QFrustumCulling(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QGeometryRenderer(PySide6.Qt3DCore.Qt3DCore.QBoundingVolume):

        firstInstanceChanged     : ClassVar[Signal] = ... # firstInstanceChanged(int)
        firstVertexChanged       : ClassVar[Signal] = ... # firstVertexChanged(int)
        geometryChanged          : ClassVar[Signal] = ... # geometryChanged(Qt3DCore::QGeometry*)
        indexBufferByteOffsetChanged: ClassVar[Signal] = ... # indexBufferByteOffsetChanged(int)
        indexOffsetChanged       : ClassVar[Signal] = ... # indexOffsetChanged(int)
        instanceCountChanged     : ClassVar[Signal] = ... # instanceCountChanged(int)
        primitiveRestartEnabledChanged: ClassVar[Signal] = ... # primitiveRestartEnabledChanged(bool)
        primitiveTypeChanged     : ClassVar[Signal] = ... # primitiveTypeChanged(PrimitiveType)
        restartIndexValueChanged : ClassVar[Signal] = ... # restartIndexValueChanged(int)
        sortIndexChanged         : ClassVar[Signal] = ... # sortIndexChanged(float)
        vertexCountChanged       : ClassVar[Signal] = ... # vertexCountChanged(int)
        verticesPerPatchChanged  : ClassVar[Signal] = ... # verticesPerPatchChanged(int)

        class PrimitiveType(enum.Enum):

            Points                   : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0x0
            Lines                    : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0x1
            LineLoop                 : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0x2
            LineStrip                : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0x3
            Triangles                : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0x4
            TriangleStrip            : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0x5
            TriangleFan              : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0x6
            LinesAdjacency           : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0xa
            LineStripAdjacency       : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0xb
            TrianglesAdjacency       : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0xc
            TriangleStripAdjacency   : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0xd
            Patches                  : Qt3DRender.QGeometryRenderer.PrimitiveType = ... # 0xe


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def firstInstance(self) -> int: ...
        def firstVertex(self) -> int: ...
        def geometry(self) -> PySide6.Qt3DCore.Qt3DCore.QGeometry: ...
        def indexBufferByteOffset(self) -> int: ...
        def indexOffset(self) -> int: ...
        def instanceCount(self) -> int: ...
        def primitiveRestartEnabled(self) -> bool: ...
        def primitiveType(self) -> PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType: ...
        def restartIndexValue(self) -> int: ...
        def setFirstInstance(self, firstInstance: int) -> None: ...
        def setFirstVertex(self, firstVertex: int) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry) -> None: ...
        def setIndexBufferByteOffset(self, offset: int) -> None: ...
        def setIndexOffset(self, indexOffset: int) -> None: ...
        def setInstanceCount(self, instanceCount: int) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType) -> None: ...
        def setRestartIndexValue(self, index: int) -> None: ...
        def setSortIndex(self, sortIndex: float) -> None: ...
        def setVertexCount(self, vertexCount: int) -> None: ...
        def setVerticesPerPatch(self, verticesPerPatch: int) -> None: ...
        def sortIndex(self) -> float: ...
        def vertexCount(self) -> int: ...
        def verticesPerPatch(self) -> int: ...

    class QGraphicsApiFilter(PySide6.QtCore.QObject):

        apiChanged               : ClassVar[Signal] = ... # apiChanged(Qt3DRender::QGraphicsApiFilter::Api)
        extensionsChanged        : ClassVar[Signal] = ... # extensionsChanged(QStringList)
        graphicsApiFilterChanged : ClassVar[Signal] = ... # graphicsApiFilterChanged()
        majorVersionChanged      : ClassVar[Signal] = ... # majorVersionChanged(int)
        minorVersionChanged      : ClassVar[Signal] = ... # minorVersionChanged(int)
        profileChanged           : ClassVar[Signal] = ... # profileChanged(Qt3DRender::QGraphicsApiFilter::OpenGLProfile)
        vendorChanged            : ClassVar[Signal] = ... # vendorChanged(QString)

        class Api(enum.Enum):

            OpenGL                   : Qt3DRender.QGraphicsApiFilter.Api = ... # 0x1
            OpenGLES                 : Qt3DRender.QGraphicsApiFilter.Api = ... # 0x2
            Vulkan                   : Qt3DRender.QGraphicsApiFilter.Api = ... # 0x3
            DirectX                  : Qt3DRender.QGraphicsApiFilter.Api = ... # 0x4
            RHI                      : Qt3DRender.QGraphicsApiFilter.Api = ... # 0x5


        class OpenGLProfile(enum.Enum):

            NoProfile                : Qt3DRender.QGraphicsApiFilter.OpenGLProfile = ... # 0x0
            CoreProfile              : Qt3DRender.QGraphicsApiFilter.OpenGLProfile = ... # 0x1
            CompatibilityProfile     : Qt3DRender.QGraphicsApiFilter.OpenGLProfile = ... # 0x2


        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def api(self) -> PySide6.Qt3DRender.Qt3DRender.QGraphicsApiFilter.Api: ...
        def extensions(self) -> List[str]: ...
        def majorVersion(self) -> int: ...
        def minorVersion(self) -> int: ...
        def profile(self) -> PySide6.Qt3DRender.Qt3DRender.QGraphicsApiFilter.OpenGLProfile: ...
        def setApi(self, api: PySide6.Qt3DRender.Qt3DRender.QGraphicsApiFilter.Api) -> None: ...
        def setExtensions(self, extensions: Sequence[str]) -> None: ...
        def setMajorVersion(self, majorVersion: int) -> None: ...
        def setMinorVersion(self, minorVersion: int) -> None: ...
        def setProfile(self, profile: PySide6.Qt3DRender.Qt3DRender.QGraphicsApiFilter.OpenGLProfile) -> None: ...
        def setVendor(self, vendor: str) -> None: ...
        def vendor(self) -> str: ...

    class QLayer(PySide6.Qt3DCore.Qt3DCore.QComponent):

        recursiveChanged         : ClassVar[Signal] = ... # recursiveChanged()

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def recursive(self) -> bool: ...
        def setRecursive(self, recursive: bool) -> None: ...

    class QLayerFilter(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        filterModeChanged        : ClassVar[Signal] = ... # filterModeChanged(FilterMode)

        class FilterMode(enum.Enum):

            AcceptAnyMatchingLayers  : Qt3DRender.QLayerFilter.FilterMode = ... # 0x0
            AcceptAllMatchingLayers  : Qt3DRender.QLayerFilter.FilterMode = ... # 0x1
            DiscardAnyMatchingLayers : Qt3DRender.QLayerFilter.FilterMode = ... # 0x2
            DiscardAllMatchingLayers : Qt3DRender.QLayerFilter.FilterMode = ... # 0x3


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addLayer(self, layer: PySide6.Qt3DRender.Qt3DRender.QLayer) -> None: ...
        def filterMode(self) -> PySide6.Qt3DRender.Qt3DRender.QLayerFilter.FilterMode: ...
        def layers(self) -> List[PySide6.Qt3DRender.Qt3DRender.QLayer]: ...
        def removeLayer(self, layer: PySide6.Qt3DRender.Qt3DRender.QLayer) -> None: ...
        def setFilterMode(self, filterMode: PySide6.Qt3DRender.Qt3DRender.QLayerFilter.FilterMode) -> None: ...

    class QLevelOfDetail(PySide6.Qt3DCore.Qt3DCore.QComponent):

        cameraChanged            : ClassVar[Signal] = ... # cameraChanged(QCamera*)
        currentIndexChanged      : ClassVar[Signal] = ... # currentIndexChanged(int)
        thresholdTypeChanged     : ClassVar[Signal] = ... # thresholdTypeChanged(ThresholdType)
        thresholdsChanged        : ClassVar[Signal] = ... # thresholdsChanged(QList<qreal>)
        volumeOverrideChanged    : ClassVar[Signal] = ... # volumeOverrideChanged(QLevelOfDetailBoundingSphere)

        class ThresholdType(enum.Enum):

            DistanceToCameraThreshold: Qt3DRender.QLevelOfDetail.ThresholdType = ... # 0x0
            ProjectedScreenPixelSizeThreshold: Qt3DRender.QLevelOfDetail.ThresholdType = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def camera(self) -> PySide6.Qt3DRender.Qt3DRender.QCamera: ...
        def createBoundingSphere(self, center: PySide6.QtGui.QVector3D, radius: float) -> PySide6.Qt3DRender.Qt3DRender.QLevelOfDetailBoundingSphere: ...
        def currentIndex(self) -> int: ...
        def setCamera(self, camera: PySide6.Qt3DRender.Qt3DRender.QCamera) -> None: ...
        def setCurrentIndex(self, currentIndex: int) -> None: ...
        def setThresholdType(self, thresholdType: PySide6.Qt3DRender.Qt3DRender.QLevelOfDetail.ThresholdType) -> None: ...
        def setThresholds(self, thresholds: Sequence[float]) -> None: ...
        def setVolumeOverride(self, volumeOverride: PySide6.Qt3DRender.Qt3DRender.QLevelOfDetailBoundingSphere) -> None: ...
        def thresholdType(self) -> PySide6.Qt3DRender.Qt3DRender.QLevelOfDetail.ThresholdType: ...
        def thresholds(self) -> List[float]: ...
        def volumeOverride(self) -> PySide6.Qt3DRender.Qt3DRender.QLevelOfDetailBoundingSphere: ...

    class QLevelOfDetailBoundingSphere(Shiboken.Object):

        @overload
        def __init__(self, center: PySide6.QtGui.QVector3D = ..., radius: float = ...) -> None: ...
        @overload
        def __init__(self, other: PySide6.Qt3DRender.Qt3DRender.QLevelOfDetailBoundingSphere) -> None: ...

        def center(self) -> PySide6.QtGui.QVector3D: ...
        def isEmpty(self) -> bool: ...
        def radius(self) -> float: ...

    class QLevelOfDetailSwitch(PySide6.Qt3DRender.Qt3DRender.QLevelOfDetail):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QLineWidth(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        smoothChanged            : ClassVar[Signal] = ... # smoothChanged(bool)
        valueChanged             : ClassVar[Signal] = ... # valueChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setSmooth(self, enabled: bool) -> None: ...
        def setValue(self, value: float) -> None: ...
        def smooth(self) -> bool: ...
        def value(self) -> float: ...

    class QMaterial(PySide6.Qt3DCore.Qt3DCore.QComponent):

        effectChanged            : ClassVar[Signal] = ... # effectChanged(QEffect*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addParameter(self, parameter: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def effect(self) -> PySide6.Qt3DRender.Qt3DRender.QEffect: ...
        def parameters(self) -> List[PySide6.Qt3DRender.Qt3DRender.QParameter]: ...
        def removeParameter(self, parameter: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def setEffect(self, effect: PySide6.Qt3DRender.Qt3DRender.QEffect) -> None: ...

    class QMemoryBarrier(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        waitOperationsChanged    : ClassVar[Signal] = ... # waitOperationsChanged(QMemoryBarrier::Operations)

        class Operation(enum.Flag):

            None_                    : Qt3DRender.QMemoryBarrier.Operation = ... # 0x0
            VertexAttributeArray     : Qt3DRender.QMemoryBarrier.Operation = ... # 0x1
            ElementArray             : Qt3DRender.QMemoryBarrier.Operation = ... # 0x2
            Uniform                  : Qt3DRender.QMemoryBarrier.Operation = ... # 0x4
            TextureFetch             : Qt3DRender.QMemoryBarrier.Operation = ... # 0x8
            ShaderImageAccess        : Qt3DRender.QMemoryBarrier.Operation = ... # 0x10
            Command                  : Qt3DRender.QMemoryBarrier.Operation = ... # 0x20
            PixelBuffer              : Qt3DRender.QMemoryBarrier.Operation = ... # 0x40
            TextureUpdate            : Qt3DRender.QMemoryBarrier.Operation = ... # 0x80
            BufferUpdate             : Qt3DRender.QMemoryBarrier.Operation = ... # 0x100
            FrameBuffer              : Qt3DRender.QMemoryBarrier.Operation = ... # 0x200
            TransformFeedback        : Qt3DRender.QMemoryBarrier.Operation = ... # 0x400
            AtomicCounter            : Qt3DRender.QMemoryBarrier.Operation = ... # 0x800
            ShaderStorage            : Qt3DRender.QMemoryBarrier.Operation = ... # 0x1000
            QueryBuffer              : Qt3DRender.QMemoryBarrier.Operation = ... # 0x2000
            All                      : Qt3DRender.QMemoryBarrier.Operation = ... # 0xffffffff


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setWaitOperations(self, operations: PySide6.Qt3DRender.Qt3DRender.QMemoryBarrier.Operation) -> None: ...
        def waitOperations(self) -> PySide6.Qt3DRender.Qt3DRender.QMemoryBarrier.Operation: ...

    class QMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        meshNameChanged          : ClassVar[Signal] = ... # meshNameChanged(QString)
        sourceChanged            : ClassVar[Signal] = ... # sourceChanged(QUrl)
        statusChanged            : ClassVar[Signal] = ... # statusChanged(Status)

        class Status(enum.Enum):

            None_                    : Qt3DRender.QMesh.Status = ... # 0x0
            Loading                  : Qt3DRender.QMesh.Status = ... # 0x1
            Ready                    : Qt3DRender.QMesh.Status = ... # 0x2
            Error                    : Qt3DRender.QMesh.Status = ... # 0x3


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def meshName(self) -> str: ...
        def setMeshName(self, meshName: str) -> None: ...
        def setSource(self, source: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def source(self) -> PySide6.QtCore.QUrl: ...
        def status(self) -> PySide6.Qt3DRender.Qt3DRender.QMesh.Status: ...

    class QMultiSampleAntiAliasing(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QNoDepthMask(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QNoDraw(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QNoPicking(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QObjectPicker(PySide6.Qt3DCore.Qt3DCore.QComponent):

        clicked                  : ClassVar[Signal] = ... # clicked(Qt3DRender::QPickEvent*)
        containsMouseChanged     : ClassVar[Signal] = ... # containsMouseChanged(bool)
        dragEnabledChanged       : ClassVar[Signal] = ... # dragEnabledChanged(bool)
        entered                  : ClassVar[Signal] = ... # entered()
        exited                   : ClassVar[Signal] = ... # exited()
        hoverEnabledChanged      : ClassVar[Signal] = ... # hoverEnabledChanged(bool)
        moved                    : ClassVar[Signal] = ... # moved(Qt3DRender::QPickEvent*)
        pressed                  : ClassVar[Signal] = ... # pressed(Qt3DRender::QPickEvent*)
        pressedChanged           : ClassVar[Signal] = ... # pressedChanged(bool)
        priorityChanged          : ClassVar[Signal] = ... # priorityChanged(int)
        released                 : ClassVar[Signal] = ... # released(Qt3DRender::QPickEvent*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def containsMouse(self) -> bool: ...
        def isDragEnabled(self) -> bool: ...
        def isHoverEnabled(self) -> bool: ...
        def isPressed(self) -> bool: ...
        def priority(self) -> int: ...
        def setDragEnabled(self, dragEnabled: bool) -> None: ...
        def setHoverEnabled(self, hoverEnabled: bool) -> None: ...
        def setPriority(self, priority: int) -> None: ...

    class QPaintedTextureImage(PySide6.Qt3DRender.Qt3DRender.QAbstractTextureImage):

        heightChanged            : ClassVar[Signal] = ... # heightChanged(int)
        sizeChanged              : ClassVar[Signal] = ... # sizeChanged(QSize)
        widthChanged             : ClassVar[Signal] = ... # widthChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def dataGenerator(self) -> Tuple[PySide6.Qt3DRender.Qt3DRender.QTextureImageDataGenerator]: ...
        def height(self) -> int: ...
        def paint(self, painter: PySide6.QtGui.QPainter) -> None: ...
        def setHeight(self, h: int) -> None: ...
        def setSize(self, size: PySide6.QtCore.QSize) -> None: ...
        def setWidth(self, w: int) -> None: ...
        def size(self) -> PySide6.QtCore.QSize: ...
        def update(self, rect: PySide6.QtCore.QRect = ...) -> None: ...
        def width(self) -> int: ...

    class QParameter(PySide6.Qt3DCore.Qt3DCore.QNode):

        nameChanged              : ClassVar[Signal] = ... # nameChanged(QString)
        valueChanged             : ClassVar[Signal] = ... # valueChanged(QVariant)

        @overload
        def __init__(self, name: str, texture: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...
        @overload
        def __init__(self, name: str, value: Any, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...
        @overload
        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def name(self) -> str: ...
        def setName(self, name: str) -> None: ...
        def setValue(self, dv: Any) -> None: ...
        def value(self) -> Any: ...

    class QPickEvent(PySide6.QtCore.QObject):

        acceptedChanged          : ClassVar[Signal] = ... # acceptedChanged(bool)

        class Buttons(enum.Enum):

            NoButton                 : Qt3DRender.QPickEvent.Buttons = ... # 0x0
            LeftButton               : Qt3DRender.QPickEvent.Buttons = ... # 0x1
            RightButton              : Qt3DRender.QPickEvent.Buttons = ... # 0x2
            MiddleButton             : Qt3DRender.QPickEvent.Buttons = ... # 0x4
            BackButton               : Qt3DRender.QPickEvent.Buttons = ... # 0x8


        class Modifiers(enum.Enum):

            NoModifier               : Qt3DRender.QPickEvent.Modifiers = ... # 0x0
            ShiftModifier            : Qt3DRender.QPickEvent.Modifiers = ... # 0x2000000
            ControlModifier          : Qt3DRender.QPickEvent.Modifiers = ... # 0x4000000
            AltModifier              : Qt3DRender.QPickEvent.Modifiers = ... # 0x8000000
            MetaModifier             : Qt3DRender.QPickEvent.Modifiers = ... # 0x10000000
            KeypadModifier           : Qt3DRender.QPickEvent.Modifiers = ... # 0x20000000


        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, position: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element], worldIntersection: PySide6.QtGui.QVector3D, localIntersection: PySide6.QtGui.QVector3D, distance: float) -> None: ...
        @overload
        def __init__(self, position: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element], worldIntersection: PySide6.QtGui.QVector3D, localIntersection: PySide6.QtGui.QVector3D, distance: float, button: PySide6.Qt3DRender.Qt3DRender.QPickEvent.Buttons, buttons: int, modifiers: int) -> None: ...

        def button(self) -> PySide6.Qt3DRender.Qt3DRender.QPickEvent.Buttons: ...
        def buttons(self) -> int: ...
        def distance(self) -> float: ...
        def entity(self) -> PySide6.Qt3DCore.Qt3DCore.QEntity: ...
        def isAccepted(self) -> bool: ...
        def localIntersection(self) -> PySide6.QtGui.QVector3D: ...
        def modifiers(self) -> int: ...
        def position(self) -> PySide6.QtCore.QPointF: ...
        def setAccepted(self, accepted: bool) -> None: ...
        def viewport(self) -> PySide6.Qt3DRender.Qt3DRender.QViewport: ...
        def worldIntersection(self) -> PySide6.QtGui.QVector3D: ...

    class QPickLineEvent(PySide6.Qt3DRender.Qt3DRender.QPickEvent):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, position: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element], worldIntersection: PySide6.QtGui.QVector3D, localIntersection: PySide6.QtGui.QVector3D, distance: float, edgeIndex: int, vertex1Index: int, vertex2Index: int, button: PySide6.Qt3DRender.Qt3DRender.QPickEvent.Buttons, buttons: int, modifiers: int) -> None: ...

        def edgeIndex(self) -> int: ...
        def vertex1Index(self) -> int: ...
        def vertex2Index(self) -> int: ...

    class QPickPointEvent(PySide6.Qt3DRender.Qt3DRender.QPickEvent):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, position: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element], worldIntersection: PySide6.QtGui.QVector3D, localIntersection: PySide6.QtGui.QVector3D, distance: float, pointIndex: int, button: PySide6.Qt3DRender.Qt3DRender.QPickEvent.Buttons, buttons: int, modifiers: int) -> None: ...

        def pointIndex(self) -> int: ...

    class QPickTriangleEvent(PySide6.Qt3DRender.Qt3DRender.QPickEvent):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, position: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element], worldIntersection: PySide6.QtGui.QVector3D, localIntersection: PySide6.QtGui.QVector3D, distance: float, triangleIndex: int, vertex1Index: int, vertex2Index: int, vertex3Index: int) -> None: ...
        @overload
        def __init__(self, position: Union[PySide6.QtCore.QPointF, PySide6.QtCore.QPoint, PySide6.QtGui.QPainterPath.Element], worldIntersection: PySide6.QtGui.QVector3D, localIntersection: PySide6.QtGui.QVector3D, distance: float, triangleIndex: int, vertex1Index: int, vertex2Index: int, vertex3Index: int, button: PySide6.Qt3DRender.Qt3DRender.QPickEvent.Buttons, buttons: int, modifiers: int, uvw: PySide6.QtGui.QVector3D) -> None: ...

        def triangleIndex(self) -> int: ...
        def uvw(self) -> PySide6.QtGui.QVector3D: ...
        def vertex1Index(self) -> int: ...
        def vertex2Index(self) -> int: ...
        def vertex3Index(self) -> int: ...

    class QPickingProxy(PySide6.Qt3DCore.Qt3DCore.QBoundingVolume):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QPickingSettings(PySide6.Qt3DCore.Qt3DCore.QNode):

        faceOrientationPickingModeChanged: ClassVar[Signal] = ... # faceOrientationPickingModeChanged(QPickingSettings::FaceOrientationPickingMode)
        pickMethodChanged        : ClassVar[Signal] = ... # pickMethodChanged(QPickingSettings::PickMethod)
        pickResultModeChanged    : ClassVar[Signal] = ... # pickResultModeChanged(QPickingSettings::PickResultMode)
        worldSpaceToleranceChanged: ClassVar[Signal] = ... # worldSpaceToleranceChanged(float)

        class FaceOrientationPickingMode(enum.Enum):

            FrontFace                : Qt3DRender.QPickingSettings.FaceOrientationPickingMode = ... # 0x1
            BackFace                 : Qt3DRender.QPickingSettings.FaceOrientationPickingMode = ... # 0x2
            FrontAndBackFace         : Qt3DRender.QPickingSettings.FaceOrientationPickingMode = ... # 0x3


        class PickMethod(enum.Enum):

            BoundingVolumePicking    : Qt3DRender.QPickingSettings.PickMethod = ... # 0x0
            TrianglePicking          : Qt3DRender.QPickingSettings.PickMethod = ... # 0x1
            LinePicking              : Qt3DRender.QPickingSettings.PickMethod = ... # 0x2
            PointPicking             : Qt3DRender.QPickingSettings.PickMethod = ... # 0x4
            PrimitivePicking         : Qt3DRender.QPickingSettings.PickMethod = ... # 0x7


        class PickResultMode(enum.Enum):

            NearestPick              : Qt3DRender.QPickingSettings.PickResultMode = ... # 0x0
            AllPicks                 : Qt3DRender.QPickingSettings.PickResultMode = ... # 0x1
            NearestPriorityPick      : Qt3DRender.QPickingSettings.PickResultMode = ... # 0x2


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def faceOrientationPickingMode(self) -> PySide6.Qt3DRender.Qt3DRender.QPickingSettings.FaceOrientationPickingMode: ...
        def pickMethod(self) -> PySide6.Qt3DRender.Qt3DRender.QPickingSettings.PickMethod: ...
        def pickResultMode(self) -> PySide6.Qt3DRender.Qt3DRender.QPickingSettings.PickResultMode: ...
        def setFaceOrientationPickingMode(self, faceOrientationPickingMode: PySide6.Qt3DRender.Qt3DRender.QPickingSettings.FaceOrientationPickingMode) -> None: ...
        def setPickMethod(self, pickMethod: PySide6.Qt3DRender.Qt3DRender.QPickingSettings.PickMethod) -> None: ...
        def setPickResultMode(self, pickResultMode: PySide6.Qt3DRender.Qt3DRender.QPickingSettings.PickResultMode) -> None: ...
        def setWorldSpaceTolerance(self, worldSpaceTolerance: float) -> None: ...
        def worldSpaceTolerance(self) -> float: ...

    class QPointLight(PySide6.Qt3DRender.Qt3DRender.QAbstractLight):

        constantAttenuationChanged: ClassVar[Signal] = ... # constantAttenuationChanged(float)
        linearAttenuationChanged : ClassVar[Signal] = ... # linearAttenuationChanged(float)
        quadraticAttenuationChanged: ClassVar[Signal] = ... # quadraticAttenuationChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def constantAttenuation(self) -> float: ...
        def linearAttenuation(self) -> float: ...
        def quadraticAttenuation(self) -> float: ...
        def setConstantAttenuation(self, value: float) -> None: ...
        def setLinearAttenuation(self, value: float) -> None: ...
        def setQuadraticAttenuation(self, value: float) -> None: ...

    class QPointSize(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        sizeModeChanged          : ClassVar[Signal] = ... # sizeModeChanged(SizeMode)
        valueChanged             : ClassVar[Signal] = ... # valueChanged(float)

        class SizeMode(enum.Enum):

            Fixed                    : Qt3DRender.QPointSize.SizeMode = ... # 0x0
            Programmable             : Qt3DRender.QPointSize.SizeMode = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setSizeMode(self, sizeMode: PySide6.Qt3DRender.Qt3DRender.QPointSize.SizeMode) -> None: ...
        def setValue(self, value: float) -> None: ...
        def sizeMode(self) -> PySide6.Qt3DRender.Qt3DRender.QPointSize.SizeMode: ...
        def value(self) -> float: ...

    class QPolygonOffset(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        depthStepsChanged        : ClassVar[Signal] = ... # depthStepsChanged(float)
        scaleFactorChanged       : ClassVar[Signal] = ... # scaleFactorChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def depthSteps(self) -> float: ...
        def scaleFactor(self) -> float: ...
        def setDepthSteps(self, depthSteps: float) -> None: ...
        def setScaleFactor(self, scaleFactor: float) -> None: ...

    class QProximityFilter(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        distanceThresholdChanged : ClassVar[Signal] = ... # distanceThresholdChanged(float)
        entityChanged            : ClassVar[Signal] = ... # entityChanged(Qt3DCore::QEntity*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def distanceThreshold(self) -> float: ...
        def entity(self) -> PySide6.Qt3DCore.Qt3DCore.QEntity: ...
        def setDistanceThreshold(self, distanceThreshold: float) -> None: ...
        def setEntity(self, entity: PySide6.Qt3DCore.Qt3DCore.QEntity) -> None: ...

    class QRasterMode(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        faceModeChanged          : ClassVar[Signal] = ... # faceModeChanged(FaceMode)
        rasterModeChanged        : ClassVar[Signal] = ... # rasterModeChanged(RasterMode)

        class FaceMode(enum.Enum):

            Front                    : Qt3DRender.QRasterMode.FaceMode = ... # 0x404
            Back                     : Qt3DRender.QRasterMode.FaceMode = ... # 0x405
            FrontAndBack             : Qt3DRender.QRasterMode.FaceMode = ... # 0x408


        class RasterMode(enum.Enum):

            Points                   : Qt3DRender.QRasterMode.RasterMode = ... # 0x1b00
            Lines                    : Qt3DRender.QRasterMode.RasterMode = ... # 0x1b01
            Fill                     : Qt3DRender.QRasterMode.RasterMode = ... # 0x1b02


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def faceMode(self) -> PySide6.Qt3DRender.Qt3DRender.QRasterMode.FaceMode: ...
        def rasterMode(self) -> PySide6.Qt3DRender.Qt3DRender.QRasterMode.RasterMode: ...
        def setFaceMode(self, faceMode: PySide6.Qt3DRender.Qt3DRender.QRasterMode.FaceMode) -> None: ...
        def setRasterMode(self, rasterMode: PySide6.Qt3DRender.Qt3DRender.QRasterMode.RasterMode) -> None: ...

    class QRayCaster(PySide6.Qt3DRender.Qt3DRender.QAbstractRayCaster):

        directionChanged         : ClassVar[Signal] = ... # directionChanged(QVector3D)
        lengthChanged            : ClassVar[Signal] = ... # lengthChanged(float)
        originChanged            : ClassVar[Signal] = ... # originChanged(QVector3D)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def direction(self) -> PySide6.QtGui.QVector3D: ...
        def length(self) -> float: ...
        def origin(self) -> PySide6.QtGui.QVector3D: ...
        def pick(self, origin: PySide6.QtGui.QVector3D, direction: PySide6.QtGui.QVector3D, length: float) -> List[PySide6.Qt3DRender.Qt3DRender.QRayCasterHit]: ...
        def setDirection(self, direction: PySide6.QtGui.QVector3D) -> None: ...
        def setLength(self, length: float) -> None: ...
        def setOrigin(self, origin: PySide6.QtGui.QVector3D) -> None: ...
        @overload
        def trigger(self) -> None: ...
        @overload
        def trigger(self, origin: PySide6.QtGui.QVector3D, direction: PySide6.QtGui.QVector3D, length: float) -> None: ...

    class QRayCasterHit(Shiboken.Object):

        class HitType(enum.Enum):

            TriangleHit              : Qt3DRender.QRayCasterHit.HitType = ... # 0x0
            LineHit                  : Qt3DRender.QRayCasterHit.HitType = ... # 0x1
            PointHit                 : Qt3DRender.QRayCasterHit.HitType = ... # 0x2
            EntityHit                : Qt3DRender.QRayCasterHit.HitType = ... # 0x3


        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, other: PySide6.Qt3DRender.Qt3DRender.QRayCasterHit) -> None: ...
        @overload
        def __init__(self, type: PySide6.Qt3DRender.Qt3DRender.QRayCasterHit.HitType, id: PySide6.Qt3DCore.Qt3DCore.QNodeId, distance: float, localIntersect: PySide6.QtGui.QVector3D, worldIntersect: PySide6.QtGui.QVector3D, primitiveIndex: int, v1: int, v2: int, v3: int) -> None: ...

        @staticmethod
        def __copy__() -> None: ...
        def distance(self) -> float: ...
        def entity(self) -> PySide6.Qt3DCore.Qt3DCore.QEntity: ...
        def entityId(self) -> PySide6.Qt3DCore.Qt3DCore.QNodeId: ...
        def localIntersection(self) -> PySide6.QtGui.QVector3D: ...
        def primitiveIndex(self) -> int: ...
        def toString(self) -> str: ...
        def type(self) -> PySide6.Qt3DRender.Qt3DRender.QRayCasterHit.HitType: ...
        def vertex1Index(self) -> int: ...
        def vertex2Index(self) -> int: ...
        def vertex3Index(self) -> int: ...
        def worldIntersection(self) -> PySide6.QtGui.QVector3D: ...

    class QRenderAspect(PySide6.Qt3DCore.Qt3DCore.QAbstractAspect):

        class SubmissionType(enum.Enum):

            Automatic                : Qt3DRender.QRenderAspect.SubmissionType = ... # 0x0
            Manual                   : Qt3DRender.QRenderAspect.SubmissionType = ... # 0x1


        @overload
        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
        @overload
        def __init__(self, submissionType: PySide6.Qt3DRender.Qt3DRender.QRenderAspect.SubmissionType, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def dependencies(self) -> List[str]: ...

    class QRenderCapabilities(PySide6.QtCore.QObject):

        class API(enum.Enum):

            OpenGL                   : Qt3DRender.QRenderCapabilities.API = ... # 0x1
            OpenGLES                 : Qt3DRender.QRenderCapabilities.API = ... # 0x2
            Vulkan                   : Qt3DRender.QRenderCapabilities.API = ... # 0x3
            DirectX                  : Qt3DRender.QRenderCapabilities.API = ... # 0x4
            RHI                      : Qt3DRender.QRenderCapabilities.API = ... # 0x5


        class Profile(enum.Enum):

            NoProfile                : Qt3DRender.QRenderCapabilities.Profile = ... # 0x0
            CoreProfile              : Qt3DRender.QRenderCapabilities.Profile = ... # 0x1
            CompatibilityProfile     : Qt3DRender.QRenderCapabilities.Profile = ... # 0x2


        def __init__(self, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def api(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderCapabilities.API: ...
        def driverVersion(self) -> str: ...
        def extensions(self) -> List[str]: ...
        def glslVersion(self) -> str: ...
        def isValid(self) -> bool: ...
        def majorVersion(self) -> int: ...
        def maxComputeInvocations(self) -> int: ...
        def maxComputeSharedMemorySize(self) -> int: ...
        def maxImageUnits(self) -> int: ...
        def maxSSBOBindings(self) -> int: ...
        def maxSSBOSize(self) -> int: ...
        def maxSamples(self) -> int: ...
        def maxTextureLayers(self) -> int: ...
        def maxTextureSize(self) -> int: ...
        def maxTextureUnits(self) -> int: ...
        def maxUBOBindings(self) -> int: ...
        def maxUBOSize(self) -> int: ...
        def maxWorkGroupCountX(self) -> int: ...
        def maxWorkGroupCountY(self) -> int: ...
        def maxWorkGroupCountZ(self) -> int: ...
        def maxWorkGroupSizeX(self) -> int: ...
        def maxWorkGroupSizeY(self) -> int: ...
        def maxWorkGroupSizeZ(self) -> int: ...
        def minorVersion(self) -> int: ...
        def profile(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderCapabilities.Profile: ...
        def renderer(self) -> str: ...
        def supportsCompute(self) -> bool: ...
        def supportsImageStore(self) -> bool: ...
        def supportsSSBO(self) -> bool: ...
        def supportsUBO(self) -> bool: ...
        def vendor(self) -> str: ...

    class QRenderCapture(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        @overload
        def requestCapture(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderCaptureReply: ...
        @overload
        def requestCapture(self, captureId: int) -> PySide6.Qt3DRender.Qt3DRender.QRenderCaptureReply: ...
        @overload
        def requestCapture(self, rect: PySide6.QtCore.QRect) -> PySide6.Qt3DRender.Qt3DRender.QRenderCaptureReply: ...

    class QRenderCaptureReply(PySide6.QtCore.QObject):

        completed                : ClassVar[Signal] = ... # completed()
        def captureId(self) -> int: ...
        def image(self) -> PySide6.QtGui.QImage: ...
        def isComplete(self) -> bool: ...
        def saveImage(self, fileName: str) -> bool: ...

    class QRenderPass(PySide6.Qt3DCore.Qt3DCore.QNode):

        shaderProgramChanged     : ClassVar[Signal] = ... # shaderProgramChanged(QShaderProgram*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addFilterKey(self, filterKey: PySide6.Qt3DRender.Qt3DRender.QFilterKey) -> None: ...
        def addParameter(self, p: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def addRenderState(self, state: PySide6.Qt3DRender.Qt3DRender.QRenderState) -> None: ...
        def filterKeys(self) -> List[PySide6.Qt3DRender.Qt3DRender.QFilterKey]: ...
        def parameters(self) -> List[PySide6.Qt3DRender.Qt3DRender.QParameter]: ...
        def removeFilterKey(self, filterKey: PySide6.Qt3DRender.Qt3DRender.QFilterKey) -> None: ...
        def removeParameter(self, p: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def removeRenderState(self, state: PySide6.Qt3DRender.Qt3DRender.QRenderState) -> None: ...
        def renderStates(self) -> List[PySide6.Qt3DRender.Qt3DRender.QRenderState]: ...
        def setShaderProgram(self, shaderProgram: PySide6.Qt3DRender.Qt3DRender.QShaderProgram) -> None: ...
        def shaderProgram(self) -> PySide6.Qt3DRender.Qt3DRender.QShaderProgram: ...

    class QRenderPassFilter(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addMatch(self, filterKey: PySide6.Qt3DRender.Qt3DRender.QFilterKey) -> None: ...
        def addParameter(self, parameter: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def matchAny(self) -> List[PySide6.Qt3DRender.Qt3DRender.QFilterKey]: ...
        def parameters(self) -> List[PySide6.Qt3DRender.Qt3DRender.QParameter]: ...
        def removeMatch(self, filterKey: PySide6.Qt3DRender.Qt3DRender.QFilterKey) -> None: ...
        def removeParameter(self, parameter: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...

    class QRenderSettings(PySide6.Qt3DCore.Qt3DCore.QComponent):

        activeFrameGraphChanged  : ClassVar[Signal] = ... # activeFrameGraphChanged(QFrameGraphNode*)
        renderPolicyChanged      : ClassVar[Signal] = ... # renderPolicyChanged(RenderPolicy)

        class RenderPolicy(enum.Enum):

            OnDemand                 : Qt3DRender.QRenderSettings.RenderPolicy = ... # 0x0
            Always                   : Qt3DRender.QRenderSettings.RenderPolicy = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def activeFrameGraph(self) -> PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode: ...
        def pickingSettings(self) -> PySide6.Qt3DRender.Qt3DRender.QPickingSettings: ...
        def renderCapabilities(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderCapabilities: ...
        def renderPolicy(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderSettings.RenderPolicy: ...
        def setActiveFrameGraph(self, activeFrameGraph: PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode) -> None: ...
        def setRenderPolicy(self, renderPolicy: PySide6.Qt3DRender.Qt3DRender.QRenderSettings.RenderPolicy) -> None: ...

    class QRenderState(PySide6.Qt3DCore.Qt3DCore.QNode): ...

    class QRenderStateSet(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addRenderState(self, state: PySide6.Qt3DRender.Qt3DRender.QRenderState) -> None: ...
        def removeRenderState(self, state: PySide6.Qt3DRender.Qt3DRender.QRenderState) -> None: ...
        def renderStates(self) -> List[PySide6.Qt3DRender.Qt3DRender.QRenderState]: ...

    class QRenderSurfaceSelector(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        externalRenderTargetSizeChanged: ClassVar[Signal] = ... # externalRenderTargetSizeChanged(QSize)
        surfaceChanged           : ClassVar[Signal] = ... # surfaceChanged(QObject*)
        surfacePixelRatioChanged : ClassVar[Signal] = ... # surfacePixelRatioChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def externalRenderTargetSize(self) -> PySide6.QtCore.QSize: ...
        def setExternalRenderTargetSize(self, size: PySide6.QtCore.QSize) -> None: ...
        def setSurface(self, surfaceObject: PySide6.QtCore.QObject) -> None: ...
        def setSurfacePixelRatio(self, ratio: float) -> None: ...
        def surface(self) -> PySide6.QtCore.QObject: ...
        def surfacePixelRatio(self) -> float: ...

    class QRenderTarget(PySide6.Qt3DCore.Qt3DCore.QComponent):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addOutput(self, output: PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput) -> None: ...
        def outputs(self) -> List[PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput]: ...
        def removeOutput(self, output: PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput) -> None: ...

    class QRenderTargetOutput(PySide6.Qt3DCore.Qt3DCore.QNode):

        attachmentPointChanged   : ClassVar[Signal] = ... # attachmentPointChanged(AttachmentPoint)
        faceChanged              : ClassVar[Signal] = ... # faceChanged(QAbstractTexture::CubeMapFace)
        layerChanged             : ClassVar[Signal] = ... # layerChanged(int)
        mipLevelChanged          : ClassVar[Signal] = ... # mipLevelChanged(int)
        textureChanged           : ClassVar[Signal] = ... # textureChanged(QAbstractTexture*)

        class AttachmentPoint(enum.Enum):

            Color0                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x0
            Color1                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x1
            Color2                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x2
            Color3                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x3
            Color4                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x4
            Color5                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x5
            Color6                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x6
            Color7                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x7
            Color8                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x8
            Color9                   : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x9
            Color10                  : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0xa
            Color11                  : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0xb
            Color12                  : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0xc
            Color13                  : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0xd
            Color14                  : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0xe
            Color15                  : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0xf
            Depth                    : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x10
            Stencil                  : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x11
            DepthStencil             : Qt3DRender.QRenderTargetOutput.AttachmentPoint = ... # 0x12


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def attachmentPoint(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput.AttachmentPoint: ...
        def face(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.CubeMapFace: ...
        def layer(self) -> int: ...
        def mipLevel(self) -> int: ...
        def setAttachmentPoint(self, attachmentPoint: PySide6.Qt3DRender.Qt3DRender.QRenderTargetOutput.AttachmentPoint) -> None: ...
        def setFace(self, face: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.CubeMapFace) -> None: ...
        def setLayer(self, layer: int) -> None: ...
        def setMipLevel(self, level: int) -> None: ...
        def setTexture(self, texture: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def texture(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...

    class QRenderTargetSelector(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        targetChanged            : ClassVar[Signal] = ... # targetChanged(QRenderTarget*)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setTarget(self, target: PySide6.Qt3DRender.Qt3DRender.QRenderTarget) -> None: ...
        def target(self) -> PySide6.Qt3DRender.Qt3DRender.QRenderTarget: ...

    class QSceneLoader(PySide6.Qt3DCore.Qt3DCore.QComponent):

        sourceChanged            : ClassVar[Signal] = ... # sourceChanged(QUrl)
        statusChanged            : ClassVar[Signal] = ... # statusChanged(Status)

        class ComponentType(enum.Enum):

            UnknownComponent         : Qt3DRender.QSceneLoader.ComponentType = ... # 0x0
            GeometryRendererComponent: Qt3DRender.QSceneLoader.ComponentType = ... # 0x1
            TransformComponent       : Qt3DRender.QSceneLoader.ComponentType = ... # 0x2
            MaterialComponent        : Qt3DRender.QSceneLoader.ComponentType = ... # 0x3
            LightComponent           : Qt3DRender.QSceneLoader.ComponentType = ... # 0x4
            CameraLensComponent      : Qt3DRender.QSceneLoader.ComponentType = ... # 0x5


        class Status(enum.Enum):

            None_                    : Qt3DRender.QSceneLoader.Status = ... # 0x0
            Loading                  : Qt3DRender.QSceneLoader.Status = ... # 0x1
            Ready                    : Qt3DRender.QSceneLoader.Status = ... # 0x2
            Error                    : Qt3DRender.QSceneLoader.Status = ... # 0x3


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def component(self, entityName: str, componentType: PySide6.Qt3DRender.Qt3DRender.QSceneLoader.ComponentType) -> PySide6.Qt3DCore.Qt3DCore.QComponent: ...
        def entity(self, entityName: str) -> PySide6.Qt3DCore.Qt3DCore.QEntity: ...
        def entityNames(self) -> List[str]: ...
        def setSource(self, arg: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def source(self) -> PySide6.QtCore.QUrl: ...
        def status(self) -> PySide6.Qt3DRender.Qt3DRender.QSceneLoader.Status: ...

    class QScissorTest(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        bottomChanged            : ClassVar[Signal] = ... # bottomChanged(int)
        heightChanged            : ClassVar[Signal] = ... # heightChanged(int)
        leftChanged              : ClassVar[Signal] = ... # leftChanged(int)
        widthChanged             : ClassVar[Signal] = ... # widthChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def bottom(self) -> int: ...
        def height(self) -> int: ...
        def left(self) -> int: ...
        def setBottom(self, bottom: int) -> None: ...
        def setHeight(self, height: int) -> None: ...
        def setLeft(self, left: int) -> None: ...
        def setWidth(self, width: int) -> None: ...
        def width(self) -> int: ...

    class QScreenRayCaster(PySide6.Qt3DRender.Qt3DRender.QAbstractRayCaster):

        positionChanged          : ClassVar[Signal] = ... # positionChanged(QPoint)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def pick(self, position: PySide6.QtCore.QPoint) -> List[PySide6.Qt3DRender.Qt3DRender.QRayCasterHit]: ...
        def position(self) -> PySide6.QtCore.QPoint: ...
        def setPosition(self, position: PySide6.QtCore.QPoint) -> None: ...
        @overload
        def trigger(self) -> None: ...
        @overload
        def trigger(self, position: PySide6.QtCore.QPoint) -> None: ...

    class QSeamlessCubemap(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QSetFence(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        handleChanged            : ClassVar[Signal] = ... # handleChanged(QVariant)
        handleTypeChanged        : ClassVar[Signal] = ... # handleTypeChanged(HandleType)

        class HandleType(enum.Enum):

            NoHandle                 : Qt3DRender.QSetFence.HandleType = ... # 0x0
            OpenGLFenceId            : Qt3DRender.QSetFence.HandleType = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def handle(self) -> Any: ...
        def handleType(self) -> PySide6.Qt3DRender.Qt3DRender.QSetFence.HandleType: ...

    class QShaderData(PySide6.Qt3DCore.Qt3DCore.QComponent):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def event(self, event: PySide6.QtCore.QEvent) -> bool: ...
        def propertyReader(self) -> Tuple[PySide6.Qt3DRender.Qt3DRender.PropertyReaderInterface]: ...

    class QShaderImage(PySide6.Qt3DCore.Qt3DCore.QNode):

        accessChanged            : ClassVar[Signal] = ... # accessChanged(Access)
        formatChanged            : ClassVar[Signal] = ... # formatChanged(ImageFormat)
        layerChanged             : ClassVar[Signal] = ... # layerChanged(int)
        layeredChanged           : ClassVar[Signal] = ... # layeredChanged(bool)
        mipLevelChanged          : ClassVar[Signal] = ... # mipLevelChanged(int)
        textureChanged           : ClassVar[Signal] = ... # textureChanged(Qt3DRender::QAbstractTexture*)

        class Access(enum.Enum):

            ReadOnly                 : Qt3DRender.QShaderImage.Access = ... # 0x0
            WriteOnly                : Qt3DRender.QShaderImage.Access = ... # 0x1
            ReadWrite                : Qt3DRender.QShaderImage.Access = ... # 0x2


        class ImageFormat(enum.Enum):

            NoFormat                 : Qt3DRender.QShaderImage.ImageFormat = ... # 0x0
            Automatic                : Qt3DRender.QShaderImage.ImageFormat = ... # 0x1
            RGBA8_UNorm              : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8058
            RGB10A2                  : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8059
            RGBA16_UNorm             : Qt3DRender.QShaderImage.ImageFormat = ... # 0x805b
            R8_UNorm                 : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8229
            R16_UNorm                : Qt3DRender.QShaderImage.ImageFormat = ... # 0x822a
            RG8_UNorm                : Qt3DRender.QShaderImage.ImageFormat = ... # 0x822b
            RG16_UNorm               : Qt3DRender.QShaderImage.ImageFormat = ... # 0x822c
            R16F                     : Qt3DRender.QShaderImage.ImageFormat = ... # 0x822d
            R32F                     : Qt3DRender.QShaderImage.ImageFormat = ... # 0x822e
            RG16F                    : Qt3DRender.QShaderImage.ImageFormat = ... # 0x822f
            RG32F                    : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8230
            R8I                      : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8231
            R8U                      : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8232
            R16I                     : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8233
            R16U                     : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8234
            R32I                     : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8235
            R32U                     : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8236
            RG8I                     : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8237
            RG8U                     : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8238
            RG16I                    : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8239
            RG16U                    : Qt3DRender.QShaderImage.ImageFormat = ... # 0x823a
            RG32I                    : Qt3DRender.QShaderImage.ImageFormat = ... # 0x823b
            RG32U                    : Qt3DRender.QShaderImage.ImageFormat = ... # 0x823c
            RGBA32F                  : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8814
            RGBA16F                  : Qt3DRender.QShaderImage.ImageFormat = ... # 0x881a
            RG11B10F                 : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8c3a
            RGBA32U                  : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8d70
            RGBA16U                  : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8d76
            RGBA8U                   : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8d7c
            RGBA32I                  : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8d82
            RGBA16I                  : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8d88
            RGBA8I                   : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8d8e
            R8_SNorm                 : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8f94
            RG8_SNorm                : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8f95
            RGBA8_SNorm              : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8f97
            R16_SNorm                : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8f98
            RG16_SNorm               : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8f99
            RGBA16_SNorm             : Qt3DRender.QShaderImage.ImageFormat = ... # 0x8f9b
            RGB10A2U                 : Qt3DRender.QShaderImage.ImageFormat = ... # 0x906f


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def access(self) -> PySide6.Qt3DRender.Qt3DRender.QShaderImage.Access: ...
        def format(self) -> PySide6.Qt3DRender.Qt3DRender.QShaderImage.ImageFormat: ...
        def layer(self) -> int: ...
        def layered(self) -> bool: ...
        def mipLevel(self) -> int: ...
        def setAccess(self, access: PySide6.Qt3DRender.Qt3DRender.QShaderImage.Access) -> None: ...
        def setFormat(self, format: PySide6.Qt3DRender.Qt3DRender.QShaderImage.ImageFormat) -> None: ...
        def setLayer(self, layer: int) -> None: ...
        def setLayered(self, layered: bool) -> None: ...
        def setMipLevel(self, mipLevel: int) -> None: ...
        def setTexture(self, texture: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture) -> None: ...
        def texture(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...

    class QShaderProgram(PySide6.Qt3DCore.Qt3DCore.QNode):

        computeShaderCodeChanged : ClassVar[Signal] = ... # computeShaderCodeChanged(QByteArray)
        formatChanged            : ClassVar[Signal] = ... # formatChanged(Format)
        fragmentShaderCodeChanged: ClassVar[Signal] = ... # fragmentShaderCodeChanged(QByteArray)
        geometryShaderCodeChanged: ClassVar[Signal] = ... # geometryShaderCodeChanged(QByteArray)
        logChanged               : ClassVar[Signal] = ... # logChanged(QString)
        statusChanged            : ClassVar[Signal] = ... # statusChanged(Status)
        tessellationControlShaderCodeChanged: ClassVar[Signal] = ... # tessellationControlShaderCodeChanged(QByteArray)
        tessellationEvaluationShaderCodeChanged: ClassVar[Signal] = ... # tessellationEvaluationShaderCodeChanged(QByteArray)
        vertexShaderCodeChanged  : ClassVar[Signal] = ... # vertexShaderCodeChanged(QByteArray)

        class Format(enum.Enum):

            GLSL                     : Qt3DRender.QShaderProgram.Format = ... # 0x0
            SPIRV                    : Qt3DRender.QShaderProgram.Format = ... # 0x1


        class ShaderType(enum.Enum):

            Vertex                   : Qt3DRender.QShaderProgram.ShaderType = ... # 0x0
            Fragment                 : Qt3DRender.QShaderProgram.ShaderType = ... # 0x1
            TessellationControl      : Qt3DRender.QShaderProgram.ShaderType = ... # 0x2
            TessellationEvaluation   : Qt3DRender.QShaderProgram.ShaderType = ... # 0x3
            Geometry                 : Qt3DRender.QShaderProgram.ShaderType = ... # 0x4
            Compute                  : Qt3DRender.QShaderProgram.ShaderType = ... # 0x5


        class Status(enum.Enum):

            NotReady                 : Qt3DRender.QShaderProgram.Status = ... # 0x0
            Ready                    : Qt3DRender.QShaderProgram.Status = ... # 0x1
            Error                    : Qt3DRender.QShaderProgram.Status = ... # 0x2


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def computeShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def format(self) -> PySide6.Qt3DRender.Qt3DRender.QShaderProgram.Format: ...
        def fragmentShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def geometryShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        @staticmethod
        def loadSource(sourceUrl: Union[PySide6.QtCore.QUrl, str]) -> PySide6.QtCore.QByteArray: ...
        def log(self) -> str: ...
        def setComputeShaderCode(self, computeShaderCode: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
        def setFormat(self, format: PySide6.Qt3DRender.Qt3DRender.QShaderProgram.Format) -> None: ...
        def setFragmentShaderCode(self, fragmentShaderCode: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
        def setGeometryShaderCode(self, geometryShaderCode: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
        def setShaderCode(self, type: PySide6.Qt3DRender.Qt3DRender.QShaderProgram.ShaderType, shaderCode: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
        def setTessellationControlShaderCode(self, tessellationControlShaderCode: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
        def setTessellationEvaluationShaderCode(self, tessellationEvaluationShaderCode: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
        def setVertexShaderCode(self, vertexShaderCode: Union[PySide6.QtCore.QByteArray, bytes]) -> None: ...
        def shaderCode(self, type: PySide6.Qt3DRender.Qt3DRender.QShaderProgram.ShaderType) -> PySide6.QtCore.QByteArray: ...
        def status(self) -> PySide6.Qt3DRender.Qt3DRender.QShaderProgram.Status: ...
        def tessellationControlShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def tessellationEvaluationShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def vertexShaderCode(self) -> PySide6.QtCore.QByteArray: ...

    class QShaderProgramBuilder(PySide6.Qt3DCore.Qt3DCore.QNode):

        computeShaderCodeChanged : ClassVar[Signal] = ... # computeShaderCodeChanged(QByteArray)
        computeShaderGraphChanged: ClassVar[Signal] = ... # computeShaderGraphChanged(QUrl)
        enabledLayersChanged     : ClassVar[Signal] = ... # enabledLayersChanged(QStringList)
        fragmentShaderCodeChanged: ClassVar[Signal] = ... # fragmentShaderCodeChanged(QByteArray)
        fragmentShaderGraphChanged: ClassVar[Signal] = ... # fragmentShaderGraphChanged(QUrl)
        geometryShaderCodeChanged: ClassVar[Signal] = ... # geometryShaderCodeChanged(QByteArray)
        geometryShaderGraphChanged: ClassVar[Signal] = ... # geometryShaderGraphChanged(QUrl)
        shaderProgramChanged     : ClassVar[Signal] = ... # shaderProgramChanged(Qt3DRender::QShaderProgram*)
        tessellationControlShaderCodeChanged: ClassVar[Signal] = ... # tessellationControlShaderCodeChanged(QByteArray)
        tessellationControlShaderGraphChanged: ClassVar[Signal] = ... # tessellationControlShaderGraphChanged(QUrl)
        tessellationEvaluationShaderCodeChanged: ClassVar[Signal] = ... # tessellationEvaluationShaderCodeChanged(QByteArray)
        tessellationEvaluationShaderGraphChanged: ClassVar[Signal] = ... # tessellationEvaluationShaderGraphChanged(QUrl)
        vertexShaderCodeChanged  : ClassVar[Signal] = ... # vertexShaderCodeChanged(QByteArray)
        vertexShaderGraphChanged : ClassVar[Signal] = ... # vertexShaderGraphChanged(QUrl)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def computeShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def computeShaderGraph(self) -> PySide6.QtCore.QUrl: ...
        def enabledLayers(self) -> List[str]: ...
        def fragmentShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def fragmentShaderGraph(self) -> PySide6.QtCore.QUrl: ...
        def geometryShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def geometryShaderGraph(self) -> PySide6.QtCore.QUrl: ...
        def setComputeShaderGraph(self, computeShaderGraph: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def setEnabledLayers(self, layers: Sequence[str]) -> None: ...
        def setFragmentShaderGraph(self, fragmentShaderGraph: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def setGeometryShaderGraph(self, geometryShaderGraph: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def setShaderProgram(self, program: PySide6.Qt3DRender.Qt3DRender.QShaderProgram) -> None: ...
        def setTessellationControlShaderGraph(self, tessellationControlShaderGraph: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def setTessellationEvaluationShaderGraph(self, tessellationEvaluationShaderGraph: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def setVertexShaderGraph(self, vertexShaderGraph: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def shaderProgram(self) -> PySide6.Qt3DRender.Qt3DRender.QShaderProgram: ...
        def tessellationControlShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def tessellationControlShaderGraph(self) -> PySide6.QtCore.QUrl: ...
        def tessellationEvaluationShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def tessellationEvaluationShaderGraph(self) -> PySide6.QtCore.QUrl: ...
        def vertexShaderCode(self) -> PySide6.QtCore.QByteArray: ...
        def vertexShaderGraph(self) -> PySide6.QtCore.QUrl: ...

    class QSharedGLTexture(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        textureIdChanged         : ClassVar[Signal] = ... # textureIdChanged(int)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def setTextureId(self, id: int) -> None: ...
        def textureId(self) -> int: ...

    class QSortPolicy(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        sortTypesChanged         : ClassVar[Signal] = ... # sortTypesChanged(QList<SortType>)

        class SortType(enum.Enum):

            StateChangeCost          : Qt3DRender.QSortPolicy.SortType = ... # 0x1
            BackToFront              : Qt3DRender.QSortPolicy.SortType = ... # 0x2
            Material                 : Qt3DRender.QSortPolicy.SortType = ... # 0x4
            FrontToBack              : Qt3DRender.QSortPolicy.SortType = ... # 0x8
            Texture                  : Qt3DRender.QSortPolicy.SortType = ... # 0x10
            Uniform                  : Qt3DRender.QSortPolicy.SortType = ... # 0x20


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        @overload
        def setSortTypes(self, sortTypes: Sequence[PySide6.Qt3DRender.Qt3DRender.QSortPolicy.SortType]) -> None: ...
        @overload
        def setSortTypes(self, sortTypesInt: Sequence[int]) -> None: ...
        def sortTypes(self) -> List[PySide6.Qt3DRender.Qt3DRender.QSortPolicy.SortType]: ...
        def sortTypesInt(self) -> List[int]: ...

    class QSpotLight(PySide6.Qt3DRender.Qt3DRender.QAbstractLight):

        constantAttenuationChanged: ClassVar[Signal] = ... # constantAttenuationChanged(float)
        cutOffAngleChanged       : ClassVar[Signal] = ... # cutOffAngleChanged(float)
        linearAttenuationChanged : ClassVar[Signal] = ... # linearAttenuationChanged(float)
        localDirectionChanged    : ClassVar[Signal] = ... # localDirectionChanged(QVector3D)
        quadraticAttenuationChanged: ClassVar[Signal] = ... # quadraticAttenuationChanged(float)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def constantAttenuation(self) -> float: ...
        def cutOffAngle(self) -> float: ...
        def linearAttenuation(self) -> float: ...
        def localDirection(self) -> PySide6.QtGui.QVector3D: ...
        def quadraticAttenuation(self) -> float: ...
        def setConstantAttenuation(self, value: float) -> None: ...
        def setCutOffAngle(self, cutOffAngle: float) -> None: ...
        def setLinearAttenuation(self, value: float) -> None: ...
        def setLocalDirection(self, localDirection: PySide6.QtGui.QVector3D) -> None: ...
        def setQuadraticAttenuation(self, value: float) -> None: ...

    class QStencilMask(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        backOutputMaskChanged    : ClassVar[Signal] = ... # backOutputMaskChanged(uint)
        frontOutputMaskChanged   : ClassVar[Signal] = ... # frontOutputMaskChanged(uint)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def backOutputMask(self) -> int: ...
        def frontOutputMask(self) -> int: ...
        def setBackOutputMask(self, backOutputMask: int) -> None: ...
        def setFrontOutputMask(self, frontOutputMask: int) -> None: ...

    class QStencilOperation(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def back(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilOperationArguments: ...
        def front(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilOperationArguments: ...

    class QStencilOperationArguments(PySide6.QtCore.QObject):

        allTestsPassOperationChanged: ClassVar[Signal] = ... # allTestsPassOperationChanged(Operation)
        depthTestFailureOperationChanged: ClassVar[Signal] = ... # depthTestFailureOperationChanged(Operation)
        faceModeChanged          : ClassVar[Signal] = ... # faceModeChanged(FaceMode)
        stencilTestFailureOperationChanged: ClassVar[Signal] = ... # stencilTestFailureOperationChanged(Operation)

        class FaceMode(enum.Enum):

            Front                    : Qt3DRender.QStencilOperationArguments.FaceMode = ... # 0x404
            Back                     : Qt3DRender.QStencilOperationArguments.FaceMode = ... # 0x405
            FrontAndBack             : Qt3DRender.QStencilOperationArguments.FaceMode = ... # 0x408


        class Operation(enum.Enum):

            Zero                     : Qt3DRender.QStencilOperationArguments.Operation = ... # 0x0
            Invert                   : Qt3DRender.QStencilOperationArguments.Operation = ... # 0x150a
            Keep                     : Qt3DRender.QStencilOperationArguments.Operation = ... # 0x1e00
            Replace                  : Qt3DRender.QStencilOperationArguments.Operation = ... # 0x1e01
            Increment                : Qt3DRender.QStencilOperationArguments.Operation = ... # 0x1e02
            Decrement                : Qt3DRender.QStencilOperationArguments.Operation = ... # 0x1e03
            IncrementWrap            : Qt3DRender.QStencilOperationArguments.Operation = ... # 0x8507
            DecrementWrap            : Qt3DRender.QStencilOperationArguments.Operation = ... # 0x8508


        def allTestsPassOperation(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilOperationArguments.Operation: ...
        def depthTestFailureOperation(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilOperationArguments.Operation: ...
        def faceMode(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilOperationArguments.FaceMode: ...
        def setAllTestsPassOperation(self, operation: PySide6.Qt3DRender.Qt3DRender.QStencilOperationArguments.Operation) -> None: ...
        def setDepthTestFailureOperation(self, operation: PySide6.Qt3DRender.Qt3DRender.QStencilOperationArguments.Operation) -> None: ...
        def setStencilTestFailureOperation(self, operation: PySide6.Qt3DRender.Qt3DRender.QStencilOperationArguments.Operation) -> None: ...
        def stencilTestFailureOperation(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilOperationArguments.Operation: ...

    class QStencilTest(PySide6.Qt3DRender.Qt3DRender.QRenderState):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def back(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilTestArguments: ...
        def front(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilTestArguments: ...

    class QStencilTestArguments(PySide6.QtCore.QObject):

        comparisonMaskChanged    : ClassVar[Signal] = ... # comparisonMaskChanged(uint)
        faceModeChanged          : ClassVar[Signal] = ... # faceModeChanged(StencilFaceMode)
        referenceValueChanged    : ClassVar[Signal] = ... # referenceValueChanged(int)
        stencilFunctionChanged   : ClassVar[Signal] = ... # stencilFunctionChanged(StencilFunction)

        class StencilFaceMode(enum.Enum):

            Front                    : Qt3DRender.QStencilTestArguments.StencilFaceMode = ... # 0x404
            Back                     : Qt3DRender.QStencilTestArguments.StencilFaceMode = ... # 0x405
            FrontAndBack             : Qt3DRender.QStencilTestArguments.StencilFaceMode = ... # 0x408


        class StencilFunction(enum.Enum):

            Never                    : Qt3DRender.QStencilTestArguments.StencilFunction = ... # 0x200
            Less                     : Qt3DRender.QStencilTestArguments.StencilFunction = ... # 0x201
            Equal                    : Qt3DRender.QStencilTestArguments.StencilFunction = ... # 0x202
            LessOrEqual              : Qt3DRender.QStencilTestArguments.StencilFunction = ... # 0x203
            Greater                  : Qt3DRender.QStencilTestArguments.StencilFunction = ... # 0x204
            NotEqual                 : Qt3DRender.QStencilTestArguments.StencilFunction = ... # 0x205
            GreaterOrEqual           : Qt3DRender.QStencilTestArguments.StencilFunction = ... # 0x206
            Always                   : Qt3DRender.QStencilTestArguments.StencilFunction = ... # 0x207


        def comparisonMask(self) -> int: ...
        def faceMode(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilTestArguments.StencilFaceMode: ...
        def referenceValue(self) -> int: ...
        def setComparisonMask(self, comparisonMask: int) -> None: ...
        def setReferenceValue(self, referenceValue: int) -> None: ...
        def setStencilFunction(self, stencilFunction: PySide6.Qt3DRender.Qt3DRender.QStencilTestArguments.StencilFunction) -> None: ...
        def stencilFunction(self) -> PySide6.Qt3DRender.Qt3DRender.QStencilTestArguments.StencilFunction: ...

    class QSubtreeEnabler(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        enablementChanged        : ClassVar[Signal] = ... # enablementChanged(Qt3DRender::QSubtreeEnabler::Enablement)

        class Enablement(enum.Enum):

            Persistent               : Qt3DRender.QSubtreeEnabler.Enablement = ... # 0x0
            SingleShot               : Qt3DRender.QSubtreeEnabler.Enablement = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def enablement(self) -> PySide6.Qt3DRender.Qt3DRender.QSubtreeEnabler.Enablement: ...
        def requestUpdate(self) -> None: ...
        def setEnablement(self, enablement: PySide6.Qt3DRender.Qt3DRender.QSubtreeEnabler.Enablement) -> None: ...

    class QTechnique(PySide6.Qt3DCore.Qt3DCore.QNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addFilterKey(self, filterKey: PySide6.Qt3DRender.Qt3DRender.QFilterKey) -> None: ...
        def addParameter(self, p: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def addRenderPass(self, pass_: PySide6.Qt3DRender.Qt3DRender.QRenderPass) -> None: ...
        def filterKeys(self) -> List[PySide6.Qt3DRender.Qt3DRender.QFilterKey]: ...
        def graphicsApiFilter(self) -> PySide6.Qt3DRender.Qt3DRender.QGraphicsApiFilter: ...
        def parameters(self) -> List[PySide6.Qt3DRender.Qt3DRender.QParameter]: ...
        def removeFilterKey(self, filterKey: PySide6.Qt3DRender.Qt3DRender.QFilterKey) -> None: ...
        def removeParameter(self, p: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def removeRenderPass(self, pass_: PySide6.Qt3DRender.Qt3DRender.QRenderPass) -> None: ...
        def renderPasses(self) -> List[PySide6.Qt3DRender.Qt3DRender.QRenderPass]: ...

    class QTechniqueFilter(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def addMatch(self, filterKey: PySide6.Qt3DRender.Qt3DRender.QFilterKey) -> None: ...
        def addParameter(self, p: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...
        def matchAll(self) -> List[PySide6.Qt3DRender.Qt3DRender.QFilterKey]: ...
        def parameters(self) -> List[PySide6.Qt3DRender.Qt3DRender.QParameter]: ...
        def removeMatch(self, filterKey: PySide6.Qt3DRender.Qt3DRender.QFilterKey) -> None: ...
        def removeParameter(self, p: PySide6.Qt3DRender.Qt3DRender.QParameter) -> None: ...

    class QTexture1D(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTexture1DArray(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTexture2D(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTexture2DArray(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTexture2DMultisample(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTexture2DMultisampleArray(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTexture3D(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTextureBuffer(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTextureCubeMap(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTextureCubeMapArray(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTextureData(Shiboken.Object):

        def __init__(self) -> None: ...

        def addImageData(self, imageData: Tuple[PySide6.Qt3DRender.Qt3DRender.QTextureImageData]) -> None: ...
        def comparisonFunction(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.ComparisonFunction: ...
        def comparisonMode(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.ComparisonMode: ...
        def depth(self) -> int: ...
        def format(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.TextureFormat: ...
        def height(self) -> int: ...
        def imageData(self) -> List[Tuple[PySide6.Qt3DRender.Qt3DRender.QTextureImageData]]: ...
        def isAutoMipMapGenerationEnabled(self) -> bool: ...
        def layers(self) -> int: ...
        def magnificationFilter(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Filter: ...
        def maximumAnisotropy(self) -> float: ...
        def minificationFilter(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Filter: ...
        def setAutoMipMapGenerationEnabled(self, isAutoMipMapGenerationEnabled: bool) -> None: ...
        def setComparisonFunction(self, comparisonFunction: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.ComparisonFunction) -> None: ...
        def setComparisonMode(self, comparisonMode: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.ComparisonMode) -> None: ...
        def setDepth(self, depth: int) -> None: ...
        def setFormat(self, arg__1: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.TextureFormat) -> None: ...
        def setHeight(self, height: int) -> None: ...
        def setLayers(self, layers: int) -> None: ...
        def setMagnificationFilter(self, filter: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Filter) -> None: ...
        def setMaximumAnisotropy(self, maximumAnisotropy: float) -> None: ...
        def setMinificationFilter(self, filter: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Filter) -> None: ...
        def setTarget(self, target: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Target) -> None: ...
        def setWidth(self, width: int) -> None: ...
        def setWrapModeX(self, wrapModeX: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode) -> None: ...
        def setWrapModeY(self, wrapModeY: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode) -> None: ...
        def setWrapModeZ(self, wrapModeZ: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode) -> None: ...
        def target(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.Target: ...
        def width(self) -> int: ...
        def wrapModeX(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode: ...
        def wrapModeY(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode: ...
        def wrapModeZ(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode: ...

    class QTextureDataUpdate(Shiboken.Object):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, other: PySide6.Qt3DRender.Qt3DRender.QTextureDataUpdate) -> None: ...

        @staticmethod
        def __copy__() -> None: ...
        def data(self) -> Tuple[PySide6.Qt3DRender.Qt3DRender.QTextureImageData]: ...
        def face(self) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.CubeMapFace: ...
        def layer(self) -> int: ...
        def mipLevel(self) -> int: ...
        def setData(self, data: Tuple[PySide6.Qt3DRender.Qt3DRender.QTextureImageData]) -> None: ...
        def setFace(self, face: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture.CubeMapFace) -> None: ...
        def setLayer(self, layer: int) -> None: ...
        def setMipLevel(self, mipLevel: int) -> None: ...
        def setX(self, x: int) -> None: ...
        def setY(self, y: int) -> None: ...
        def setZ(self, z: int) -> None: ...
        def swap(self, other: PySide6.Qt3DRender.Qt3DRender.QTextureDataUpdate) -> None: ...
        def x(self) -> int: ...
        def y(self) -> int: ...
        def z(self) -> int: ...

    class QTextureImage(PySide6.Qt3DRender.Qt3DRender.QAbstractTextureImage):

        mirroredChanged          : ClassVar[Signal] = ... # mirroredChanged(bool)
        sourceChanged            : ClassVar[Signal] = ... # sourceChanged(QUrl)
        statusChanged            : ClassVar[Signal] = ... # statusChanged(Status)

        class Status(enum.Enum):

            None_                    : Qt3DRender.QTextureImage.Status = ... # 0x0
            Loading                  : Qt3DRender.QTextureImage.Status = ... # 0x1
            Ready                    : Qt3DRender.QTextureImage.Status = ... # 0x2
            Error                    : Qt3DRender.QTextureImage.Status = ... # 0x3


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def dataGenerator(self) -> Tuple[PySide6.Qt3DRender.Qt3DRender.QTextureImageDataGenerator]: ...
        def isMirrored(self) -> bool: ...
        def setMirrored(self, mirrored: bool) -> None: ...
        def setSource(self, source: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def setStatus(self, status: PySide6.Qt3DRender.Qt3DRender.QTextureImage.Status) -> None: ...
        def source(self) -> PySide6.QtCore.QUrl: ...
        def status(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureImage.Status: ...

    class QTextureImageData(Shiboken.Object):

        def __init__(self) -> None: ...

        def alignment(self) -> int: ...
        def cleanup(self) -> None: ...
        def data(self, layer: int = ..., face: int = ..., mipmapLevel: int = ...) -> PySide6.QtCore.QByteArray: ...
        def depth(self) -> int: ...
        def faces(self) -> int: ...
        def height(self) -> int: ...
        def isCompressed(self) -> bool: ...
        def layers(self) -> int: ...
        def mipLevels(self) -> int: ...
        def setAlignment(self, alignment: int) -> None: ...
        def setData(self, data: Union[PySide6.QtCore.QByteArray, bytes], blockSize: int, isCompressed: bool = ...) -> None: ...
        def setDepth(self, depth: int) -> None: ...
        def setFaces(self, faces: int) -> None: ...
        def setHeight(self, height: int) -> None: ...
        def setImage(self, arg__1: Union[PySide6.QtGui.QImage, str]) -> None: ...
        def setLayers(self, layers: int) -> None: ...
        def setMipLevels(self, mipLevels: int) -> None: ...
        def setWidth(self, width: int) -> None: ...
        def width(self) -> int: ...

    class QTextureImageDataGenerator(PySide6.Qt3DCore.Qt3DCore.QAbstractFunctor):
        def __call__(self) -> Tuple[PySide6.Qt3DRender.Qt3DRender.QTextureImageData]: ...

    class QTextureImageDataGeneratorPtr(Shiboken.Object):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, pointee: PySide6.Qt3DRender.Qt3DRender.QTextureImageDataGenerator) -> None: ...

        @staticmethod
        def __copy__() -> None: ...
        def data(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureImageDataGenerator: ...
        @overload
        def reset(self) -> None: ...
        @overload
        def reset(self, t: PySide6.Qt3DRender.Qt3DRender.QTextureImageDataGenerator) -> None: ...

    class QTextureImageDataPtr(Shiboken.Object):

        @overload
        def __init__(self) -> None: ...
        @overload
        def __init__(self, pointee: PySide6.Qt3DRender.Qt3DRender.QTextureImageData) -> None: ...

        @staticmethod
        def __copy__() -> None: ...
        def data(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureImageData: ...
        @overload
        def reset(self) -> None: ...
        @overload
        def reset(self, t: PySide6.Qt3DRender.Qt3DRender.QTextureImageData) -> None: ...

    class QTextureLoader(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        mirroredChanged          : ClassVar[Signal] = ... # mirroredChanged(bool)
        sourceChanged            : ClassVar[Signal] = ... # sourceChanged(QUrl)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def isMirrored(self) -> bool: ...
        def setMirrored(self, mirrored: bool) -> None: ...
        def setSource(self, source: Union[PySide6.QtCore.QUrl, str]) -> None: ...
        def source(self) -> PySide6.QtCore.QUrl: ...

    class QTextureRectangle(PySide6.Qt3DRender.Qt3DRender.QAbstractTexture):

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...


    class QTextureWrapMode(PySide6.QtCore.QObject):

        xChanged                 : ClassVar[Signal] = ... # xChanged(WrapMode)
        yChanged                 : ClassVar[Signal] = ... # yChanged(WrapMode)
        zChanged                 : ClassVar[Signal] = ... # zChanged(WrapMode)

        class WrapMode(enum.Enum):

            Repeat                   : Qt3DRender.QTextureWrapMode.WrapMode = ... # 0x2901
            ClampToBorder            : Qt3DRender.QTextureWrapMode.WrapMode = ... # 0x812d
            ClampToEdge              : Qt3DRender.QTextureWrapMode.WrapMode = ... # 0x812f
            MirroredRepeat           : Qt3DRender.QTextureWrapMode.WrapMode = ... # 0x8370


        @overload
        def __init__(self, wrapMode: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode = ..., parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...
        @overload
        def __init__(self, x: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode, y: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode, z: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode, parent: Optional[PySide6.QtCore.QObject] = ...) -> None: ...

        def setX(self, x: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode) -> None: ...
        def setY(self, y: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode) -> None: ...
        def setZ(self, z: PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode) -> None: ...
        def x(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode: ...
        def y(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode: ...
        def z(self) -> PySide6.Qt3DRender.Qt3DRender.QTextureWrapMode.WrapMode: ...

    class QViewport(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        gammaChanged             : ClassVar[Signal] = ... # gammaChanged(float)
        normalizedRectChanged    : ClassVar[Signal] = ... # normalizedRectChanged(QRectF)

        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def gamma(self) -> float: ...
        def normalizedRect(self) -> PySide6.QtCore.QRectF: ...
        def setGamma(self, gamma: float) -> None: ...
        def setNormalizedRect(self, normalizedRect: Union[PySide6.QtCore.QRectF, PySide6.QtCore.QRect]) -> None: ...

    class QWaitFence(PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode):

        handleChanged            : ClassVar[Signal] = ... # handleChanged(QVariant)
        handleTypeChanged        : ClassVar[Signal] = ... # handleTypeChanged(HandleType)
        timeoutChanged           : ClassVar[Signal] = ... # timeoutChanged(qulonglong)
        waitOnCPUChanged         : ClassVar[Signal] = ... # waitOnCPUChanged(bool)

        class HandleType(enum.Enum):

            NoHandle                 : Qt3DRender.QWaitFence.HandleType = ... # 0x0
            OpenGLFenceId            : Qt3DRender.QWaitFence.HandleType = ... # 0x1


        def __init__(self, parent: Optional[PySide6.Qt3DCore.Qt3DCore.QNode] = ...) -> None: ...

        def handle(self) -> Any: ...
        def handleType(self) -> PySide6.Qt3DRender.Qt3DRender.QWaitFence.HandleType: ...
        def setHandle(self, handle: Any) -> None: ...
        def setHandleType(self, type: PySide6.Qt3DRender.Qt3DRender.QWaitFence.HandleType) -> None: ...
        def setTimeout(self, timeout: int) -> None: ...
        def setWaitOnCPU(self, waitOnCPU: bool) -> None: ...
        def timeout(self) -> int: ...
        def waitOnCPU(self) -> bool: ...


    @staticmethod
    def swap(lhs: PySide6.Qt3DRender.Qt3DRender.QTextureDataUpdate, rhs: PySide6.Qt3DRender.Qt3DRender.QTextureDataUpdate) -> None: ...


# eof
