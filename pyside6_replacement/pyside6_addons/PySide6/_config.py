built_modules = list(name for name in
    "Core;Gui;Widgets;PrintSupport;Sql;Network;Test;Concurrent;DBus;Designer;Xml;Help;Multimedia;MultimediaWidgets;OpenGL;OpenGLWidgets;Pdf;PdfWidgets;Positioning;Location;NetworkAuth;Nfc;Qml;Quick;Quick3D;QuickControls2;QuickWidgets;RemoteObjects;Scxml;Sensors;SerialPort;SerialBus;StateMachine;TextToSpeech;Charts;SpatialAudio;Svg;SvgWidgets;DataVisualization;Bluetooth;UiTools;WebChannel;WebEngineCore;WebEngineWidgets;WebEngineQuick;WebSockets;HttpServer;3DCore;3DRender;3DInput;3DLogic;3DAnimation;3DExtras"
    .split(";"))

shiboken_library_soversion = str(6.5)
pyside_library_soversion = str(6.5)

version = "6.5.3"
version_info = (6, 5, 3, "", "")

__build_date__ = '2023-09-29T16:26:06+00:00'




__setup_py_package_version__ = '6.5.3'
__qt_macos_min_deployment_target__ = '11.0'
