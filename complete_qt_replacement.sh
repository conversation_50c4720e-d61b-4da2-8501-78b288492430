#!/bin/bash

# 完整的 Qt 6.5.3 替换脚本
# 系统性地替换 YCursor 中的 Qt WebEngine 组件

echo "=== YCursor Qt 6.5.3 完整替换脚本 ==="

# 恢复备份
echo "恢复原始备份..."
rm -rf /Applications/YCursor.app
cp -r /Applications/YCursor_backup.app /Applications/YCursor.app

# 设置路径
PYSIDE6_653_ESSENTIALS="pyside6_replacement/pyside6_essentials/PySide6"
PYSIDE6_653_ADDONS="pyside6_replacement/pyside6_addons/PySide6"
SHIBOKEN6_653="pyside6_replacement/shiboken6/shiboken6"
YCURSOR_PYSIDE6="/Applications/YCursor.app/Contents/MacOS/PySide6"
YCURSOR_MACOS="/Applications/YCursor.app/Contents/MacOS"

echo "开始替换 Qt 组件..."

# 1. 替换核心 Qt 框架
echo "替换 QtWebEngineCore..."
cp "$PYSIDE6_653_ADDONS/Qt/lib/QtWebEngineCore.framework/Versions/A/QtWebEngineCore" "$YCURSOR_MACOS/QtWebEngineCore"

echo "替换 QtWebEngineWidgets..."
cp "$PYSIDE6_653_ADDONS/Qt/lib/QtWebEngineWidgets.framework/Versions/A/QtWebEngineWidgets" "$YCURSOR_MACOS/QtWebEngineWidgets"

# 2. 替换 PySide6 Python 绑定
echo "替换 PySide6 Python 绑定..."
cp "$PYSIDE6_653_ADDONS/QtWebEngineCore.abi3.so" "$YCURSOR_PYSIDE6/QtWebEngineCore.so"
cp "$PYSIDE6_653_ADDONS/QtWebEngineWidgets.abi3.so" "$YCURSOR_PYSIDE6/QtWebEngineWidgets.so"

# 3. 替换相关的核心库
echo "替换核心库..."
cp "$PYSIDE6_653_ESSENTIALS/QtCore.abi3.so" "$YCURSOR_PYSIDE6/QtCore.so"
cp "$PYSIDE6_653_ESSENTIALS/QtGui.abi3.so" "$YCURSOR_PYSIDE6/QtGui.so"
cp "$PYSIDE6_653_ESSENTIALS/QtWidgets.abi3.so" "$YCURSOR_PYSIDE6/QtWidgets.so"

# 4. 替换动态库
echo "替换动态库..."
find "$PYSIDE6_653_ESSENTIALS" -name "*.dylib" -exec cp {} "$YCURSOR_PYSIDE6/" \;
find "$PYSIDE6_653_ADDONS" -name "*.dylib" -exec cp {} "$YCURSOR_PYSIDE6/" \;
find "$SHIBOKEN6_653" -name "*.dylib" -exec cp {} "$YCURSOR_PYSIDE6/" \;

# 5. 替换 Qt 框架库
echo "替换其他 Qt 框架..."
if [ -d "$PYSIDE6_653_ESSENTIALS/Qt/lib/QtCore.framework" ]; then
    cp "$PYSIDE6_653_ESSENTIALS/Qt/lib/QtCore.framework/Versions/A/QtCore" "$YCURSOR_MACOS/QtCore" 2>/dev/null || true
fi

if [ -d "$PYSIDE6_653_ESSENTIALS/Qt/lib/QtGui.framework" ]; then
    cp "$PYSIDE6_653_ESSENTIALS/Qt/lib/QtGui.framework/Versions/A/QtGui" "$YCURSOR_MACOS/QtGui" 2>/dev/null || true
fi

if [ -d "$PYSIDE6_653_ESSENTIALS/Qt/lib/QtWidgets.framework" ]; then
    cp "$PYSIDE6_653_ESSENTIALS/Qt/lib/QtWidgets.framework/Versions/A/QtWidgets" "$YCURSOR_MACOS/QtWidgets" 2>/dev/null || true
fi

echo "替换完成！"
echo "正在测试 YCursor..."

# 测试启动
/Applications/YCursor.app/Contents/MacOS/YCursor
